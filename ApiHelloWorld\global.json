﻿{
  "version": "0.2.0",
  "configurations": [
    {
      "type": "coreclr",
      "request": "launch",
      "name": "Launch .NET Core App",
      "program": "${workspaceFolder}/src/ApiHelloWorld/bin/Debug/netcoreapp1.0/ApiHelloWorld.dll",
      "preLaunchTask": "build",
      "cwd": "${workspaceFolder}/src/ApiHelloWorld",
      "stopAtEntry": false
    }
  ],
  "projects": [ "src", "test" ],
  "sdk": {
    "version": "1.0.0-preview2-003121"
  }
}
