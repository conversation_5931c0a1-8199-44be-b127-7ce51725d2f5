{"runtimeTarget": {"name": ".NETCoreApp,Version=v1.0", "signature": "be35e27118c847435190a4e977d5b0b9a55404e1"}, "compilationOptions": {"defines": ["DEBUG", "TRACE", "NETCOREAPP1_0"], "optimize": false, "emitEntryPoint": true}, "targets": {".NETCoreApp,Version=v1.0": {"ApiHelloWorld/1.0.0": {"dependencies": {"Microsoft.NETCore.App": "1.0.0", "Microsoft.AspNetCore.Mvc": "1.0.0", "Microsoft.AspNetCore.Server.IISIntegration": "1.0.0", "Microsoft.AspNetCore.Server.Kestrel": "1.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "1.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "1.0.0", "Microsoft.Extensions.Configuration.Json": "1.0.0", "Microsoft.Extensions.Logging": "1.0.0", "Microsoft.Extensions.Logging.Console": "1.0.0", "Microsoft.Extensions.Logging.Debug": "1.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "1.0.0"}, "runtime": {"ApiHelloWorld.dll": {}}, "compile": {"ApiHelloWorld.dll": {}}}, "Microsoft.AspNetCore.Antiforgery/1.0.0": {"dependencies": {"Microsoft.AspNetCore.DataProtection": "1.0.0", "Microsoft.AspNetCore.Http.Abstractions": "1.0.0", "Microsoft.AspNetCore.WebUtilities": "1.0.0", "Microsoft.Extensions.ObjectPool": "1.0.0"}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.Antiforgery.dll": {}}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.Antiforgery.dll": {}}}, "Microsoft.AspNetCore.Authorization/1.0.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "1.0.0", "Microsoft.Extensions.Options": "1.0.0", "System.Security.Claims": "4.0.1"}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.Authorization.dll": {}}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.Authorization.dll": {}}}, "Microsoft.AspNetCore.Cors/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "1.0.0", "Microsoft.Extensions.Configuration.Abstractions": "1.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "1.0.0", "Microsoft.Extensions.Options": "1.0.0"}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.Cors.dll": {}}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.Cors.dll": {}}}, "Microsoft.AspNetCore.Cryptography.Internal/1.0.0": {"dependencies": {"System.Diagnostics.Debug": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Threading": "4.0.11"}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.Cryptography.Internal.dll": {}}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.Cryptography.Internal.dll": {}}}, "Microsoft.AspNetCore.DataProtection/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "1.0.0", "Microsoft.AspNetCore.DataProtection.Abstractions": "1.0.0", "Microsoft.AspNetCore.Hosting.Abstractions": "1.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "1.0.0", "Microsoft.Extensions.Logging.Abstractions": "1.0.0", "Microsoft.Extensions.Options": "1.0.0", "Microsoft.Win32.Registry": "4.0.0", "System.IO.FileSystem": "4.0.1", "System.Reflection.Extensions": "4.0.1", "System.Security.Claims": "4.0.1", "System.Security.Cryptography.X509Certificates": "4.1.0", "System.Security.Principal.Windows": "4.0.0", "System.Xml.XDocument": "4.0.11"}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.DataProtection.dll": {}}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.DataProtection.dll": {}}}, "Microsoft.AspNetCore.DataProtection.Abstractions/1.0.0": {"dependencies": {"System.ComponentModel": "4.0.1", "System.Diagnostics.Debug": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime.Extensions": "4.1.0"}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.DataProtection.Abstractions.dll": {}}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.DataProtection.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Diagnostics.Abstractions/1.0.0": {"dependencies": {"System.Resources.ResourceManager": "4.0.1"}, "runtime": {"lib/netstandard1.0/Microsoft.AspNetCore.Diagnostics.Abstractions.dll": {}}, "compile": {"lib/netstandard1.0/Microsoft.AspNetCore.Diagnostics.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Hosting/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "1.0.0", "Microsoft.AspNetCore.Hosting.Server.Abstractions": "1.0.0", "Microsoft.AspNetCore.Http": "1.0.0", "Microsoft.AspNetCore.Http.Extensions": "1.0.0", "Microsoft.Extensions.Configuration": "1.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "1.0.0", "Microsoft.Extensions.DependencyInjection": "1.0.0", "Microsoft.Extensions.FileProviders.Physical": "1.0.0", "Microsoft.Extensions.Logging": "1.0.0", "Microsoft.Extensions.Options": "1.0.0", "Microsoft.Extensions.PlatformAbstractions": "1.0.0", "System.Console": "4.0.0", "System.Diagnostics.DiagnosticSource": "4.0.0", "System.Diagnostics.StackTrace": "4.0.1", "System.Reflection.Extensions": "4.0.1", "System.Reflection.Metadata": "1.3.0", "System.Runtime.InteropServices.RuntimeInformation": "4.0.0"}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.Hosting.dll": {}}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.Hosting.dll": {}}}, "Microsoft.AspNetCore.Hosting.Abstractions/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Server.Abstractions": "1.0.0", "Microsoft.AspNetCore.Http.Abstractions": "1.0.0", "Microsoft.Extensions.Configuration.Abstractions": "1.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "1.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "1.0.0", "Microsoft.Extensions.Logging.Abstractions": "1.0.0"}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.Hosting.Abstractions.dll": {}}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.Hosting.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "1.0.0", "Microsoft.Extensions.Configuration.Abstractions": "1.0.0"}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.Hosting.Server.Abstractions.dll": {}}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.Hosting.Server.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Html.Abstractions/1.0.0": {"dependencies": {"System.Collections": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Text.Encodings.Web": "4.0.0"}, "runtime": {"lib/netstandard1.0/Microsoft.AspNetCore.Html.Abstractions.dll": {}}, "compile": {"lib/netstandard1.0/Microsoft.AspNetCore.Html.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Http/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "1.0.0", "Microsoft.AspNetCore.WebUtilities": "1.0.0", "Microsoft.Extensions.ObjectPool": "1.0.0", "Microsoft.Extensions.Options": "1.0.0", "Microsoft.Net.Http.Headers": "1.0.0", "System.Buffers": "4.0.0", "System.Threading": "4.0.11"}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.Http.dll": {}}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.Http.dll": {}}}, "Microsoft.AspNetCore.Http.Abstractions/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "1.0.0", "System.Globalization.Extensions": "4.0.1", "System.Linq.Expressions": "4.1.0", "System.Reflection.TypeExtensions": "4.1.0", "System.Runtime.InteropServices": "4.1.0", "System.Text.Encodings.Web": "4.0.0"}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.Http.Abstractions.dll": {}}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.Http.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Http.Extensions/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "1.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "1.0.0", "Microsoft.Net.Http.Headers": "1.0.0", "System.Buffers": "4.0.0", "System.IO.FileSystem": "4.0.1"}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.Http.Extensions.dll": {}}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.Http.Extensions.dll": {}}}, "Microsoft.AspNetCore.Http.Features/1.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "1.0.0", "System.Collections": "4.0.11", "System.ComponentModel": "4.0.1", "System.Linq": "4.1.0", "System.Net.Primitives": "4.0.11", "System.Net.WebSockets": "4.0.0", "System.Runtime.Extensions": "4.1.0", "System.Security.Claims": "4.0.1", "System.Security.Cryptography.X509Certificates": "4.1.0", "System.Security.Principal": "4.0.1"}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.Http.Features.dll": {}}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.Http.Features.dll": {}}}, "Microsoft.AspNetCore.HttpOverrides/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "1.0.0", "Microsoft.Extensions.Logging.Abstractions": "1.0.0", "Microsoft.Extensions.Options": "1.0.0"}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.HttpOverrides.dll": {}}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.HttpOverrides.dll": {}}}, "Microsoft.AspNetCore.JsonPatch/1.0.0": {"dependencies": {"Microsoft.CSharp": "4.0.1", "Newtonsoft.Json": "9.0.1", "System.Collections.Concurrent": "4.0.12", "System.ComponentModel.TypeConverter": "4.1.0", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.Linq": "4.1.0", "System.Reflection.Extensions": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Serialization.Primitives": "4.1.1", "System.Text.Encoding.Extensions": "4.0.11"}, "runtime": {"lib/netstandard1.1/Microsoft.AspNetCore.JsonPatch.dll": {}}, "compile": {"lib/netstandard1.1/Microsoft.AspNetCore.JsonPatch.dll": {}}}, "Microsoft.AspNetCore.Localization/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "1.0.0", "Microsoft.Extensions.Globalization.CultureInfoCache": "1.0.0", "Microsoft.Extensions.Localization.Abstractions": "1.0.0", "Microsoft.Extensions.Options": "1.0.0"}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.Localization.dll": {}}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.Localization.dll": {}}}, "Microsoft.AspNetCore.Mvc/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.ApiExplorer": "1.0.0", "Microsoft.AspNetCore.Mvc.Cors": "1.0.0", "Microsoft.AspNetCore.Mvc.DataAnnotations": "1.0.0", "Microsoft.AspNetCore.Mvc.Formatters.Json": "1.0.0", "Microsoft.AspNetCore.Mvc.Localization": "1.0.0", "Microsoft.AspNetCore.Mvc.Razor": "1.0.0", "Microsoft.AspNetCore.Mvc.TagHelpers": "1.0.0", "Microsoft.AspNetCore.Mvc.ViewFeatures": "1.0.0", "Microsoft.Extensions.Caching.Memory": "1.0.0", "Microsoft.Extensions.DependencyInjection": "1.0.0"}, "runtime": {"lib/netstandard1.6/Microsoft.AspNetCore.Mvc.dll": {}}, "compile": {"lib/netstandard1.6/Microsoft.AspNetCore.Mvc.dll": {}}}, "Microsoft.AspNetCore.Mvc.Abstractions/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Routing.Abstractions": "1.0.0", "Microsoft.CSharp": "4.0.1", "Microsoft.Net.Http.Headers": "1.0.0", "System.ComponentModel.TypeConverter": "4.1.0", "System.Reflection.Extensions": "4.0.1", "System.Text.Encoding.Extensions": "4.0.11"}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.Mvc.Abstractions.dll": {}}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.Mvc.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Mvc.ApiExplorer/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.Core": "1.0.0"}, "runtime": {"lib/netstandard1.6/Microsoft.AspNetCore.Mvc.ApiExplorer.dll": {}}, "compile": {"lib/netstandard1.6/Microsoft.AspNetCore.Mvc.ApiExplorer.dll": {}}}, "Microsoft.AspNetCore.Mvc.Core/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Authorization": "1.0.0", "Microsoft.AspNetCore.Hosting.Abstractions": "1.0.0", "Microsoft.AspNetCore.Http": "1.0.0", "Microsoft.AspNetCore.Mvc.Abstractions": "1.0.0", "Microsoft.AspNetCore.Routing": "1.0.0", "Microsoft.Extensions.DependencyModel": "1.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "1.0.0", "Microsoft.Extensions.Logging.Abstractions": "1.0.0", "Microsoft.Extensions.PlatformAbstractions": "1.0.0", "System.Buffers": "4.0.0", "System.Diagnostics.DiagnosticSource": "4.0.0", "System.Text.Encoding": "4.0.11"}, "runtime": {"lib/netstandard1.6/Microsoft.AspNetCore.Mvc.Core.dll": {}}, "compile": {"lib/netstandard1.6/Microsoft.AspNetCore.Mvc.Core.dll": {}}}, "Microsoft.AspNetCore.Mvc.Cors/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Cors": "1.0.0", "Microsoft.AspNetCore.Mvc.Core": "1.0.0"}, "runtime": {"lib/netstandard1.6/Microsoft.AspNetCore.Mvc.Cors.dll": {}}, "compile": {"lib/netstandard1.6/Microsoft.AspNetCore.Mvc.Cors.dll": {}}}, "Microsoft.AspNetCore.Mvc.DataAnnotations/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.Core": "1.0.0", "Microsoft.Extensions.Localization": "1.0.0", "System.ComponentModel.Annotations": "4.1.0"}, "runtime": {"lib/netstandard1.6/Microsoft.AspNetCore.Mvc.DataAnnotations.dll": {}}, "compile": {"lib/netstandard1.6/Microsoft.AspNetCore.Mvc.DataAnnotations.dll": {}}}, "Microsoft.AspNetCore.Mvc.Formatters.Json/1.0.0": {"dependencies": {"Microsoft.AspNetCore.JsonPatch": "1.0.0", "Microsoft.AspNetCore.Mvc.Core": "1.0.0"}, "runtime": {"lib/netstandard1.6/Microsoft.AspNetCore.Mvc.Formatters.Json.dll": {}}, "compile": {"lib/netstandard1.6/Microsoft.AspNetCore.Mvc.Formatters.Json.dll": {}}}, "Microsoft.AspNetCore.Mvc.Localization/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Localization": "1.0.0", "Microsoft.AspNetCore.Mvc.Razor": "1.0.0", "Microsoft.Extensions.DependencyInjection": "1.0.0", "Microsoft.Extensions.Localization": "1.0.0"}, "runtime": {"lib/netstandard1.6/Microsoft.AspNetCore.Mvc.Localization.dll": {}}, "compile": {"lib/netstandard1.6/Microsoft.AspNetCore.Mvc.Localization.dll": {}}}, "Microsoft.AspNetCore.Mvc.Razor/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.Razor.Host": "1.0.0", "Microsoft.AspNetCore.Mvc.ViewFeatures": "1.0.0", "Microsoft.CodeAnalysis.CSharp": "1.3.0", "Microsoft.Extensions.FileProviders.Composite": "1.0.0", "System.Runtime.Loader": "4.0.0", "System.Text.Encoding": "4.0.11"}, "runtime": {"lib/netstandard1.6/Microsoft.AspNetCore.Mvc.Razor.dll": {}}, "compile": {"lib/netstandard1.6/Microsoft.AspNetCore.Mvc.Razor.dll": {}}}, "Microsoft.AspNetCore.Mvc.Razor.Host/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Razor.Runtime": "1.0.0", "Microsoft.Extensions.Caching.Memory": "1.0.0", "Microsoft.Extensions.FileProviders.Physical": "1.0.0", "System.Collections.Concurrent": "4.0.12", "System.ComponentModel.TypeConverter": "4.1.0", "System.Reflection.Extensions": "4.0.1", "System.Text.Encoding.Extensions": "4.0.11"}, "runtime": {"lib/netstandard1.6/Microsoft.AspNetCore.Mvc.Razor.Host.dll": {}}, "compile": {"lib/netstandard1.6/Microsoft.AspNetCore.Mvc.Razor.Host.dll": {}}}, "Microsoft.AspNetCore.Mvc.TagHelpers/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Mvc.Razor": "1.0.0", "Microsoft.Extensions.Caching.Memory": "1.0.0", "Microsoft.Extensions.FileSystemGlobbing": "1.0.0", "Microsoft.Extensions.Primitives": "1.0.0"}, "runtime": {"lib/netstandard1.6/Microsoft.AspNetCore.Mvc.TagHelpers.dll": {}}, "compile": {"lib/netstandard1.6/Microsoft.AspNetCore.Mvc.TagHelpers.dll": {}}}, "Microsoft.AspNetCore.Mvc.ViewFeatures/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Antiforgery": "1.0.0", "Microsoft.AspNetCore.Diagnostics.Abstractions": "1.0.0", "Microsoft.AspNetCore.Html.Abstractions": "1.0.0", "Microsoft.AspNetCore.Mvc.Core": "1.0.0", "Microsoft.AspNetCore.Mvc.DataAnnotations": "1.0.0", "Microsoft.AspNetCore.Mvc.Formatters.Json": "1.0.0", "Microsoft.Extensions.WebEncoders": "1.0.0", "Newtonsoft.Json": "9.0.1", "System.Buffers": "4.0.0", "System.Runtime.Serialization.Primitives": "4.1.1"}, "runtime": {"lib/netstandard1.6/Microsoft.AspNetCore.Mvc.ViewFeatures.dll": {}}, "compile": {"lib/netstandard1.6/Microsoft.AspNetCore.Mvc.ViewFeatures.dll": {}}}, "Microsoft.AspNetCore.Razor/1.0.0": {"dependencies": {"System.Diagnostics.Debug": "4.0.11", "System.Linq": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime.Extensions": "4.1.0", "System.Security.Cryptography.Algorithms": "4.2.0", "System.Threading": "4.0.11", "System.Threading.Thread": "4.0.0"}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.Razor.dll": {}}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.Razor.dll": {}}}, "Microsoft.AspNetCore.Razor.Runtime/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Html.Abstractions": "1.0.0", "Microsoft.AspNetCore.Razor": "1.0.0", "System.Collections.Concurrent": "4.0.12", "System.IO.FileSystem": "4.0.1", "System.Reflection": "4.1.0", "System.Reflection.Extensions": "4.0.1", "System.Reflection.TypeExtensions": "4.1.0", "System.Text.RegularExpressions": "4.1.0", "System.Xml.XDocument": "4.0.11"}, "runtime": {"lib/netstandard1.5/Microsoft.AspNetCore.Razor.Runtime.dll": {}}, "compile": {"lib/netstandard1.5/Microsoft.AspNetCore.Razor.Runtime.dll": {}}}, "Microsoft.AspNetCore.Routing/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "1.0.0", "Microsoft.AspNetCore.Routing.Abstractions": "1.0.0", "Microsoft.Extensions.Logging.Abstractions": "1.0.0", "Microsoft.Extensions.ObjectPool": "1.0.0", "Microsoft.Extensions.Options": "1.0.0", "System.Collections": "4.0.11", "System.Text.RegularExpressions": "4.1.0"}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.Routing.dll": {}}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.Routing.dll": {}}}, "Microsoft.AspNetCore.Routing.Abstractions/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "1.0.0", "System.Collections.Concurrent": "4.0.12", "System.Reflection.Extensions": "4.0.1", "System.Threading.Tasks": "4.0.11"}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.Routing.Abstractions.dll": {}}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.Routing.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Server.IISIntegration/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "1.0.0", "Microsoft.AspNetCore.Http": "1.0.0", "Microsoft.AspNetCore.Http.Extensions": "1.0.0", "Microsoft.AspNetCore.HttpOverrides": "1.0.0", "Microsoft.Extensions.Logging.Abstractions": "1.0.0", "Microsoft.Extensions.Options": "1.0.0", "System.Security.Principal.Windows": "4.0.0"}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.Server.IISIntegration.dll": {}}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.Server.IISIntegration.dll": {}}}, "Microsoft.AspNetCore.Server.Kestrel/1.0.0": {"dependencies": {"Libuv": "1.9.0", "Microsoft.AspNetCore.Hosting": "1.0.0", "Microsoft.Extensions.Logging.Abstractions": "1.0.0", "System.Buffers": "4.0.0", "System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.Linq": "4.1.0", "System.Net.Primitives": "4.0.11", "System.Numerics.Vectors": "4.1.1", "System.Runtime.Extensions": "4.1.0", "System.Runtime.InteropServices": "4.1.0", "System.Runtime.InteropServices.RuntimeInformation": "4.0.0", "System.Text.Encoding": "4.0.11", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.0.11", "System.Threading.Tasks.Extensions": "4.0.0", "System.Threading.Thread": "4.0.0", "System.Threading.ThreadPool": "4.0.10", "System.Threading.Timer": "4.0.1"}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.Server.Kestrel.dll": {}}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.Server.Kestrel.dll": {}}}, "Microsoft.AspNetCore.WebUtilities/1.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "1.0.0", "System.Buffers": "4.0.0", "System.Collections": "4.0.11", "System.IO": "4.1.0", "System.IO.FileSystem": "4.0.1", "System.Text.Encodings.Web": "4.0.0"}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.WebUtilities.dll": {}}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.WebUtilities.dll": {}}}, "Microsoft.DotNet.InternalAbstractions/1.0.0": {"dependencies": {"System.AppContext": "4.1.0", "System.Collections": "4.0.11", "System.IO": "4.1.0", "System.IO.FileSystem": "4.0.1", "System.Reflection.TypeExtensions": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.InteropServices": "4.1.0", "System.Runtime.InteropServices.RuntimeInformation": "4.0.0"}, "runtime": {"lib/netstandard1.3/Microsoft.DotNet.InternalAbstractions.dll": {}}, "compile": {"lib/netstandard1.3/Microsoft.DotNet.InternalAbstractions.dll": {}}}, "Microsoft.Extensions.Caching.Abstractions/1.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "1.0.0", "System.Collections": "4.0.11", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.0.11"}, "runtime": {"lib/netstandard1.0/Microsoft.Extensions.Caching.Abstractions.dll": {}}, "compile": {"lib/netstandard1.0/Microsoft.Extensions.Caching.Abstractions.dll": {}}}, "Microsoft.Extensions.Caching.Memory/1.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "1.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "1.0.0", "Microsoft.Extensions.Options": "1.0.0", "System.Linq": "4.1.0", "System.Threading": "4.0.11"}, "runtime": {"lib/netstandard1.3/Microsoft.Extensions.Caching.Memory.dll": {}}, "compile": {"lib/netstandard1.3/Microsoft.Extensions.Caching.Memory.dll": {}}}, "Microsoft.Extensions.Configuration/1.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "1.0.0", "System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.IO": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime.Extensions": "4.1.0", "System.Runtime.InteropServices": "4.1.0", "System.Threading": "4.0.11"}, "runtime": {"lib/netstandard1.1/Microsoft.Extensions.Configuration.dll": {}}, "compile": {"lib/netstandard1.1/Microsoft.Extensions.Configuration.dll": {}}}, "Microsoft.Extensions.Configuration.Abstractions/1.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "1.0.0", "System.Linq": "4.1.0"}, "runtime": {"lib/netstandard1.0/Microsoft.Extensions.Configuration.Abstractions.dll": {}}, "compile": {"lib/netstandard1.0/Microsoft.Extensions.Configuration.Abstractions.dll": {}}}, "Microsoft.Extensions.Configuration.Binder/1.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "1.0.0", "System.ComponentModel.TypeConverter": "4.1.0"}, "runtime": {"lib/netstandard1.1/Microsoft.Extensions.Configuration.Binder.dll": {}}, "compile": {"lib/netstandard1.1/Microsoft.Extensions.Configuration.Binder.dll": {}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/1.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "1.0.0"}, "runtime": {"lib/netstandard1.3/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {}}, "compile": {"lib/netstandard1.3/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {}}}, "Microsoft.Extensions.Configuration.FileExtensions/1.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "1.0.0", "Microsoft.Extensions.FileProviders.Physical": "1.0.0", "System.AppContext": "4.1.0"}, "runtime": {"lib/netstandard1.3/Microsoft.Extensions.Configuration.FileExtensions.dll": {}}, "compile": {"lib/netstandard1.3/Microsoft.Extensions.Configuration.FileExtensions.dll": {}}}, "Microsoft.Extensions.Configuration.Json/1.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "1.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "1.0.0", "Newtonsoft.Json": "9.0.1", "System.Dynamic.Runtime": "4.0.11", "System.IO.FileSystem": "4.0.1", "System.Runtime.Serialization.Primitives": "4.1.1"}, "runtime": {"lib/netstandard1.3/Microsoft.Extensions.Configuration.Json.dll": {}}, "compile": {"lib/netstandard1.3/Microsoft.Extensions.Configuration.Json.dll": {}}}, "Microsoft.Extensions.DependencyInjection/1.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "1.0.0", "System.Collections": "4.0.11", "System.Collections.Concurrent": "4.0.12", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.0.11"}, "runtime": {"lib/netstandard1.1/Microsoft.Extensions.DependencyInjection.dll": {}}, "compile": {"lib/netstandard1.1/Microsoft.Extensions.DependencyInjection.dll": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/1.0.0": {"dependencies": {"System.ComponentModel": "4.0.1", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.Linq": "4.1.0", "System.Linq.Expressions": "4.1.0", "System.Reflection": "4.1.0", "System.Resources.ResourceManager": "4.0.1"}, "runtime": {"lib/netstandard1.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {}}, "compile": {"lib/netstandard1.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {}}}, "Microsoft.Extensions.DependencyModel/1.0.0": {"dependencies": {"Microsoft.DotNet.InternalAbstractions": "1.0.0", "Newtonsoft.Json": "9.0.1", "System.Diagnostics.Debug": "4.0.11", "System.Dynamic.Runtime": "4.0.11", "System.Linq": "4.1.0"}, "runtime": {"lib/netstandard1.6/Microsoft.Extensions.DependencyModel.dll": {}}, "compile": {"lib/netstandard1.6/Microsoft.Extensions.DependencyModel.dll": {}}}, "Microsoft.Extensions.FileProviders.Abstractions/1.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "1.0.0", "System.IO": "4.1.0", "System.Resources.ResourceManager": "4.0.1"}, "runtime": {"lib/netstandard1.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {}}, "compile": {"lib/netstandard1.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {}}}, "Microsoft.Extensions.FileProviders.Composite/1.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "1.0.0"}, "runtime": {"lib/netstandard1.0/Microsoft.Extensions.FileProviders.Composite.dll": {}}, "compile": {"lib/netstandard1.0/Microsoft.Extensions.FileProviders.Composite.dll": {}}}, "Microsoft.Extensions.FileProviders.Physical/1.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "1.0.0", "Microsoft.Extensions.FileSystemGlobbing": "1.0.0", "System.Collections.Concurrent": "4.0.12", "System.IO.FileSystem": "4.0.1", "System.IO.FileSystem.Watcher": "4.0.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}, "runtime": {"lib/netstandard1.3/Microsoft.Extensions.FileProviders.Physical.dll": {}}, "compile": {"lib/netstandard1.3/Microsoft.Extensions.FileProviders.Physical.dll": {}}}, "Microsoft.Extensions.FileSystemGlobbing/1.0.0": {"dependencies": {"System.Collections": "4.0.11", "System.IO.FileSystem": "4.0.1", "System.Linq": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime.Extensions": "4.1.0"}, "runtime": {"lib/netstandard1.3/Microsoft.Extensions.FileSystemGlobbing.dll": {}}, "compile": {"lib/netstandard1.3/Microsoft.Extensions.FileSystemGlobbing.dll": {}}}, "Microsoft.Extensions.Globalization.CultureInfoCache/1.0.0": {"dependencies": {"System.Collections.Concurrent": "4.0.12", "System.Linq": "4.1.0", "System.Resources.ResourceManager": "4.0.1"}, "runtime": {"lib/netstandard1.1/Microsoft.Extensions.Globalization.CultureInfoCache.dll": {}}, "compile": {"lib/netstandard1.1/Microsoft.Extensions.Globalization.CultureInfoCache.dll": {}}}, "Microsoft.Extensions.Localization/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "1.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "1.0.0", "Microsoft.Extensions.Localization.Abstractions": "1.0.0", "Microsoft.Extensions.Options": "1.0.0", "System.Collections.Concurrent": "4.0.12", "System.Resources.Reader": "4.0.0"}, "runtime": {"lib/netstandard1.3/Microsoft.Extensions.Localization.dll": {}}, "compile": {"lib/netstandard1.3/Microsoft.Extensions.Localization.dll": {}}}, "Microsoft.Extensions.Localization.Abstractions/1.0.0": {"dependencies": {"Microsoft.CSharp": "4.0.1", "System.Globalization": "4.0.11", "System.Resources.ResourceManager": "4.0.1"}, "runtime": {"lib/netstandard1.0/Microsoft.Extensions.Localization.Abstractions.dll": {}}, "compile": {"lib/netstandard1.0/Microsoft.Extensions.Localization.Abstractions.dll": {}}}, "Microsoft.Extensions.Logging/1.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "1.0.0", "Microsoft.Extensions.Logging.Abstractions": "1.0.0", "System.Threading": "4.0.11"}, "runtime": {"lib/netstandard1.1/Microsoft.Extensions.Logging.dll": {}}, "compile": {"lib/netstandard1.1/Microsoft.Extensions.Logging.dll": {}}}, "Microsoft.Extensions.Logging.Abstractions/1.0.0": {"dependencies": {"System.Collections": "4.0.11", "System.Collections.Concurrent": "4.0.12", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.Linq": "4.1.0", "System.Reflection": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime.Extensions": "4.1.0", "System.Runtime.InteropServices": "4.1.0"}, "runtime": {"lib/netstandard1.1/Microsoft.Extensions.Logging.Abstractions.dll": {}}, "compile": {"lib/netstandard1.1/Microsoft.Extensions.Logging.Abstractions.dll": {}}}, "Microsoft.Extensions.Logging.Console/1.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "1.0.0", "Microsoft.Extensions.Logging.Abstractions": "1.0.0", "System.Console": "4.0.0", "System.Runtime.InteropServices.RuntimeInformation": "4.0.0", "System.Threading": "4.0.11"}, "runtime": {"lib/netstandard1.3/Microsoft.Extensions.Logging.Console.dll": {}}, "compile": {"lib/netstandard1.3/Microsoft.Extensions.Logging.Console.dll": {}}}, "Microsoft.Extensions.Logging.Debug/1.0.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "1.0.0", "System.Diagnostics.Debug": "4.0.11"}, "runtime": {"lib/netstandard1.3/Microsoft.Extensions.Logging.Debug.dll": {}}, "compile": {"lib/netstandard1.3/Microsoft.Extensions.Logging.Debug.dll": {}}}, "Microsoft.Extensions.ObjectPool/1.0.0": {"dependencies": {"System.Diagnostics.Debug": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}, "runtime": {"lib/netstandard1.3/Microsoft.Extensions.ObjectPool.dll": {}}, "compile": {"lib/netstandard1.3/Microsoft.Extensions.ObjectPool.dll": {}}}, "Microsoft.Extensions.Options/1.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "1.0.0", "Microsoft.Extensions.Primitives": "1.0.0", "System.ComponentModel": "4.0.1", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.Linq": "4.1.0", "System.Linq.Expressions": "4.1.0", "System.Reflection": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}, "runtime": {"lib/netstandard1.0/Microsoft.Extensions.Options.dll": {}}, "compile": {"lib/netstandard1.0/Microsoft.Extensions.Options.dll": {}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/1.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "1.0.0", "Microsoft.Extensions.Configuration.Binder": "1.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "1.0.0", "Microsoft.Extensions.Options": "1.0.0"}, "runtime": {"lib/netstandard1.1/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {}}, "compile": {"lib/netstandard1.1/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {}}}, "Microsoft.Extensions.PlatformAbstractions/1.0.0": {"dependencies": {"System.AppContext": "4.1.0", "System.Reflection": "4.1.0", "System.Reflection.Extensions": "4.0.1", "System.Reflection.TypeExtensions": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime.Extensions": "4.1.0"}, "runtime": {"lib/netstandard1.3/Microsoft.Extensions.PlatformAbstractions.dll": {}}, "compile": {"lib/netstandard1.3/Microsoft.Extensions.PlatformAbstractions.dll": {}}}, "Microsoft.Extensions.Primitives/1.0.0": {"dependencies": {"System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0"}, "runtime": {"lib/netstandard1.0/Microsoft.Extensions.Primitives.dll": {}}, "compile": {"lib/netstandard1.0/Microsoft.Extensions.Primitives.dll": {}}}, "Microsoft.Extensions.WebEncoders/1.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "1.0.0", "Microsoft.Extensions.Options": "1.0.0", "System.Text.Encodings.Web": "4.0.0"}, "runtime": {"lib/netstandard1.0/Microsoft.Extensions.WebEncoders.dll": {}}, "compile": {"lib/netstandard1.0/Microsoft.Extensions.WebEncoders.dll": {}}}, "Microsoft.Net.Http.Headers/1.0.0": {"dependencies": {"System.Buffers": "4.0.0", "System.Collections": "4.0.11", "System.Diagnostics.Contracts": "4.0.1", "System.Globalization": "4.0.11", "System.Linq": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime.Extensions": "4.1.0", "System.Text.Encoding": "4.0.11"}, "runtime": {"lib/netstandard1.1/Microsoft.Net.Http.Headers.dll": {}}, "compile": {"lib/netstandard1.1/Microsoft.Net.Http.Headers.dll": {}}}, "Newtonsoft.Json/9.0.1": {"dependencies": {"Microsoft.CSharp": "4.0.1", "System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Dynamic.Runtime": "4.0.11", "System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.Linq": "4.1.0", "System.Linq.Expressions": "4.1.0", "System.ObjectModel": "4.0.12", "System.Reflection": "4.1.0", "System.Reflection.Extensions": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Serialization.Primitives": "4.1.1", "System.Text.Encoding": "4.0.11", "System.Text.Encoding.Extensions": "4.0.11", "System.Text.RegularExpressions": "4.1.0", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.0.11", "System.Xml.ReaderWriter": "4.0.11", "System.Xml.XDocument": "4.0.11"}, "runtime": {"lib/netstandard1.0/Newtonsoft.Json.dll": {}}, "compile": {"lib/netstandard1.0/Newtonsoft.Json.dll": {}}}, "System.Collections.NonGeneric/4.0.1": {"dependencies": {"System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}, "runtime": {"lib/netstandard1.3/System.Collections.NonGeneric.dll": {}}, "compile": {"ref/netstandard1.3/System.Collections.NonGeneric.dll": {}}}, "System.Collections.Specialized/4.0.1": {"dependencies": {"System.Collections.NonGeneric": "4.0.1", "System.Globalization": "4.0.11", "System.Globalization.Extensions": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}, "runtime": {"lib/netstandard1.3/System.Collections.Specialized.dll": {}}}, "System.ComponentModel.Primitives/4.1.0": {"dependencies": {"System.ComponentModel": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0"}, "runtime": {"lib/netstandard1.0/System.ComponentModel.Primitives.dll": {}}, "compile": {"ref/netstandard1.0/System.ComponentModel.Primitives.dll": {}}}, "System.ComponentModel.TypeConverter/4.1.0": {"dependencies": {"System.Collections": "4.0.11", "System.Collections.NonGeneric": "4.0.1", "System.Collections.Specialized": "4.0.1", "System.ComponentModel": "4.0.1", "System.ComponentModel.Primitives": "4.1.0", "System.Globalization": "4.0.11", "System.Linq": "4.1.0", "System.Reflection": "4.1.0", "System.Reflection.Extensions": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Reflection.TypeExtensions": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}, "runtime": {"lib/netstandard1.5/System.ComponentModel.TypeConverter.dll": {}}, "compile": {"ref/netstandard1.5/System.ComponentModel.TypeConverter.dll": {}}}, "System.Diagnostics.Contracts/4.0.1": {"dependencies": {"System.Runtime": "4.1.0"}, "runtime": {"lib/netstandard1.0/System.Diagnostics.Contracts.dll": {}}, "compile": {"ref/netstandard1.0/System.Diagnostics.Contracts.dll": {}}}, "System.Net.WebSockets/4.0.0": {"dependencies": {"Microsoft.Win32.Primitives": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Threading.Tasks": "4.0.11"}, "runtime": {"lib/netstandard1.3/System.Net.WebSockets.dll": {}}, "compile": {"ref/netstandard1.3/System.Net.WebSockets.dll": {}}}, "System.Runtime.Serialization.Primitives/4.1.1": {"dependencies": {"System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0"}, "runtime": {"lib/netstandard1.3/System.Runtime.Serialization.Primitives.dll": {}}, "compile": {"ref/netstandard1.3/System.Runtime.Serialization.Primitives.dll": {}}}, "System.Text.Encodings.Web/4.0.0": {"dependencies": {"System.Diagnostics.Debug": "4.0.11", "System.IO": "4.1.0", "System.Reflection": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}, "runtime": {"lib/netstandard1.0/System.Text.Encodings.Web.dll": {}}, "compile": {"lib/netstandard1.0/System.Text.Encodings.Web.dll": {}}}, "Libuv/1.9.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1"}, "compileOnly": true}, "Microsoft.CodeAnalysis.Analyzers/1.1.0": {"compileOnly": true}, "Microsoft.CodeAnalysis.Common/1.3.0": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "1.1.0", "System.AppContext": "4.1.0", "System.Collections": "4.0.11", "System.Collections.Concurrent": "4.0.12", "System.Collections.Immutable": "1.2.0", "System.Console": "4.0.0", "System.Diagnostics.Debug": "4.0.11", "System.Diagnostics.FileVersionInfo": "4.0.0", "System.Diagnostics.StackTrace": "4.0.1", "System.Diagnostics.Tools": "4.0.1", "System.Dynamic.Runtime": "4.0.11", "System.Globalization": "4.0.11", "System.IO.FileSystem": "4.0.1", "System.IO.FileSystem.Primitives": "4.0.1", "System.Linq": "4.1.0", "System.Linq.Expressions": "4.1.0", "System.Reflection": "4.1.0", "System.Reflection.Metadata": "1.3.0", "System.Reflection.Primitives": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Runtime.Numerics": "4.0.1", "System.Security.Cryptography.Algorithms": "4.2.0", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.X509Certificates": "4.1.0", "System.Text.Encoding": "4.0.11", "System.Text.Encoding.CodePages": "4.0.1", "System.Text.Encoding.Extensions": "4.0.11", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.0.11", "System.Threading.Tasks.Parallel": "4.0.1", "System.Threading.Thread": "4.0.0", "System.Xml.ReaderWriter": "4.0.11", "System.Xml.XDocument": "4.0.11", "System.Xml.XPath.XDocument": "4.0.1", "System.Xml.XmlDocument": "4.0.1"}, "compile": {"lib/netstandard1.3/Microsoft.CodeAnalysis.dll": {}}, "compileOnly": true}, "Microsoft.CodeAnalysis.CSharp/1.3.0": {"dependencies": {"Microsoft.CodeAnalysis.Common": "1.3.0"}, "compile": {"lib/netstandard1.3/Microsoft.CodeAnalysis.CSharp.dll": {}}, "compileOnly": true}, "Microsoft.CodeAnalysis.VisualBasic/1.3.0": {"dependencies": {"Microsoft.CodeAnalysis.Common": "1.3.0"}, "compileOnly": true}, "Microsoft.CSharp/4.0.1": {"dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Dynamic.Runtime": "4.0.11", "System.Globalization": "4.0.11", "System.Linq": "4.1.0", "System.Linq.Expressions": "4.1.0", "System.ObjectModel": "4.0.12", "System.Reflection": "4.1.0", "System.Reflection.Extensions": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Reflection.TypeExtensions": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.InteropServices": "4.1.0", "System.Threading": "4.0.11"}, "compile": {"ref/netstandard1.0/Microsoft.CSharp.dll": {}}, "compileOnly": true}, "Microsoft.NETCore.App/1.0.0": {"dependencies": {"Libuv": "1.9.0", "Microsoft.CSharp": "4.0.1", "Microsoft.CodeAnalysis.CSharp": "1.3.0", "Microsoft.CodeAnalysis.VisualBasic": "1.3.0", "Microsoft.NETCore.DotNetHostPolicy": "1.0.1", "Microsoft.NETCore.Runtime.CoreCLR": "1.0.2", "Microsoft.VisualBasic": "10.0.1", "NETStandard.Library": "1.6.0", "System.Buffers": "4.0.0", "System.Collections.Immutable": "1.2.0", "System.ComponentModel": "4.0.1", "System.ComponentModel.Annotations": "4.1.0", "System.Diagnostics.DiagnosticSource": "4.0.0", "System.Diagnostics.Process": "4.1.0", "System.Dynamic.Runtime": "4.0.11", "System.Globalization.Extensions": "4.0.1", "System.IO.FileSystem.Watcher": "4.0.0", "System.IO.MemoryMappedFiles": "4.0.0", "System.IO.UnmanagedMemoryStream": "4.0.1", "System.Linq.Expressions": "4.1.0", "System.Linq.Parallel": "4.0.1", "System.Linq.Queryable": "4.0.1", "System.Net.NameResolution": "4.0.0", "System.Net.Requests": "4.0.11", "System.Net.Security": "4.0.0", "System.Net.WebHeaderCollection": "4.0.1", "System.Numerics.Vectors": "4.1.1", "System.Reflection.DispatchProxy": "4.0.1", "System.Reflection.Metadata": "1.3.0", "System.Reflection.TypeExtensions": "4.1.0", "System.Resources.Reader": "4.0.0", "System.Runtime.Loader": "4.0.0", "System.Security.Cryptography.Algorithms": "4.2.0", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Security.Cryptography.X509Certificates": "4.1.0", "System.Threading.Tasks.Dataflow": "4.6.0", "System.Threading.Tasks.Extensions": "4.0.0", "System.Threading.Tasks.Parallel": "4.0.1", "System.Threading.Thread": "4.0.0", "System.Threading.ThreadPool": "4.0.10"}, "compileOnly": true}, "Microsoft.NETCore.DotNetHost/1.0.1": {"compileOnly": true}, "Microsoft.NETCore.DotNetHostPolicy/1.0.1": {"dependencies": {"Microsoft.NETCore.DotNetHostResolver": "1.0.1"}, "compileOnly": true}, "Microsoft.NETCore.DotNetHostResolver/1.0.1": {"dependencies": {"Microsoft.NETCore.DotNetHost": "1.0.1"}, "compileOnly": true}, "Microsoft.NETCore.Jit/1.0.2": {"compileOnly": true}, "Microsoft.NETCore.Platforms/1.0.1": {"compileOnly": true}, "Microsoft.NETCore.Runtime.CoreCLR/1.0.2": {"dependencies": {"Microsoft.NETCore.Jit": "1.0.2", "Microsoft.NETCore.Windows.ApiSets": "1.0.1"}, "compileOnly": true}, "Microsoft.NETCore.Targets/1.0.1": {"compileOnly": true}, "Microsoft.NETCore.Windows.ApiSets/1.0.1": {"compileOnly": true}, "Microsoft.VisualBasic/10.0.1": {"dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Dynamic.Runtime": "4.0.11", "System.Globalization": "4.0.11", "System.Linq": "4.1.0", "System.Linq.Expressions": "4.1.0", "System.ObjectModel": "4.0.12", "System.Reflection": "4.1.0", "System.Reflection.Extensions": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Reflection.TypeExtensions": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.InteropServices": "4.1.0", "System.Threading": "4.0.11"}, "compile": {"ref/netstandard1.1/Microsoft.VisualBasic.dll": {}}, "compileOnly": true}, "Microsoft.Win32.Primitives/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.3/Microsoft.Win32.Primitives.dll": {}}, "compileOnly": true}, "Microsoft.Win32.Registry/4.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.Collections": "4.0.11", "System.Globalization": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0"}, "compile": {"ref/netstandard1.3/Microsoft.Win32.Registry.dll": {}}, "compileOnly": true}, "NETStandard.Library/1.6.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.Win32.Primitives": "4.0.1", "System.AppContext": "4.1.0", "System.Collections": "4.0.11", "System.Collections.Concurrent": "4.0.12", "System.Console": "4.0.0", "System.Diagnostics.Debug": "4.0.11", "System.Diagnostics.Tools": "4.0.1", "System.Diagnostics.Tracing": "4.1.0", "System.Globalization": "4.0.11", "System.Globalization.Calendars": "4.0.1", "System.IO": "4.1.0", "System.IO.Compression": "4.1.0", "System.IO.Compression.ZipFile": "4.0.1", "System.IO.FileSystem": "4.0.1", "System.IO.FileSystem.Primitives": "4.0.1", "System.Linq": "4.1.0", "System.Linq.Expressions": "4.1.0", "System.Net.Http": "4.1.0", "System.Net.Primitives": "4.0.11", "System.Net.Sockets": "4.1.0", "System.ObjectModel": "4.0.12", "System.Reflection": "4.1.0", "System.Reflection.Extensions": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Runtime.InteropServices.RuntimeInformation": "4.0.0", "System.Runtime.Numerics": "4.0.1", "System.Security.Cryptography.Algorithms": "4.2.0", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Security.Cryptography.X509Certificates": "4.1.0", "System.Text.Encoding": "4.0.11", "System.Text.Encoding.Extensions": "4.0.11", "System.Text.RegularExpressions": "4.1.0", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.0.11", "System.Threading.Timer": "4.0.1", "System.Xml.ReaderWriter": "4.0.11", "System.Xml.XDocument": "4.0.11"}, "compileOnly": true}, "runtime.native.System/4.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1"}, "compileOnly": true}, "runtime.native.System.IO.Compression/4.1.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1"}, "compileOnly": true}, "runtime.native.System.Net.Http/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1"}, "compileOnly": true}, "runtime.native.System.Net.Security/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1"}, "compileOnly": true}, "runtime.native.System.Security.Cryptography/4.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1"}, "compileOnly": true}, "System.AppContext/4.1.0": {"dependencies": {"System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.6/System.AppContext.dll": {}}, "compileOnly": true}, "System.Buffers/4.0.0": {"dependencies": {"System.Diagnostics.Debug": "4.0.11", "System.Diagnostics.Tracing": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Threading": "4.0.11"}, "compile": {"lib/netstandard1.1/System.Buffers.dll": {}}, "compileOnly": true}, "System.Collections/4.0.11": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.3/System.Collections.dll": {}}, "compileOnly": true}, "System.Collections.Concurrent/4.0.12": {"dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Diagnostics.Tracing": "4.1.0", "System.Globalization": "4.0.11", "System.Reflection": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.0.11"}, "compile": {"ref/netstandard1.3/System.Collections.Concurrent.dll": {}}, "compileOnly": true}, "System.Collections.Immutable/1.2.0": {"dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.Linq": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}, "compile": {"lib/netstandard1.0/System.Collections.Immutable.dll": {}}, "compileOnly": true}, "System.ComponentModel/4.0.1": {"dependencies": {"System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.0/System.ComponentModel.dll": {}}, "compileOnly": true}, "System.ComponentModel.Annotations/4.1.0": {"dependencies": {"System.Collections": "4.0.11", "System.ComponentModel": "4.0.1", "System.Globalization": "4.0.11", "System.Linq": "4.1.0", "System.Reflection": "4.1.0", "System.Reflection.Extensions": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Text.RegularExpressions": "4.1.0", "System.Threading": "4.0.11"}, "compile": {"ref/netstandard1.4/System.ComponentModel.Annotations.dll": {}}, "compileOnly": true}, "System.Console/4.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.IO": "4.1.0", "System.Runtime": "4.1.0", "System.Text.Encoding": "4.0.11"}, "compile": {"ref/netstandard1.3/System.Console.dll": {}}, "compileOnly": true}, "System.Diagnostics.Debug/4.0.11": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.3/System.Diagnostics.Debug.dll": {}}, "compileOnly": true}, "System.Diagnostics.DiagnosticSource/4.0.0": {"dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Tracing": "4.1.0", "System.Reflection": "4.1.0", "System.Runtime": "4.1.0", "System.Threading": "4.0.11"}, "compile": {"lib/netstandard1.3/System.Diagnostics.DiagnosticSource.dll": {}}, "compileOnly": true}, "System.Diagnostics.FileVersionInfo/4.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.IO.FileSystem": "4.0.1", "System.IO.FileSystem.Primitives": "4.0.1", "System.Reflection.Metadata": "1.3.0", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.InteropServices": "4.1.0"}, "compileOnly": true}, "System.Diagnostics.Process/4.1.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.Win32.Primitives": "4.0.1", "Microsoft.Win32.Registry": "4.0.0", "System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.IO.FileSystem": "4.0.1", "System.IO.FileSystem.Primitives": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Text.Encoding": "4.0.11", "System.Text.Encoding.Extensions": "4.0.11", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.0.11", "System.Threading.Thread": "4.0.0", "System.Threading.ThreadPool": "4.0.10", "runtime.native.System": "4.0.0"}, "compile": {"ref/netstandard1.4/System.Diagnostics.Process.dll": {}}, "compileOnly": true}, "System.Diagnostics.StackTrace/4.0.1": {"dependencies": {"System.Collections.Immutable": "1.2.0", "System.IO.FileSystem": "4.0.1", "System.Reflection": "4.1.0", "System.Reflection.Metadata": "1.3.0", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0"}, "compile": {"ref/netstandard1.3/System.Diagnostics.StackTrace.dll": {}}, "compileOnly": true}, "System.Diagnostics.Tools/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.0/System.Diagnostics.Tools.dll": {}}, "compileOnly": true}, "System.Diagnostics.Tracing/4.1.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.5/System.Diagnostics.Tracing.dll": {}}, "compileOnly": true}, "System.Dynamic.Runtime/4.0.11": {"dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.Linq": "4.1.0", "System.Linq.Expressions": "4.1.0", "System.ObjectModel": "4.0.12", "System.Reflection": "4.1.0", "System.Reflection.Emit": "4.0.1", "System.Reflection.Emit.ILGeneration": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Reflection.TypeExtensions": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}, "compile": {"ref/netstandard1.3/System.Dynamic.Runtime.dll": {}}, "compileOnly": true}, "System.Globalization/4.0.11": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.3/System.Globalization.dll": {}}, "compileOnly": true}, "System.Globalization.Calendars/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Globalization": "4.0.11", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.3/System.Globalization.Calendars.dll": {}}, "compileOnly": true}, "System.Globalization.Extensions/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.Globalization": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.InteropServices": "4.1.0"}, "compile": {"ref/netstandard1.3/System.Globalization.Extensions.dll": {}}, "compileOnly": true}, "System.IO/4.1.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0", "System.Text.Encoding": "4.0.11", "System.Threading.Tasks": "4.0.11"}, "compile": {"ref/netstandard1.5/System.IO.dll": {}}, "compileOnly": true}, "System.IO.Compression/4.1.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.IO": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Text.Encoding": "4.0.11", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.0.11", "runtime.native.System": "4.0.0", "runtime.native.System.IO.Compression": "4.1.0"}, "compile": {"ref/netstandard1.3/System.IO.Compression.dll": {}}, "compileOnly": true}, "System.IO.Compression.ZipFile/4.0.1": {"dependencies": {"System.Buffers": "4.0.0", "System.IO": "4.1.0", "System.IO.Compression": "4.1.0", "System.IO.FileSystem": "4.0.1", "System.IO.FileSystem.Primitives": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Text.Encoding": "4.0.11"}, "compile": {"ref/netstandard1.3/System.IO.Compression.ZipFile.dll": {}}, "compileOnly": true}, "System.IO.FileSystem/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.IO": "4.1.0", "System.IO.FileSystem.Primitives": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Text.Encoding": "4.0.11", "System.Threading.Tasks": "4.0.11"}, "compile": {"ref/netstandard1.3/System.IO.FileSystem.dll": {}}, "compileOnly": true}, "System.IO.FileSystem.Primitives/4.0.1": {"dependencies": {"System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.3/System.IO.FileSystem.Primitives.dll": {}}, "compileOnly": true}, "System.IO.FileSystem.Watcher/4.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.Win32.Primitives": "4.0.1", "System.Collections": "4.0.11", "System.IO.FileSystem": "4.0.1", "System.IO.FileSystem.Primitives": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Text.Encoding": "4.0.11", "System.Threading": "4.0.11", "System.Threading.Overlapped": "4.0.1", "System.Threading.Tasks": "4.0.11", "System.Threading.Thread": "4.0.0", "runtime.native.System": "4.0.0"}, "compile": {"ref/netstandard1.3/System.IO.FileSystem.Watcher.dll": {}}, "compileOnly": true}, "System.IO.MemoryMappedFiles/4.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.IO": "4.1.0", "System.IO.FileSystem": "4.0.1", "System.IO.FileSystem.Primitives": "4.0.1", "System.IO.UnmanagedMemoryStream": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.0.11", "runtime.native.System": "4.0.0"}, "compile": {"ref/netstandard1.3/System.IO.MemoryMappedFiles.dll": {}}, "compileOnly": true}, "System.IO.UnmanagedMemoryStream/4.0.1": {"dependencies": {"System.IO": "4.1.0", "System.IO.FileSystem.Primitives": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.InteropServices": "4.1.0", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.0.11"}, "compile": {"ref/netstandard1.3/System.IO.UnmanagedMemoryStream.dll": {}}, "compileOnly": true}, "System.Linq/4.1.0": {"dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0"}, "compile": {"ref/netstandard1.6/System.Linq.dll": {}}, "compileOnly": true}, "System.Linq.Expressions/4.1.0": {"dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.Linq": "4.1.0", "System.ObjectModel": "4.0.12", "System.Reflection": "4.1.0", "System.Reflection.Emit": "4.0.1", "System.Reflection.Emit.ILGeneration": "4.0.1", "System.Reflection.Emit.Lightweight": "4.0.1", "System.Reflection.Extensions": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Reflection.TypeExtensions": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}, "compile": {"ref/netstandard1.6/System.Linq.Expressions.dll": {}}, "compileOnly": true}, "System.Linq.Parallel/4.0.1": {"dependencies": {"System.Collections": "4.0.11", "System.Collections.Concurrent": "4.0.12", "System.Diagnostics.Debug": "4.0.11", "System.Diagnostics.Tracing": "4.1.0", "System.Linq": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.0.11"}, "compile": {"ref/netstandard1.1/System.Linq.Parallel.dll": {}}, "compileOnly": true}, "System.Linq.Queryable/4.0.1": {"dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Linq": "4.1.0", "System.Linq.Expressions": "4.1.0", "System.Reflection": "4.1.0", "System.Reflection.Extensions": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.0/System.Linq.Queryable.dll": {}}, "compileOnly": true}, "System.Net.Http/4.1.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Diagnostics.DiagnosticSource": "4.0.0", "System.Diagnostics.Tracing": "4.1.0", "System.Globalization": "4.0.11", "System.Globalization.Extensions": "4.0.1", "System.IO": "4.1.0", "System.IO.FileSystem": "4.0.1", "System.Net.Primitives": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Security.Cryptography.Algorithms": "4.2.0", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.OpenSsl": "4.0.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Security.Cryptography.X509Certificates": "4.1.0", "System.Text.Encoding": "4.0.11", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.0.11", "runtime.native.System": "4.0.0", "runtime.native.System.Net.Http": "4.0.1", "runtime.native.System.Security.Cryptography": "4.0.0"}, "compile": {"ref/netstandard1.3/System.Net.Http.dll": {}}, "compileOnly": true}, "System.Net.NameResolution/4.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.Collections": "4.0.11", "System.Diagnostics.Tracing": "4.1.0", "System.Globalization": "4.0.11", "System.Net.Primitives": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Security.Principal.Windows": "4.0.0", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.0.11", "runtime.native.System": "4.0.0"}, "compile": {"ref/netstandard1.3/System.Net.NameResolution.dll": {}}, "compileOnly": true}, "System.Net.Primitives/4.0.11": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0", "System.Runtime.Handles": "4.0.1"}, "compile": {"ref/netstandard1.3/System.Net.Primitives.dll": {}}, "compileOnly": true}, "System.Net.Requests/4.0.11": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Diagnostics.Tracing": "4.1.0", "System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.Net.Http": "4.1.0", "System.Net.Primitives": "4.0.11", "System.Net.WebHeaderCollection": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.0.11"}, "compile": {"ref/netstandard1.3/System.Net.Requests.dll": {}}, "compileOnly": true}, "System.Net.Security/4.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.Win32.Primitives": "4.0.1", "System.Collections": "4.0.11", "System.Collections.Concurrent": "4.0.12", "System.Diagnostics.Tracing": "4.1.0", "System.Globalization": "4.0.11", "System.Globalization.Extensions": "4.0.1", "System.IO": "4.1.0", "System.Net.Primitives": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Security.Claims": "4.0.1", "System.Security.Cryptography.Algorithms": "4.2.0", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.OpenSsl": "4.0.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Security.Cryptography.X509Certificates": "4.1.0", "System.Security.Principal": "4.0.1", "System.Text.Encoding": "4.0.11", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.0.11", "System.Threading.ThreadPool": "4.0.10", "runtime.native.System": "4.0.0", "runtime.native.System.Net.Security": "4.0.1", "runtime.native.System.Security.Cryptography": "4.0.0"}, "compile": {"ref/netstandard1.3/System.Net.Security.dll": {}}, "compileOnly": true}, "System.Net.Sockets/4.1.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.IO": "4.1.0", "System.Net.Primitives": "4.0.11", "System.Runtime": "4.1.0", "System.Threading.Tasks": "4.0.11"}, "compile": {"ref/netstandard1.3/System.Net.Sockets.dll": {}}, "compileOnly": true}, "System.Net.WebHeaderCollection/4.0.1": {"dependencies": {"System.Collections": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0"}, "compile": {"ref/netstandard1.3/System.Net.WebHeaderCollection.dll": {}}, "compileOnly": true}, "System.Numerics.Vectors/4.1.1": {"dependencies": {"System.Globalization": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0"}, "compile": {"ref/netstandard1.0/System.Numerics.Vectors.dll": {}}, "compileOnly": true}, "System.ObjectModel/4.0.12": {"dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Threading": "4.0.11"}, "compile": {"ref/netstandard1.3/System.ObjectModel.dll": {}}, "compileOnly": true}, "System.Reflection/4.1.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.IO": "4.1.0", "System.Reflection.Primitives": "4.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.5/System.Reflection.dll": {}}, "compileOnly": true}, "System.Reflection.DispatchProxy/4.0.1": {"dependencies": {"System.Collections": "4.0.11", "System.Linq": "4.1.0", "System.Reflection": "4.1.0", "System.Reflection.Emit": "4.0.1", "System.Reflection.Emit.ILGeneration": "4.0.1", "System.Reflection.Extensions": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Threading": "4.0.11"}, "compile": {"ref/netstandard1.3/System.Reflection.DispatchProxy.dll": {}}, "compileOnly": true}, "System.Reflection.Emit/4.0.1": {"dependencies": {"System.IO": "4.1.0", "System.Reflection": "4.1.0", "System.Reflection.Emit.ILGeneration": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Runtime": "4.1.0"}, "compileOnly": true}, "System.Reflection.Emit.ILGeneration/4.0.1": {"dependencies": {"System.Reflection": "4.1.0", "System.Reflection.Primitives": "4.0.1", "System.Runtime": "4.1.0"}, "compileOnly": true}, "System.Reflection.Emit.Lightweight/4.0.1": {"dependencies": {"System.Reflection": "4.1.0", "System.Reflection.Emit.ILGeneration": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Runtime": "4.1.0"}, "compileOnly": true}, "System.Reflection.Extensions/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Reflection": "4.1.0", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.0/System.Reflection.Extensions.dll": {}}, "compileOnly": true}, "System.Reflection.Metadata/1.3.0": {"dependencies": {"System.Collections": "4.0.11", "System.Collections.Immutable": "1.2.0", "System.Diagnostics.Debug": "4.0.11", "System.IO": "4.1.0", "System.Linq": "4.1.0", "System.Reflection": "4.1.0", "System.Reflection.Extensions": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.InteropServices": "4.1.0", "System.Text.Encoding": "4.0.11", "System.Text.Encoding.Extensions": "4.0.11", "System.Threading": "4.0.11"}, "compile": {"lib/netstandard1.1/System.Reflection.Metadata.dll": {}}, "compileOnly": true}, "System.Reflection.Primitives/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.0/System.Reflection.Primitives.dll": {}}, "compileOnly": true}, "System.Reflection.TypeExtensions/4.1.0": {"dependencies": {"System.Reflection": "4.1.0", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.5/System.Reflection.TypeExtensions.dll": {}}, "compileOnly": true}, "System.Resources.Reader/4.0.0": {"dependencies": {"System.IO": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Text.Encoding": "4.0.11", "System.Threading": "4.0.11"}, "compile": {"lib/netstandard1.0/System.Resources.Reader.dll": {}}, "compileOnly": true}, "System.Resources.ResourceManager/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Globalization": "4.0.11", "System.Reflection": "4.1.0", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.0/System.Resources.ResourceManager.dll": {}}, "compileOnly": true}, "System.Runtime/4.1.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1"}, "compile": {"ref/netstandard1.5/System.Runtime.dll": {}}, "compileOnly": true}, "System.Runtime.Extensions/4.1.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.5/System.Runtime.Extensions.dll": {}}, "compileOnly": true}, "System.Runtime.Handles/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.3/System.Runtime.Handles.dll": {}}, "compileOnly": true}, "System.Runtime.InteropServices/4.1.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Reflection": "4.1.0", "System.Reflection.Primitives": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Handles": "4.0.1"}, "compile": {"ref/netstandard1.5/System.Runtime.InteropServices.dll": {}}, "compileOnly": true}, "System.Runtime.InteropServices.RuntimeInformation/4.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.Reflection": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.InteropServices": "4.1.0", "System.Threading": "4.0.11", "runtime.native.System": "4.0.0"}, "compile": {"ref/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll": {}}, "compileOnly": true}, "System.Runtime.Loader/4.0.0": {"dependencies": {"System.IO": "4.1.0", "System.Reflection": "4.1.0", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.5/System.Runtime.Loader.dll": {}}, "compileOnly": true}, "System.Runtime.Numerics/4.0.1": {"dependencies": {"System.Globalization": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0"}, "compile": {"ref/netstandard1.1/System.Runtime.Numerics.dll": {}}, "compileOnly": true}, "System.Security.Claims/4.0.1": {"dependencies": {"System.Collections": "4.0.11", "System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Security.Principal": "4.0.1"}, "compile": {"ref/netstandard1.3/System.Security.Claims.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Algorithms/4.2.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.Collections": "4.0.11", "System.IO": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Runtime.Numerics": "4.0.1", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Text.Encoding": "4.0.11", "runtime.native.System.Security.Cryptography": "4.0.0"}, "compile": {"ref/netstandard1.6/System.Security.Cryptography.Algorithms.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Cng/4.2.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.IO": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Security.Cryptography.Algorithms": "4.2.0", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Text.Encoding": "4.0.11"}, "compileOnly": true}, "System.Security.Cryptography.Csp/4.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.IO": "4.1.0", "System.Reflection": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Security.Cryptography.Algorithms": "4.2.0", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Text.Encoding": "4.0.11", "System.Threading": "4.0.11"}, "compileOnly": true}, "System.Security.Cryptography.Encoding/4.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.Collections": "4.0.11", "System.Collections.Concurrent": "4.0.12", "System.Linq": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Text.Encoding": "4.0.11", "runtime.native.System.Security.Cryptography": "4.0.0"}, "compile": {"ref/netstandard1.3/System.Security.Cryptography.Encoding.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.OpenSsl/4.0.0": {"dependencies": {"System.Collections": "4.0.11", "System.IO": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Runtime.Numerics": "4.0.1", "System.Security.Cryptography.Algorithms": "4.2.0", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Text.Encoding": "4.0.11", "runtime.native.System.Security.Cryptography": "4.0.0"}, "compileOnly": true}, "System.Security.Cryptography.Primitives/4.0.0": {"dependencies": {"System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.0.11"}, "compile": {"ref/netstandard1.3/System.Security.Cryptography.Primitives.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.X509Certificates/4.1.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.Globalization.Calendars": "4.0.1", "System.IO": "4.1.0", "System.IO.FileSystem": "4.0.1", "System.IO.FileSystem.Primitives": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Runtime.Numerics": "4.0.1", "System.Security.Cryptography.Algorithms": "4.2.0", "System.Security.Cryptography.Cng": "4.2.0", "System.Security.Cryptography.Csp": "4.0.0", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.OpenSsl": "4.0.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Text.Encoding": "4.0.11", "System.Threading": "4.0.11", "runtime.native.System": "4.0.0", "runtime.native.System.Net.Http": "4.0.1", "runtime.native.System.Security.Cryptography": "4.0.0"}, "compile": {"ref/netstandard1.4/System.Security.Cryptography.X509Certificates.dll": {}}, "compileOnly": true}, "System.Security.Principal/4.0.1": {"dependencies": {"System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.0/System.Security.Principal.dll": {}}, "compileOnly": true}, "System.Security.Principal.Windows/4.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.Win32.Primitives": "4.0.1", "System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Reflection": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Security.Claims": "4.0.1", "System.Security.Principal": "4.0.1", "System.Text.Encoding": "4.0.11", "System.Threading": "4.0.11"}, "compile": {"ref/netstandard1.3/System.Security.Principal.Windows.dll": {}}, "compileOnly": true}, "System.Text.Encoding/4.0.11": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.3/System.Text.Encoding.dll": {}}, "compileOnly": true}, "System.Text.Encoding.CodePages/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.Collections": "4.0.11", "System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.Reflection": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Text.Encoding": "4.0.11", "System.Threading": "4.0.11"}, "compileOnly": true}, "System.Text.Encoding.Extensions/4.0.11": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0", "System.Text.Encoding": "4.0.11"}, "compile": {"ref/netstandard1.3/System.Text.Encoding.Extensions.dll": {}}, "compileOnly": true}, "System.Text.RegularExpressions/4.1.0": {"dependencies": {"System.Collections": "4.0.11", "System.Globalization": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}, "compile": {"ref/netstandard1.6/System.Text.RegularExpressions.dll": {}}, "compileOnly": true}, "System.Threading/4.0.11": {"dependencies": {"System.Runtime": "4.1.0", "System.Threading.Tasks": "4.0.11"}, "compile": {"ref/netstandard1.3/System.Threading.dll": {}}, "compileOnly": true}, "System.Threading.Overlapped/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Handles": "4.0.1"}, "compileOnly": true}, "System.Threading.Tasks/4.0.11": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.3/System.Threading.Tasks.dll": {}}, "compileOnly": true}, "System.Threading.Tasks.Dataflow/4.6.0": {"dependencies": {"System.Collections": "4.0.11", "System.Collections.Concurrent": "4.0.12", "System.Diagnostics.Debug": "4.0.11", "System.Diagnostics.Tracing": "4.1.0", "System.Dynamic.Runtime": "4.0.11", "System.Linq": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.0.11"}, "compile": {"lib/netstandard1.1/System.Threading.Tasks.Dataflow.dll": {}}, "compileOnly": true}, "System.Threading.Tasks.Extensions/4.0.0": {"dependencies": {"System.Collections": "4.0.11", "System.Runtime": "4.1.0", "System.Threading.Tasks": "4.0.11"}, "compile": {"lib/netstandard1.0/System.Threading.Tasks.Extensions.dll": {}}, "compileOnly": true}, "System.Threading.Tasks.Parallel/4.0.1": {"dependencies": {"System.Collections.Concurrent": "4.0.12", "System.Diagnostics.Debug": "4.0.11", "System.Diagnostics.Tracing": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.0.11"}, "compile": {"ref/netstandard1.1/System.Threading.Tasks.Parallel.dll": {}}, "compileOnly": true}, "System.Threading.Thread/4.0.0": {"dependencies": {"System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.3/System.Threading.Thread.dll": {}}, "compileOnly": true}, "System.Threading.ThreadPool/4.0.10": {"dependencies": {"System.Runtime": "4.1.0", "System.Runtime.Handles": "4.0.1"}, "compile": {"ref/netstandard1.3/System.Threading.ThreadPool.dll": {}}, "compileOnly": true}, "System.Threading.Timer/4.0.1": {"dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.2/System.Threading.Timer.dll": {}}, "compileOnly": true}, "System.Xml.ReaderWriter/4.0.11": {"dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.IO.FileSystem": "4.0.1", "System.IO.FileSystem.Primitives": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.InteropServices": "4.1.0", "System.Text.Encoding": "4.0.11", "System.Text.Encoding.Extensions": "4.0.11", "System.Text.RegularExpressions": "4.1.0", "System.Threading.Tasks": "4.0.11", "System.Threading.Tasks.Extensions": "4.0.0"}, "compile": {"ref/netstandard1.3/System.Xml.ReaderWriter.dll": {}}, "compileOnly": true}, "System.Xml.XDocument/4.0.11": {"dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Diagnostics.Tools": "4.0.1", "System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.Reflection": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Text.Encoding": "4.0.11", "System.Threading": "4.0.11", "System.Xml.ReaderWriter": "4.0.11"}, "compile": {"ref/netstandard1.3/System.Xml.XDocument.dll": {}}, "compileOnly": true}, "System.Xml.XmlDocument/4.0.1": {"dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Text.Encoding": "4.0.11", "System.Threading": "4.0.11", "System.Xml.ReaderWriter": "4.0.11"}, "compileOnly": true}, "System.Xml.XPath/4.0.1": {"dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11", "System.Xml.ReaderWriter": "4.0.11"}, "compileOnly": true}, "System.Xml.XPath.XDocument/4.0.1": {"dependencies": {"System.Diagnostics.Debug": "4.0.11", "System.Linq": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11", "System.Xml.ReaderWriter": "4.0.11", "System.Xml.XDocument": "4.0.11", "System.Xml.XPath": "4.0.1"}, "compileOnly": true}}}, "libraries": {"ApiHelloWorld/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Antiforgery/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-oJnrSvL6S7jM2eD/TR/Kyp/7O6pKvN+8FcnYvUaxaHbKlISwl98o44uidzePBjGxTf4fh9NFEx/q3OuuxAvBzw=="}, "Microsoft.AspNetCore.Authorization/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-iVFQ5xHSyxmfWYdl5B/xIFzXgm4SRgYQUKlLFVNGfEhbbjw0Ur2pfVrEvpENrhHFOQ2XAZcuFlGxSIzZwsVrMg=="}, "Microsoft.AspNetCore.Cors/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fC8lWOU3+ltkbgQyD1P7eRQ66fGfZkPNU2UkwOI8tyF5FUsd8nRTfzvsO4mSyQfgmgfk2Hc8TGzx/okevZwXkg=="}, "Microsoft.AspNetCore.Cryptography.Internal/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0btvxwOqYNpKTUQrD7LA3p6Wi0vrhfWGBVqIKPS1KtEdkCv3QoVgFO4eJYuClGDS9NXhqk7TWh46/8x8wtZHaw=="}, "Microsoft.AspNetCore.DataProtection/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-gt4URT+8ljPk0ePspLqOGPJBm+s6iMvsZqweplhf7wiZSjFiG1uYBNpQ/0dFY7wSx3NMRjekyXzCjvkGAV570g=="}, "Microsoft.AspNetCore.DataProtection.Abstractions/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-h5ycDgkqmRdManmYMQVJgzNI7YtVp2X2/os1cKmdfrpfq+m9L8bMKhbd7PCksoLci+aYTOSn45khPl+hpPb9ug=="}, "Microsoft.AspNetCore.Diagnostics.Abstractions/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-RrXsm5Xzvxs0OFDhRcIIUNOM5rXKnRWj/bIkuDkXNIBniGcPDrfGbACIatA127I6pmybNAE84puFAt3wsU2kww=="}, "Microsoft.AspNetCore.Hosting/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0M7ZRAxTmGHOQV3B5Lm30VBg33uxxkPIKAxMc/C9yFBMPWPfk6V1uvb2ZL5eEPlo9/MZooITyMcGBQUHiakFjg=="}, "Microsoft.AspNetCore.Hosting.Abstractions/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-8r6qOl1jYyC523ZKM1QNl+6ijIoYWELWm0tpEWqtTIOg9DytHJWshB7usgqiuRmfHXM0EUziR6ouFY7iP7Tuzw=="}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-sHZyhQEoW15T9E36rfdm5Ux6a6RZB0KNM79ccf2IplWASqmlRGhX4ydU3dzQRLhkHpLx16fnWOL0KScsO6BevQ=="}, "Microsoft.AspNetCore.Html.Abstractions/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/JLMu2k8FiInLZC0SHXT+Cmdzi7AYa3B5v9w32Kd0mPTH4RYIQo/XNPIOr2HsPTXp3I9cZo1DajaMVGnJMN2QA=="}, "Microsoft.AspNetCore.Http/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-c/+eWVWQ8fX5hBHhL1BY4k2n4kVyUnqJLSCj0sTTXwRTU6IKoGbTOUqHT9as8C71Vk54YpAsi/VPmGW7T/ap3A=="}, "Microsoft.AspNetCore.Http.Abstractions/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OJHlqdJOWKKBfsiVdX4Z4KCNuqvBIu6+1MVKuejRDyHnGyMkNHNoP/dtVzhPqvJXaJg9N4HlD0XNc6GDCFVffg=="}, "Microsoft.AspNetCore.Http.Extensions/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-GlvCPRpnw2jjHLdbGf/C28NQZLMeX1mugv5BS1a3FCQOJYyuwQZil4JwblR0frLyVrUVoJQ7UXRNZIzEVlO5XA=="}, "Microsoft.AspNetCore.Http.Features/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-6x7zgfbTo1gL9xMEb7EMO2ES/48bqwnWyfH09z+ubWhnzxdhHls8rtqstPylu5FPD9nid6Vo2pgDm5vufRAy5Q=="}, "Microsoft.AspNetCore.HttpOverrides/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-gHpdaaAzhaTWJZuJVo3ler2zzdQWrm8wnsoSjcNtoZZdTOkwImndRwK8o4GYoM18dfmfNheM7i4EENI7XHM/lA=="}, "Microsoft.AspNetCore.JsonPatch/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-WVaSVS+dDlWCR/qerHnBxU9tIeJ9GMA3M5tg4cxH7/cJYZZLnr2zvaFHGB+cRRNCKKTJ0pFRxT7ES8knhgAAaA=="}, "Microsoft.AspNetCore.Localization/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-DF/maMd9f6ZPoTlU8n6/AGm3fpZNPiiip34bPrBQuloX2a5O0KwyV72qKhJhJNqmVVnDnTu8XYT16ysoFXRxQA=="}, "Microsoft.AspNetCore.Mvc/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-nNiMnzdXHpMrsjnBRiYaVy5EMsCmTsqSIIOtJvMbqJldh1i3NCM9jgvp4Da+Ke1gkGd2/MK8rXp+8a5yF+QOOQ=="}, "Microsoft.AspNetCore.Mvc.Abstractions/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-d7KEexDwxSwVeZv+SDbsMRPl2WuKMVckOCp/KTGuI1NJhd/7GvNGW101iRIC3tC/yym0PaajcWwTZNVfjhyoJw=="}, "Microsoft.AspNetCore.Mvc.ApiExplorer/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-46aWHLmZ37c44bJzLdbSEmIxCwQo7BljHBoK8C9CPCEPOLPWmg0XyPhGyMSGY4woDmm9ukBOEpqT899BWSxhRw=="}, "Microsoft.AspNetCore.Mvc.Core/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tjCOZJheOAKStHs4LIcrLsbF/00wEwSinC+vCFpsmdqGVl3/tX9jnID20E1NlkKOW68DOLBavoC23BWFiHa0JA=="}, "Microsoft.AspNetCore.Mvc.Cors/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jz3au6mm/O0ahotfUqZTGtsftcd4UYKIzl2l0+WRG817UJdMGLmnmgmUPcAQR1nrI0Dg49MsfTkjWoMQM9CsUw=="}, "Microsoft.AspNetCore.Mvc.DataAnnotations/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZU02Y2tnKu/lVv2ywnNO+nSRzDWiTlq+ZhSuR9L3Q9NqlCyQJXOgX+iD/BGshnMQ7ZTstjyO4h8WeF7Ii9vBWQ=="}, "Microsoft.AspNetCore.Mvc.Formatters.Json/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-XQQLbxYLmdRj2U685NxFIrxVxqsXHLO5zN4ZIhTQ23VxzI6Qk2WN9ska0tl4ZMDV/4pSyE8SlmNeKPCN3pW86w=="}, "Microsoft.AspNetCore.Mvc.Localization/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+w4s6j88pzJmc++3IozCmo0AIOF8ks/LrOAuMTRm6ve/l+wTp/oqXu2tjLA3QAvP6n6hC3cm40qW69UhYUtSIQ=="}, "Microsoft.AspNetCore.Mvc.Razor/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-G17pVnANhBj6AdpzTnJV36MRx4KNLQao0NqGUyKFvtKjy77KR55Fmt6/MVykbOB5xH33fbMIveTiSF3h4kWSQA=="}, "Microsoft.AspNetCore.Mvc.Razor.Host/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cMdbvKf56IRyviirKFAgwcUSxwzLVASRA8cgxQD6Bw/JO9uwpG33mWjMnsdmZveW0y/ek1FjHTx6Zd4UpZfQ6A=="}, "Microsoft.AspNetCore.Mvc.TagHelpers/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-5IT4kddg3Tz3Ki53HvP3fvjnpYzKjY5mFWhmpPQvE2vzfMr7zU6X1Cls2SnJPMcV6sAqzTB4j6AmUmcEpFNMqg=="}, "Microsoft.AspNetCore.Mvc.ViewFeatures/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-DNMCqY+TX5jgO3M1C7Lf5E61llWZ+QgtjLYfrIkq7yfZjhzI52nprFE3mh66HahKU1EvyOz9+ISdaSmTimfNbQ=="}, "Microsoft.AspNetCore.Razor/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+vhlFn8n45hj1M91HYVm2ryLMZ+ZYR/OUdBVE8aUzkvkTVF+3UnNxSY3hAEugcgcbf9/XQTE+DDxEgN4LdYEjg=="}, "Microsoft.AspNetCore.Razor.Runtime/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hsq6xJeqDDb78akZuy79QE3kaCxcigD3vccbIaNrrz7JSXOzayfteF06ToK+J1SXSDRtrBj3XZZfrjiqIY/vCw=="}, "Microsoft.AspNetCore.Routing/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NvFvRtYHXWjBbXz5/7F7JDNcdhrE+tG1/Q9R6LmMxFgu8tkl1bqtFZQbMy17FYFkmm8Fn/T81blRGE2nxCbDRA=="}, "Microsoft.AspNetCore.Routing.Abstractions/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Ne5CFiD1xCGSHrGICw7PsVnj7gijfkMfsw52AO6ingcUhE01dc87cJPpfGLnY22MIvqn11ECLbNZYmzFp/Rs+A=="}, "Microsoft.AspNetCore.Server.IISIntegration/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xmn6EivvL4Ymo7LP+Jc49WLcIiYsUiujZo0loEbAg473nY2dIHxcxncpFAKzPf/MzqN0qBtaXEP0igYJ813H3Q=="}, "Microsoft.AspNetCore.Server.Kestrel/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-TNRTsufpdeoa88kR2NU+mO0IZIyJCcBurkdLx4I9d7MpLV1MCnRCrIeTgFIOWpB+j6kytUUXblzhsd0rfk6+bQ=="}, "Microsoft.AspNetCore.WebUtilities/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-D0licSnS1JgqQ/gYlN41wXbeYG3dFIdjY781YzMHZ5gBB7kczacshW+H6plZkXRr/cCnAJWGa31o1R8c5GEy/A=="}, "Microsoft.DotNet.InternalAbstractions/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AAguUq7YyKk3yDWPoWA8DrLZvURxB/LrDdTn1h5lmPeznkFUpfC3p459w5mQYQE0qpquf/CkSQZ0etiV5vRHFA=="}, "Microsoft.Extensions.Caching.Abstractions/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IxlFDVOchL6tdR05bk7EiJvMtvZrVkZXBhkbXqc3GxOHOrHFGcN+92WoWFPeBpdpy8ot/Px5ZdXzt7k+2n1Bdg=="}, "Microsoft.Extensions.Caching.Memory/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-6+7zTufCnZ+tfrUo7RbIRR3LB0BxwOwxfXuo0IbLyIvgoToGpWuz5wYEDfCYNOvpig9tY8FA0I1uRHYmITMXMQ=="}, "Microsoft.Extensions.Configuration/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hR4yYebruRp6qyFnV3RW4qrnEb0J1LnMmQbj50AUA423V8dMs4E3YAohsyRyGBFnpbJ+KKzieSG/n2A6T0klZQ=="}, "Microsoft.Extensions.Configuration.Abstractions/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-nJ+Et/rnDMDmGhxvFAKdN3va7y+YDPICv1nUEP8I4IKgOkWwr/dCZHMqxVhJFrkbW9ux8Kd7erC4mvxfZh0WnA=="}, "Microsoft.Extensions.Configuration.Binder/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-kK8QuBcPQtmKJCkC9enc1uMRFa++mPTuVNm2K5jDVXcAYKRBcFSbdEBvIe1JIgA6dEsAQeqjfHfKSaUJ8f5NFQ=="}, "Microsoft.Extensions.Configuration.EnvironmentVariables/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-A0yqS98VtPNlFkFI7YBlwkAekUHE/9mMeNc+K4RmgTjCrskuk6pX3LGhDU7aD5CPYc9Px7M2To/2u4xDSnRikg=="}, "Microsoft.Extensions.Configuration.FileExtensions/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MO7XtmLiqnpgVTX34uzFPvIS7jPmBUGLN0MP5MsYu6CqYTIs90ULjtrV5TegH5mTqKTXWjZRGXL26R6apTyc4w=="}, "Microsoft.Extensions.Configuration.Json/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-KRyEOe5/Xi3qyDMdEVh++e2pQRsI6C3wzINVExOcu9lOsFmXK/k4qOf244fyo59rnn6s5xKnIW3WbhxWS1hu2w=="}, "Microsoft.Extensions.DependencyInjection/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-zdtkiZNV6LB8xtpmfyUjP/9N9ZCL/ydQ+0bfjun38fbrk+MDEm9M2yeLzRdq+OIt5xExw/KU04wFaVwJ1bhQPg=="}, "Microsoft.Extensions.DependencyInjection.Abstractions/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+XwaNo3o9RhLQhUnnOBCaukeRi1X9yYc0Fzye9RlErSflKZdw0VgHtn6rvKo0FTionsW0x8QVULhKH+nkqVjQA=="}, "Microsoft.Extensions.DependencyModel/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-n55Y2T4qMgCNMrJaqAN+nlG2EH4XL+e9uxIg4vdFsQeF+L8UKxRdD3C35Bt+xk3vO3Zwp3g+6KFq2VPH2COSmg=="}, "Microsoft.Extensions.FileProviders.Abstractions/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-4jsqTxG3py/hYSsOtZMkNJ2/CQqPdpwyK7bDUkrwHgqowCFSmx/C+R4IzQ+2AK2Up1fVcu+ldC0gktwidL828A=="}, "Microsoft.Extensions.FileProviders.Composite/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-4nbDQfagNr1eILXSFZbTNuAKuZ6SsOyK6ySTMryo67ECi8+EcZBQ12E0aXcxX/aT3v+3pbWSt71NXlEm8tKIxw=="}, "Microsoft.Extensions.FileProviders.Physical/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Ej5hGWtK3xM9YU+B2O8EdlMcJf5utbDQs9ecnfvwhENQeeNU7iI2jjnRB2d7V6o9SQZmNHPzdPvaNb3PlSMz+Q=="}, "Microsoft.Extensions.FileSystemGlobbing/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-scXp1Y+hmhQKLe57Z7cSjsAEFtE4zSHHydkg1SpvG56nWwWQVpVcRAbRZsv1qIBR5/vNB4LA9xiOKnvKO/Halg=="}, "Microsoft.Extensions.Globalization.CultureInfoCache/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-nxGoN8o+4clQk103krYRqS5FVVCZc3Tlc09AYj4W8gZ9Q5Jxa2BLW7ss+ogKU/hvNSg2NkJyQTfi9SegGU6ssQ=="}, "Microsoft.Extensions.Localization/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-nkDgz++GXjMSEIiVS6CpeirV8m8zvc/vUN2sq5sPnqG8PZltCMSNmqrwyL1onx6A6aRNdTr1nVfvYHwWAmS4vg=="}, "Microsoft.Extensions.Localization.Abstractions/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hQ2sEJf7swsD5jk4DogLI3DazGvsvbz0IuSbxPFDjcvP0PRdxgCsyGpg70LD+3tRmxZcE1uh5jtcAi4X2BcB9w=="}, "Microsoft.Extensions.Logging/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0mDuASVrd/nMeBYIJSK+9lT3TSmWxUXP/ipVB1pF1ApMN5fqGCckPTNwmOfT4Z9wPkXGnhbwFTGrxZvbzTWxOg=="}, "Microsoft.Extensions.Logging.Abstractions/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-wHT6oY50q36mAXBRKtFaB7u07WxKC5u2M8fi3PqHOOnHyUo9gD0u1TlCNR8UObHQxKMYwqlgI8TLcErpt29n8A=="}, "Microsoft.Extensions.Logging.Console/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-GN4gFFONP12KbFEG9rNFpXuz6D2Tybcm8+c1wilaQ1eSl9zVX0gVRrKw/YRwxdwbM3eK7nWfRRqJaQPzOjtLnA=="}, "Microsoft.Extensions.Logging.Debug/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-V8fP8pGJxieGa1DAYOF1RX+cCGGqdOGNoAQUzxmy27+qNzbHB/cUXc7mCZT72jPZMB4U12zSVWlIt26GMsUIOg=="}, "Microsoft.Extensions.ObjectPool/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-BTXoWSTrv/saLlNSg8l41YOoSKeUUanQLykUqRTtiUJz2xxQOCgm4ckPzrdmSK6w0mdjR2h7IrUDGdBF78Z7yg=="}, "Microsoft.Extensions.Options/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-SdP3yPKF++JTkoa91pBDiE70uQkR/gdXWzOnMPbSj+eOqY1vgY+b8RVl+gh7TrJ2wlCK2QqnQtvCQlPPZRK36w=="}, "Microsoft.Extensions.Options.ConfigurationExtensions/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ov+cv1w81/lVrjqorjSpUvTzJhRVFu2GSRyRQYcGfGWPwrCz6473YoiqK/XNbeBmmF9VVyGPHVMubAH+Atsr3g=="}, "Microsoft.Extensions.PlatformAbstractions/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-zyjUzrOmuevOAJpIo3Mt5GmpALVYCVdLZ99keMbmCxxgQH7oxzU58kGHzE6hAgYEiWsdfMJLjVR7r+vSmaJmtg=="}, "Microsoft.Extensions.Primitives/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3q2vzfKEDjL6JFkRpk5SFA3zarYsO6+ZYgoucNImrUMzDn0mFbEOL5p9oPoWiypwypbJVVjWTf557bXZ0YFLig=="}, "Microsoft.Extensions.WebEncoders/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NSSIBREmHHiyoAFXV2LMA+a6RMZtTHxgUbHJGHRtnjmTKnRyticx5HAzNpy8VG9+HCCHenL9QD7zSA8jjgAi5A=="}, "Microsoft.Net.Http.Headers/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-1lr92itF1fKR2oEQ6gk1IUsuCgp7UMlf/b1sjlAyuDeUnttj39ra59GQHYpomglJX1UVNpi1/cSBbEsXoNeIhw=="}, "Newtonsoft.Json/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-U82mHQSKaIk+lpSVCbWYKNavmNH1i5xrExDEquU1i6I5pV6UMOqRnJRSlKO3cMPfcpp0RgDY+8jUXHdQ4IfXvw=="}, "System.Collections.NonGeneric/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-hMxFT2RhhlffyCdKLDXjx8WEC5JfCvNozAZxCablAuFRH74SCV4AgzE8yJCh/73bFnEoZgJ9MJmkjQ0dJmnKqA=="}, "System.Collections.Specialized/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-/HKQyVP0yH1I0YtK7KJL/28snxHNH/bi+0lgk/+MbURF6ULhAE31MDI+NZDerNWu264YbxklXCCygISgm+HMug=="}, "System.ComponentModel.Primitives/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-sc/7eVCdxPrp3ljpgTKVaQGUXiW05phNWvtv/m2kocXqrUQvTVWKou1Edas2aDjTThLPZOxPYIGNb/HN0QjURg=="}, "System.ComponentModel.TypeConverter/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-MnDAlaeJZy9pdB5ZdOlwdxfpI+LJQ6e0hmH7d2+y2LkiD8DRJynyDYl4Xxf3fWFm7SbEwBZh4elcfzONQLOoQw=="}, "System.Diagnostics.Contracts/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-HvQQjy712vnlpPxaloZYkuE78Gn353L0SJLJVeLcNASeg9c4qla2a1Xq8I7B3jZoDzKPtHTkyVO7AZ5tpeQGuA=="}, "System.Net.WebSockets/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-2KJo8hir6Edi9jnMDAMhiJoI691xRBmKcbNpwjrvpIMOCTYOtBpSsSEGBxBDV7PKbasJNaFp1+PZz1D7xS41Hg=="}, "System.Runtime.Serialization.Primitives/4.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-HZ6Du5QrTG8MNJbf4e4qMO3JRAkIboGT5Fk804uZtg3Gq516S7hAqTm2UZKUHa7/6HUGdVy3AqMQKbns06G/cg=="}, "System.Text.Encodings.Web/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-TWZnuiJgPDAEEUfobD7njXvSVR2Toz+jvKWds6yL4oSztmKQfnWzucczjzA+6Dv1bktBdY71sZW1YN0X6m9chQ=="}, "Libuv/1.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-9Q7AaqtQhS8JDSIvRBt6ODSLWDBI4c8YxNxyCQemWebBFUtBbc6M5Vi5Gz1ZyIUlTW3rZK9bIr5gnVyv0z7a2Q=="}, "Microsoft.CodeAnalysis.Analyzers/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-HS3iRWZKcUw/8eZ/08GXKY2Bn7xNzQPzf8gRPHGSowX7u7XXu9i9YEaBeBNKUXWfI7qjvT2zXtLUvbN0hds8vg=="}, "Microsoft.CodeAnalysis.Common/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-V09G35cs0CT1C4Dr1IEOh8IGfnWALEVAOO5JXsqagxXwmYR012TlorQ+vx2eXxfZRKs3gAS/r92gN9kRBLba5A=="}, "Microsoft.CodeAnalysis.CSharp/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BgWDIAbSFsHuGeLSn/rljLi51nXqkSo4DZ0qEIrHyPVasrhxEVq7aV8KKZ3HEfSFB+GIhBmOogE+mlOLYg19eg=="}, "Microsoft.CodeAnalysis.VisualBasic/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Sf3k8PkTkWqBmXnnblJbvb7ewO6mJzX6WO2t7m04BmOY5qBq6yhhyXnn/BMM+QCec3Arw3X35Zd8f9eBql0qgg=="}, "Microsoft.CSharp/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-17h8b5mXa87XYKrrVqdgZ38JefSUqLChUQpXgSnpzsM0nDOhE40FTeNWOJ/YmySGV6tG6T8+hjz6vxbknHJr6A=="}, "Microsoft.NETCore.App/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Bv40dLDrT+Igcg1e6otW3D8voeJCfcAxOlsxSVlDz+J+cdWls5kblZvPHHvx7gX3/oJoQVIkEeO3sMyv5PSVJA=="}, "Microsoft.NETCore.DotNetHost/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-uaMgykq6AckP3hZW4dsD6zjocxyXPz0tcTl8OX7mlSUWsyFXdtf45sjdwI0JIHxt3gnI6GihAlOAwYK8HE4niQ=="}, "Microsoft.NETCore.DotNetHostPolicy/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-d8AQ+ZVj2iK9sbgl3IEsshCSaumhM1PNTPHxldZAQLOoI1BKF8QZ1zPCNqwBGisPiWOE3f/1SHDbQi1BTRBxuA=="}, "Microsoft.NETCore.DotNetHostResolver/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-GEXgpAHB9E0OhfcmNJ664Xcd2bJkz2qkGIAFmCgEI5ANlQy4qEEmBVfUqA+Z9HB85ZwWxZc1eIJ6fxdxcjrctg=="}, "Microsoft.NETCore.Jit/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-Ok2vWofa6X8WD9vc4pfLHwvJz1/B6t3gOAoZcjrjrQf7lQOlNIuZIZtLn3wnWX28DuQGpPJkRlBxFj7Z5txNqw=="}, "Microsoft.NETCore.Platforms/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-2G6OjjJzwBfNOO8myRV/nFrbTw5iA+DEm0N+qUqhrOmaVtn4pC77h38I1jsXGw5VH55+dPfQsqHD0We9sCl9FQ=="}, "Microsoft.NETCore.Runtime.CoreCLR/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-A0x1xtTjYJWZr2DRzgfCOXgB0JkQg8twnmtTJ79wFje+IihlLbXtx6Z2AxyVokBM5ruwTedR6YdCmHk39QJdtQ=="}, "Microsoft.NETCore.Targets/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-rkn+fKobF/cbWfnnfBOQHKVKIOpxMZBvlSHkqDWgBpwGDcLRduvs3D9OLGeV6GWGvVwNlVi2CBbTjuPmtHvyNw=="}, "Microsoft.NETCore.Windows.ApiSets/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-SaToCvvsGMxTgtLv/BrFQ5IFMPRE1zpWbnqbpwykJa8W5XiX82CXI6K2o7yf5xS7EP6t/JzFLV0SIDuWpvBZVw=="}, "Microsoft.VisualBasic/10.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-HpNyOf/4Tp2lh4FyywB55VITk0SqVxEjDzsVDDyF1yafDN6Bq18xcHowzCPINyYHUTgGcEtmpYiRsFdSo0KKdQ=="}, "Microsoft.Win32.Primitives/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-fQnBHO9DgcmkC9dYSJoBqo6sH1VJwJprUHh8F3hbcRlxiQiBUuTntdk8tUwV490OqC2kQUrinGwZyQHTieuXRA=="}, "Microsoft.Win32.Registry/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-q+eLtROUAQ3OxYA5mpQrgyFgzLQxIyrfT2eLpYX5IEPlHmIio2nh4F5bgOaQoGOV865kFKZZso9Oq9RlazvXtg=="}, "NETStandard.Library/1.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-ypsCvIdCZ4IoYASJHt6tF2fMo7N30NLgV1EbmC+snO490OMl9FvVxmumw14rhReWU3j3g7BYudG6YCrchwHJlA=="}, "runtime.native.System/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-QfS/nQI7k/BLgmLrw7qm7YBoULEvgWnPI+cYsbfCVFTW8Aj+i8JhccxcFMu1RWms0YZzF+UHguNBK4Qn89e2Sg=="}, "runtime.native.System.IO.Compression/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-Ob7nvnJBox1aaB222zSVZSkf4WrebPG4qFscfK7vmD7P7NxoSxACQLtO7ytWpqXDn2wcd/+45+EAZ7xjaPip8A=="}, "runtime.native.System.Net.Http/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Nh0UPZx2Vifh8r+J+H2jxifZUD3sBrmolgiFWJd2yiNrxO0xTa6bAw3YwRn1VOiSen/tUXMS31ttNItCZ6lKuA=="}, "runtime.native.System.Net.Security/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Az6Ff6rZFb8nYGAaejFR6jr8ktt9f3e1Q/yKdw0pwHNTLaO/1eCAC9vzBoR9YAb0QeZD6fZXl1A9tRB5stpzXA=="}, "runtime.native.System.Security.Cryptography/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-2CQK0jmO6Eu7ZeMgD+LOFbNJSXHFVQbCJJkEyEwowh1SCgYnrn9W9RykMfpeeVGw7h4IBvYikzpGUlmZTUafJw=="}, "System.AppContext/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-3QjO4jNV7PdKkmQAVp9atA+usVnKRwI3Kx1nMwJ93T0LcQfx7pKAYk0nKz5wn1oP5iqlhZuy6RXOFdhr7rDwow=="}, "System.Buffers/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-msXumHfjjURSkvxUjYuq4N2ghHoRi2VpXcKMA7gK6ujQfU3vGpl+B6ld0ATRg+FZFpRyA6PgEPA+VlIkTeNf2w=="}, "System.Collections/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-YUJGz6eFKqS0V//mLt25vFGrrCvOnsXjlvFQs+KimpwNxug9x0Pzy4PlFMU3Q2IzqAa9G2L4LsK3+9vCBK7oTg=="}, "System.Collections.Concurrent/4.0.12": {"type": "package", "serviceable": true, "sha512": "sha512-2gBcbb3drMLgxlI0fBfxMA31ec6AEyYCHygGse4vxceJan8mRIWeKJ24BFzN7+bi/NFTgdIgufzb94LWO5EERQ=="}, "System.Collections.Immutable/1.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-Cma8cBW6di16ZLibL8LYQ+cLjGzoKxpOTu/faZfDcx94ZjAGq6Nv5RO7+T1YZXqEXTZP9rt1wLVEONVpURtUqw=="}, "System.ComponentModel/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-oBZFnm7seFiVfugsIyOvQCWobNZs7FzqDV/B7tx20Ep/l3UUFCPDkdTnCNaJZTU27zjeODmy2C/cP60u3D4c9w=="}, "System.ComponentModel.Annotations/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-rhnz80h8NnHJzoi0nbQJLRR2cJznyqG168q1bgoSpe5qpaME2SguXzuEzpY68nFCi2kBgHpbU4bRN2cP3unYRA=="}, "System.Console/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qSKUSOIiYA/a0g5XXdxFcUFmv1hNICBD7QZ0QhGYVipPIhvpiydY8VZqr1thmCXvmn8aipMg64zuanB4eotK9A=="}, "System.Diagnostics.Debug/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-w5U95fVKHY4G8ASs/K5iK3J5LY+/dLFd4vKejsnI/ZhBsWS9hQakfx3Zr7lRWKg4tAw9r4iktyvsTagWkqYCiw=="}, "System.Diagnostics.DiagnosticSource/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-YKglnq4BMTJxfcr6nuT08g+yJ0UxdePIHxosiLuljuHIUR6t4KhFsyaHOaOc1Ofqp0PUvJ0EmcgiEz6T7vEx3w=="}, "System.Diagnostics.FileVersionInfo/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qjF74OTAU+mRhLaL4YSfiWy3vj6T3AOz8AW37l5zCwfbBfj0k7E94XnEsRaf2TnhE/7QaV6Hvqakoy2LoV8MVg=="}, "System.Diagnostics.Process/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-mpVZ5bnlSs3tTeJ6jYyDJEIa6tavhAd88lxq1zbYhkkCu0Pno2+gHXcvZcoygq2d8JxW3gojXqNJMTAshduqZA=="}, "System.Diagnostics.StackTrace/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-6i2EbRq0lgGfiZ+FDf0gVaw9qeEU+7IS2+wbZJmFVpvVzVOgZEt0ScZtyenuBvs6iDYbGiF51bMAa0oDP/tujQ=="}, "System.Diagnostics.Tools/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-xBfJ8pnd4C17dWaC9FM6aShzbJcRNMChUMD42I6772KGGrqaFdumwhn9OdM68erj1ueNo3xdQ1EwiFjK5k8p0g=="}, "System.Diagnostics.Tracing/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-vDN1PoMZCkkdNjvZLql592oYJZgS7URcJzJ7bxeBgGtx5UtR5leNm49VmfHGqIffX4FKacHbI3H6UyNSHQknBg=="}, "System.Dynamic.Runtime/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-db34f6LHYM0U0JpE+sOmjar27BnqTVkbLJhgfwMpTdgTigG/Hna3m2MYVwnFzGGKnEJk2UXFuoVTr8WUbU91/A=="}, "System.Globalization/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-B95h0YLEL2oSnwF/XjqSWKnwKOy/01VWkNlsCeMTFJLLabflpGV26nK164eRs5GiaRSBGpOxQ3pKoSnnyZN5pg=="}, "System.Globalization.Calendars/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-L1c6IqeQ88vuzC1P81JeHmHA8mxq8a18NUBNXnIY/BVb+TCyAaGIFbhpZt60h9FJNmisymoQkHEFSE9Vslja1Q=="}, "System.Globalization.Extensions/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KKo23iKeOaIg61SSXwjANN7QYDr/3op3OWGGzDzz7mypx0Za0fZSeG0l6cco8Ntp8YMYkIQcAqlk8yhm5/Uhcg=="}, "System.IO/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-3KlTJceQc3gnGIaHZ7UBZO26SHL1SHE4ddrmiwumFnId+CEHP+O8r386tZKaE6zlk5/mF8vifMBzHj9SaXN+mQ=="}, "System.IO.Compression/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-TjnBS6eztThSzeSib+WyVbLzEdLKUcEHN69VtS3u8aAsSc18FU6xCZlNWWsEd8SKcXAE+y1sOu7VbU8sUeM0sg=="}, "System.IO.Compression.ZipFile/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-hBQYJzfTbQURF10nLhd+az2NHxsU6MU7AB8RUf4IolBP5lOAm4Luho851xl+CqslmhI5ZH/el8BlngEk4lBkaQ=="}, "System.IO.FileSystem/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-IBErlVq5jOggAD69bg1t0pJcHaDbJbWNUZTPI96fkYWzwYbN6D9wRHMULLDd9dHsl7C2YsxXL31LMfPI1SWt8w=="}, "System.IO.FileSystem.Primitives/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-kWkKD203JJKxJeE74p8aF8y4Qc9r9WQx4C0cHzHPrY3fv/L/IhWnyCHaFJ3H1QPOH6A93whlQ2vG5nHlBDvzWQ=="}, "System.IO.FileSystem.Watcher/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qM4Wr3La+RYb/03B0mZZjbA7tHsGzDffnuXP8Sl48HW2JwCjn3kfD5qdw0sqyNNowUipcJMi9/q6sMUrOIJ6UQ=="}, "System.IO.MemoryMappedFiles/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Xqj4xaFAnLVpss9ZSUIvB/VdJAA7GxZDnFGDKJfiGAnZ5VnFROn6eOHWepFpujCYTsh6wlZ3B33bqYkF0QJ7Eg=="}, "System.IO.UnmanagedMemoryStream/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-wcq0kXcpfJwdl1Y4/ZjDk7Dhx5HdLyRYYWYmD8Nn8skoGYYQd2BQWbXwjWSczip8AL4Z57o2dWWXAl4aABAKiQ=="}, "System.Linq/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-bQ0iYFOQI0nuTnt+NQADns6ucV4DUvMdwN6CbkB1yj8i7arTGiTN5eok1kQwdnnNWSDZfIUySQY+J3d5KjWn0g=="}, "System.Linq.Expressions/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-I+y02iqkgmCAyfbqOmSDOgqdZQ5tTj80Akm5BPSS8EeB0VGWdy6X1KCoYe8Pk6pwDoAKZUOdLVxnTJcExiv5zw=="}, "System.Linq.Parallel/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-J7XCa7n2cFn32uLbtceXfBFhgCk5M++50lylHKNbqTiJkw5y4Tglpi6amuJNPCvj9bLzNSI7rs1fi4joLMNRgg=="}, "System.Linq.Queryable/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Yn/WfYe9RoRfmSLvUt2JerP0BTGGykCZkQPgojaxgzF2N0oPo+/AhB8TXOpdCcNlrG3VRtsamtK2uzsp3cqRVw=="}, "System.Net.Http/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-ULq9g3SOPVuupt+Y3U+A37coXzdNisB1neFCSKzBwo182u0RDddKJF8I5+HfyXqK6OhJPgeoAwWXrbiUXuRDsg=="}, "System.Net.NameResolution/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JdqRdM1Qym3YehqdKIi5LHrpypP4JMfxKQSNCJ2z4WawkG0il+N3XfNeJOxll2XrTnG7WgYYPoeiu/KOwg0DQw=="}, "System.Net.Primitives/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-hVvfl4405DRjA2408luZekbPhplJK03j2Y2lSfMlny7GHXlkByw1iLnc9mgKW0GdQn73vvMcWrWewAhylXA4Nw=="}, "System.Net.Requests/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-vxGt7C0cZixN+VqoSW4Yakc1Y9WknmxauDqzxgpw/FnBdz4kQNN51l4wxdXX5VY1xjqy//+G+4CvJWp1+f+y6Q=="}, "System.Net.Security/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-uM1JaYJciCc2w7efD6du0EpQ1n5ZQqE6/P43/aI4H5E59qvP+wt3l70KIUF/Ha7NaeXGoGNFPVO0MB80pVHk2g=="}, "System.Net.Sockets/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-xAz0N3dAV/aR/9g8r0Y5oEqU1JRsz29F5EGb/WVHmX3jVSLqi2/92M5hTad2aNWovruXrJpJtgZ9fccPMG9uSw=="}, "System.Net.WebHeaderCollection/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-XX2TIAN+wBSAIV51BU2FvvXMdstUa8b0FBSZmDWjZdwUMmggQSifpTOZ5fNH20z9ZCg2fkV1L5SsZnpO2RQDRQ=="}, "System.Numerics.Vectors/4.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-Ex1NSKycC2wi5XBMWUGWPc3lumh6OQWFFmmpZFZz0oLht5lQ+wWPHVZumOrMJuckfUiVMd4p67BrkBos8lcF+Q=="}, "System.ObjectModel/4.0.12": {"type": "package", "serviceable": true, "sha512": "sha512-tAgJM1xt3ytyMoW4qn4wIqgJYm7L7TShRZG4+Q4Qsi2PCcj96pXN7nRywS9KkB3p/xDUjc2HSwP9SROyPYDYKQ=="}, "System.Reflection/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-JCKANJ0TI7kzoQzuwB/OoJANy1Lg338B6+JVacPl4TpUwi3cReg3nMLplMq2uqYfHFQpKIlHAUVAJlImZz/4ng=="}, "System.Reflection.DispatchProxy/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-GPPgWoSxQEU3aCKSOvsAc1dhTTi4iq92PUVEVfnGPGwqCf6synaAJGYLKMs5E3CuRfel8ufACWUijXqDpOlGrA=="}, "System.Reflection.Emit/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-P2wqAj72fFjpP6wb9nSfDqNBMab+2ovzSDzUZK7MVIm54tBJEPr9jWfSjjoTpPwj1LeKcmX3vr0ttyjSSFM47g=="}, "System.Reflection.Emit.ILGeneration/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Ov6dU8Bu15Bc7zuqttgHF12J5lwSWyTf1S+FJouUXVMSqImLZzYaQ+vRr1rQ0OZ0HqsrwWl4dsKHELckQkVpgA=="}, "System.Reflection.Emit.Lightweight/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-sSzHHXueZ5Uh0OLpUQprhr+ZYJrLPA2Cmr4gn0wj9+FftNKXx8RIMKvO9qnjk2ebPYUjZ+F2ulGdPOsvj+MEjA=="}, "System.Reflection.Extensions/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-GYrtRsZcMuHF3sbmRHfMYpvxZoIN2bQGrYGerUiWLEkqdEUQZhH3TRSaC/oI4wO0II1RKBPlpIa1TOMxIcOOzQ=="}, "System.Reflection.Metadata/1.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-jMSCxA4LSyKBGRDm/WtfkO03FkcgRzHxwvQRib1bm2GZ8ifKM1MX1al6breGCEQK280mdl9uQS7JNPXRYk90jw=="}, "System.Reflection.Primitives/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-4inTox4wTBaDhB7V3mPvp9XlCbeGYWVEM9/fXALd52vNEAVisc1BoVWQPuUuD0Ga//dNbA/WeMy9u9mzLxGTHQ=="}, "System.Reflection.TypeExtensions/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-tsQ/ptQ3H5FYfON8lL4MxRk/8kFyE0A+tGPXmVP967cT/gzLHYxIejIYSxp4JmIeFHVP78g/F2FE1mUUTbDtrg=="}, "System.Resources.Reader/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VX1iHAoHxgrLZv+nq/9drCZI6Q4SSCzSVyUm1e0U60sqWdj6XhY7wvKmy3RvsSal9h+/vqSWwxxJsm0J4vn/jA=="}, "System.Resources.ResourceManager/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-TxwVeUNoTgUOdQ09gfTjvW411MF+w9MBYL7AtNVc+HtBCFlutPLhUCdZjNkjbhj3bNQWMdHboF0KIWEOjJssbA=="}, "System.Runtime/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-v6c/4Yaa9uWsq+JMhnOFewrYkgdNHNG2eMKuNqRn8P733rNXeRCGvV5FkkjBXn2dbVkPXOsO0xjsEeM1q2zC0g=="}, "System.Runtime.Extensions/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-CUOHjTT/vgP0qGW22U4/hDlOqXmcPq5YicBaXdUR2UiUoLwBT+olO6we4DVbq57jeX5uXH2uerVZhf0qGj+sVQ=="}, "System.Runtime.Handles/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-nCJvEKguXEvk2ymk1gqj625vVnlK3/xdGzx0vOKicQkoquaTBJTP13AIYkocSUwHCLNBwUbXTqTWGDxBTWpt7g=="}, "System.Runtime.InteropServices/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-16eu3kjHS633yYdkjwShDHZLRNMKVi/s0bY8ODiqJ2RfMhDMAwxZaUaWVnZ2P71kr/or+X9o/xFWtNqz8ivieQ=="}, "System.Runtime.InteropServices.RuntimeInformation/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-hWPhJxc453RCa8Z29O91EmfGeZIHX1ZH2A8L6lYQVSaKzku2DfArSfMEb1/MYYzPQRJZeu0c9dmYeJKxW5Fgng=="}, "System.Runtime.Loader/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-4UN78GOVU/mbDFcXkEWtetJT/sJ0yic2gGk1HSlSpWI0TDf421xnrZTDZnwNBapk1GQeYN7U1lTj/aQB1by6ow=="}, "System.Runtime.Numerics/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-+XbKFuzdmLP3d1o9pdHu2nxjNr2OEPqGzKeegPLCUMM71a0t50A/rOcIRmGs9wR7a8KuHX6hYs/7/TymIGLNqg=="}, "System.Security.Claims/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-4Jlp0OgJLS/Voj1kyFP6MJlIYp3crgfH8kNQk2p7+4JYfc1aAmh9PZyAMMbDhuoolGNtux9HqSOazsioRiDvCw=="}, "System.Security.Cryptography.Algorithms/4.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-8JQFxbLVdrtIOKMDN38Fn0GWnqYZw/oMlwOUG/qz1jqChvyZlnUmu+0s7wLx7JYua/nAXoESpHA3iw11QFWhXg=="}, "System.Security.Cryptography.Cng/4.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-cUJ2h+ZvONDe28Szw3st5dOHdjndhJzQ2WObDEXAWRPEQBtVItVoxbXM/OEsTthl3cNn2dk2k0I3y45igCQcLw=="}, "System.Security.Cryptography.Csp/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/i1Usuo4PgAqgbPNC0NjbO3jPW//BoBlTpcWFD1EHVbidH21y4c1ap5bbEMSGAXjAShhMH4abi/K8fILrnu4BQ=="}, "System.Security.Cryptography.Encoding/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FbKgE5MbxSQMPcSVRgwM6bXN3GtyAh04NkV8E5zKCBE26X0vYW0UtTa2FIgkH33WVqBVxRgxljlVYumWtU+HcQ=="}, "System.Security.Cryptography.OpenSsl/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-HUG/zNUJwEiLkoURDixzkzZdB5yGA5pQhDP93ArOpDPQMteURIGERRNzzoJlmTreLBWr5lkFSjjMSk8ySEpQMw=="}, "System.Security.Cryptography.Primitives/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Wkd7QryWYjkQclX0bngpntW5HSlMzeJU24UaLJQ7YTfI8ydAVAaU2J+HXLLABOVJlKTVvAeL0Aj39VeTe7L+oA=="}, "System.Security.Cryptography.X509Certificates/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-4HEfsQIKAhA1+ApNn729Gi09zh+lYWwyIuViihoMDWp1vQnEkL2ct7mAbhBlLYm+x/L4Rr/pyGge1lIY635e0w=="}, "System.Security.Principal/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-On+SKhXY5rzxh/S8wlH1Rm0ogBlu7zyHNxeNBiXauNrhHRXAe9EuX8Yl5IOzLPGU5Z4kLWHMvORDOCG8iu9hww=="}, "System.Security.Principal.Windows/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-iFx15AF3RMEPZn3COh8+Bb2Thv2zsmLd93RchS1b8Mj5SNYeGqbYNCSn5AES1+gq56p4ujGZPrl0xN7ngkXOHg=="}, "System.Text.Encoding/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-U3gGeMlDZXxCEiY4DwVLSacg+DFWCvoiX+JThA/rvw37Sqrku7sEFeVBBBMBnfB6FeZHsyDx85HlKL19x0HtZA=="}, "System.Text.Encoding.CodePages/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-h4z6rrA/hxWf4655D18IIZ0eaLRa3tQC/j+e26W+VinIHY0l07iEXaAvO0YSYq3MvCjMYy8Zs5AdC1sxNQOB7Q=="}, "System.Text.Encoding.Extensions/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-jtbiTDtvfLYgXn8PTfWI+SiBs51rrmO4AAckx4KR6vFK9Wzf6tI8kcRdsYQNwriUeQ1+CtQbM1W4cMbLXnj/OQ=="}, "System.Text.RegularExpressions/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-i88YCXpRTjCnoSQZtdlHkAOx4KNNik4hMy83n0+Ftlb7jvV6ZiZWMpnEZHhjBp6hQVh8gWd/iKNPzlPF7iyA2g=="}, "System.Threading/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-N+3xqIcg3VDKyjwwCGaZ9HawG9aC6cSDI+s7ROma310GQo8vilFZa86hqKppwTHleR/G0sfOzhvgnUxWCR/DrQ=="}, "System.Threading.Overlapped/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-f7aLuLkBoCQM2kng7zqLFBXz9Gk48gDK8lk1ih9rH/1arJJzZK9gJwNvPDhL6Ps/l6rwOr8jw+4FCHL0KKWiEg=="}, "System.Threading.Tasks/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-k1S4Gc6IGwtHGT8188RSeGaX86Qw/wnrgNLshJvsdNUOPP9etMmo8S07c+UlOAx4K/xLuN9ivA1bD0LVurtIxQ=="}, "System.Threading.Tasks.Dataflow/4.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-2hRjGu2r2jxRZ55wmcHO/WbdX+YAOz9x6FE8xqkHZgPaoFMKQZRe9dk8xTZIas8fRjxRmzawnTEWIrhlM+Un7w=="}, "System.Threading.Tasks.Extensions/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-pH4FZDsZQ/WmgJtN4LWYmRdJAEeVkyriSwrv2Teoe5FOU0Yxlb6II6GL8dBPOfRmutHGATduj3ooMt7dJ2+i+w=="}, "System.Threading.Tasks.Parallel/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-7Pc9t25bcynT9FpMvkUw4ZjYwUiGup/5cJFW72/5MgCG+np2cfVUMdh29u8d7onxX7d8PS3J+wL73zQRqkdrSA=="}, "System.Threading.Thread/4.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-gIdJqDXlOr5W9zeqFErLw3dsOsiShSCYtF9SEHitACycmvNvY8odf9kiKvp6V7aibc8C4HzzNBkWXjyfn7plbQ=="}, "System.Threading.ThreadPool/4.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-IMXgB5Vf/5Qw1kpoVgJMOvUO1l32aC+qC3OaIZjWJOjvcxuxNWOK2ZTWWYXfij22NHxT2j1yWX5vlAeQWld9vA=="}, "System.Threading.Timer/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-saGfUV8uqVW6LeURiqxcGhZ24PzuRNaUBtbhVeuUAvky1naH395A/1nY0P2bWvrw/BreRtIB/EzTDkGBpqCwEw=="}, "System.Xml.ReaderWriter/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-ZIiLPsf67YZ9zgr31vzrFaYQqxRPX9cVHjtPSnmx4eN6lbS/yEyYNr2vs1doGDEscF0tjCZFsk9yUg1sC9e8tg=="}, "System.Xml.XDocument/4.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-Mk2mKmPi0nWaoiYeotq1dgeNK1fqWh61+EK+w4Wu8SWuTYLzpUnschb59bJtGywaPq7SmTuPf44wrXRwbIrukg=="}, "System.Xml.XmlDocument/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-2eZu6IP+etFVBBFUFzw2w6J21DqIN5eL9Y8r8JfJWUmV28Z5P0SNU01oCisVHQgHsDhHPnmq2s1hJrJCFZWloQ=="}, "System.Xml.XPath/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-UWd1H+1IJ9Wlq5nognZ/XJdyj8qPE4XufBUkAW59ijsCPjZkZe0MUzKKJFBr+ZWBe5Wq1u1d5f2CYgE93uH7DA=="}, "System.Xml.XPath.XDocument/4.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-FLhdYJx4331oGovQypQ8JIw2kEmNzCsjVOVYY/16kZTUoquZG85oVn7yUhBE2OZt1yGPSXAL0HTEfzjlbNpM7Q=="}}}