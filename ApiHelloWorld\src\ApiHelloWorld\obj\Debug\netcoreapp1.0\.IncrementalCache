{"inputs": ["C:\\ASP.NET\\ApiHelloWorld\\src\\ApiHelloWorld\\project.json", "C:\\ASP.NET\\ApiHelloWorld\\src\\ApiHelloWorld\\project.lock.json", "C:\\ASP.NET\\ApiHelloWorld\\src\\ApiHelloWorld\\Program.cs", "C:\\ASP.NET\\ApiHelloWorld\\src\\ApiHelloWorld\\Startup.cs", "C:\\ASP.NET\\ApiHelloWorld\\src\\ApiHelloWorld\\Controllers\\ApiHelloWorldController.cs", "C:\\ASP.NET\\ApiHelloWorld\\src\\ApiHelloWorld\\Controllers\\ApiHelloWorldWithValueController.cs", "C:\\ASP.NET\\ApiHelloWorld\\src\\ApiHelloWorld\\Controllers\\ValuesController.cs"], "outputs": ["C:\\ASP.NET\\ApiHelloWorld\\src\\ApiHelloWorld\\bin\\Debug\\netcoreapp1.0\\ApiHelloWorld.dll", "C:\\ASP.NET\\ApiHelloWorld\\src\\ApiHelloWorld\\bin\\Debug\\netcoreapp1.0\\ApiHelloWorld.pdb", "C:\\ASP.NET\\ApiHelloWorld\\src\\ApiHelloWorld\\bin\\Debug\\netcoreapp1.0\\ApiHelloWorld.deps.json", "C:\\ASP.NET\\ApiHelloWorld\\src\\ApiHelloWorld\\bin\\Debug\\netcoreapp1.0\\ApiHelloWorld.runtimeconfig.json"], "buildArguments": {"version-suffix": null}}