{"locked": false, "version": 2, "targets": {".NETCoreApp,Version=v1.0": {"Libuv/1.9.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1"}, "runtimeTargets": {"runtimes/debian-x64/native/libuv.so": {"assetType": "native", "rid": "debian-x64"}, "runtimes/fedora-x64/native/libuv.so": {"assetType": "native", "rid": "fedora-x64"}, "runtimes/opensuse-x64/native/libuv.so": {"assetType": "native", "rid": "opensuse-x64"}, "runtimes/osx/native/libuv.dylib": {"assetType": "native", "rid": "osx"}, "runtimes/rhel-x64/native/libuv.so": {"assetType": "native", "rid": "rhel-x64"}, "runtimes/win7-arm/native/libuv.dll": {"assetType": "native", "rid": "win7-arm"}, "runtimes/win7-x64/native/libuv.dll": {"assetType": "native", "rid": "win7-x64"}, "runtimes/win7-x86/native/libuv.dll": {"assetType": "native", "rid": "win7-x86"}}}, "Microsoft.AspNetCore.Antiforgery/1.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.DataProtection": "1.0.0", "Microsoft.AspNetCore.Http.Abstractions": "1.0.0", "Microsoft.AspNetCore.WebUtilities": "1.0.0", "Microsoft.Extensions.ObjectPool": "1.0.0"}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.Antiforgery.dll": {}}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.Antiforgery.dll": {}}}, "Microsoft.AspNetCore.Authorization/1.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "1.0.0", "Microsoft.Extensions.Options": "1.0.0", "System.Security.Claims": "4.0.1"}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.Authorization.dll": {}}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.Authorization.dll": {}}}, "Microsoft.AspNetCore.Cors/1.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Extensions": "1.0.0", "Microsoft.Extensions.Configuration.Abstractions": "1.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "1.0.0", "Microsoft.Extensions.Options": "1.0.0"}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.Cors.dll": {}}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.Cors.dll": {}}}, "Microsoft.AspNetCore.Cryptography.Internal/1.0.0": {"type": "package", "dependencies": {"System.Diagnostics.Debug": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Threading": "4.0.11"}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.Cryptography.Internal.dll": {}}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.Cryptography.Internal.dll": {}}}, "Microsoft.AspNetCore.DataProtection/1.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "1.0.0", "Microsoft.AspNetCore.DataProtection.Abstractions": "1.0.0", "Microsoft.AspNetCore.Hosting.Abstractions": "1.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "1.0.0", "Microsoft.Extensions.Logging.Abstractions": "1.0.0", "Microsoft.Extensions.Options": "1.0.0", "Microsoft.Win32.Registry": "4.0.0", "System.IO.FileSystem": "4.0.1", "System.Reflection.Extensions": "4.0.1", "System.Security.Claims": "4.0.1", "System.Security.Cryptography.X509Certificates": "4.1.0", "System.Security.Principal.Windows": "4.0.0", "System.Xml.XDocument": "4.0.11"}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.DataProtection.dll": {}}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.DataProtection.dll": {}}}, "Microsoft.AspNetCore.DataProtection.Abstractions/1.0.0": {"type": "package", "dependencies": {"System.ComponentModel": "4.0.1", "System.Diagnostics.Debug": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime.Extensions": "4.1.0"}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.DataProtection.Abstractions.dll": {}}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.DataProtection.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Diagnostics.Abstractions/1.0.0": {"type": "package", "dependencies": {"System.Resources.ResourceManager": "4.0.1"}, "compile": {"lib/netstandard1.0/Microsoft.AspNetCore.Diagnostics.Abstractions.dll": {}}, "runtime": {"lib/netstandard1.0/Microsoft.AspNetCore.Diagnostics.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Hosting/1.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "1.0.0", "Microsoft.AspNetCore.Hosting.Server.Abstractions": "1.0.0", "Microsoft.AspNetCore.Http": "1.0.0", "Microsoft.AspNetCore.Http.Extensions": "1.0.0", "Microsoft.Extensions.Configuration": "1.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "1.0.0", "Microsoft.Extensions.DependencyInjection": "1.0.0", "Microsoft.Extensions.FileProviders.Physical": "1.0.0", "Microsoft.Extensions.Logging": "1.0.0", "Microsoft.Extensions.Options": "1.0.0", "Microsoft.Extensions.PlatformAbstractions": "1.0.0", "System.Console": "4.0.0", "System.Diagnostics.DiagnosticSource": "4.0.0", "System.Diagnostics.StackTrace": "4.0.1", "System.Reflection.Extensions": "4.0.1", "System.Reflection.Metadata": "1.3.0", "System.Runtime.InteropServices.RuntimeInformation": "4.0.0"}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.Hosting.dll": {}}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.Hosting.dll": {}}}, "Microsoft.AspNetCore.Hosting.Abstractions/1.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Hosting.Server.Abstractions": "1.0.0", "Microsoft.AspNetCore.Http.Abstractions": "1.0.0", "Microsoft.Extensions.Configuration.Abstractions": "1.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "1.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "1.0.0", "Microsoft.Extensions.Logging.Abstractions": "1.0.0"}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.Hosting.Abstractions.dll": {}}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.Hosting.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/1.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Features": "1.0.0", "Microsoft.Extensions.Configuration.Abstractions": "1.0.0"}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.Hosting.Server.Abstractions.dll": {}}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.Hosting.Server.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Html.Abstractions/1.0.0": {"type": "package", "dependencies": {"System.Collections": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Text.Encodings.Web": "4.0.0"}, "compile": {"lib/netstandard1.0/Microsoft.AspNetCore.Html.Abstractions.dll": {}}, "runtime": {"lib/netstandard1.0/Microsoft.AspNetCore.Html.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Http/1.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "1.0.0", "Microsoft.AspNetCore.WebUtilities": "1.0.0", "Microsoft.Extensions.ObjectPool": "1.0.0", "Microsoft.Extensions.Options": "1.0.0", "Microsoft.Net.Http.Headers": "1.0.0", "System.Buffers": "4.0.0", "System.Threading": "4.0.11"}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.Http.dll": {}}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.Http.dll": {}}}, "Microsoft.AspNetCore.Http.Abstractions/1.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Features": "1.0.0", "System.Globalization.Extensions": "4.0.1", "System.Linq.Expressions": "4.1.0", "System.Reflection.TypeExtensions": "4.1.0", "System.Runtime.InteropServices": "4.1.0", "System.Text.Encodings.Web": "4.0.0"}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.Http.Abstractions.dll": {}}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.Http.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Http.Extensions/1.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "1.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "1.0.0", "Microsoft.Net.Http.Headers": "1.0.0", "System.Buffers": "4.0.0", "System.IO.FileSystem": "4.0.1"}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.Http.Extensions.dll": {}}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.Http.Extensions.dll": {}}}, "Microsoft.AspNetCore.Http.Features/1.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "1.0.0", "System.Collections": "4.0.11", "System.ComponentModel": "4.0.1", "System.Linq": "4.1.0", "System.Net.Primitives": "4.0.11", "System.Net.WebSockets": "4.0.0", "System.Runtime.Extensions": "4.1.0", "System.Security.Claims": "4.0.1", "System.Security.Cryptography.X509Certificates": "4.1.0", "System.Security.Principal": "4.0.1"}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.Http.Features.dll": {}}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.Http.Features.dll": {}}}, "Microsoft.AspNetCore.HttpOverrides/1.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Extensions": "1.0.0", "Microsoft.Extensions.Logging.Abstractions": "1.0.0", "Microsoft.Extensions.Options": "1.0.0"}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.HttpOverrides.dll": {}}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.HttpOverrides.dll": {}}}, "Microsoft.AspNetCore.JsonPatch/1.0.0": {"type": "package", "dependencies": {"Microsoft.CSharp": "4.0.1", "Newtonsoft.Json": "9.0.1", "System.Collections.Concurrent": "4.0.12", "System.ComponentModel.TypeConverter": "4.1.0", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.Linq": "4.1.0", "System.Reflection.Extensions": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Serialization.Primitives": "4.1.1", "System.Text.Encoding.Extensions": "4.0.11"}, "compile": {"lib/netstandard1.1/Microsoft.AspNetCore.JsonPatch.dll": {}}, "runtime": {"lib/netstandard1.1/Microsoft.AspNetCore.JsonPatch.dll": {}}}, "Microsoft.AspNetCore.Localization/1.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Extensions": "1.0.0", "Microsoft.Extensions.Globalization.CultureInfoCache": "1.0.0", "Microsoft.Extensions.Localization.Abstractions": "1.0.0", "Microsoft.Extensions.Options": "1.0.0"}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.Localization.dll": {}}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.Localization.dll": {}}}, "Microsoft.AspNetCore.Mvc/1.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Mvc.ApiExplorer": "1.0.0", "Microsoft.AspNetCore.Mvc.Cors": "1.0.0", "Microsoft.AspNetCore.Mvc.DataAnnotations": "1.0.0", "Microsoft.AspNetCore.Mvc.Formatters.Json": "1.0.0", "Microsoft.AspNetCore.Mvc.Localization": "1.0.0", "Microsoft.AspNetCore.Mvc.Razor": "1.0.0", "Microsoft.AspNetCore.Mvc.TagHelpers": "1.0.0", "Microsoft.AspNetCore.Mvc.ViewFeatures": "1.0.0", "Microsoft.Extensions.Caching.Memory": "1.0.0", "Microsoft.Extensions.DependencyInjection": "1.0.0"}, "compile": {"lib/netstandard1.6/Microsoft.AspNetCore.Mvc.dll": {}}, "runtime": {"lib/netstandard1.6/Microsoft.AspNetCore.Mvc.dll": {}}}, "Microsoft.AspNetCore.Mvc.Abstractions/1.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Routing.Abstractions": "1.0.0", "Microsoft.CSharp": "4.0.1", "Microsoft.Net.Http.Headers": "1.0.0", "System.ComponentModel.TypeConverter": "4.1.0", "System.Reflection.Extensions": "4.0.1", "System.Text.Encoding.Extensions": "4.0.11"}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.Mvc.Abstractions.dll": {}}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.Mvc.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Mvc.ApiExplorer/1.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Mvc.Core": "1.0.0"}, "compile": {"lib/netstandard1.6/Microsoft.AspNetCore.Mvc.ApiExplorer.dll": {}}, "runtime": {"lib/netstandard1.6/Microsoft.AspNetCore.Mvc.ApiExplorer.dll": {}}}, "Microsoft.AspNetCore.Mvc.Core/1.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Authorization": "1.0.0", "Microsoft.AspNetCore.Hosting.Abstractions": "1.0.0", "Microsoft.AspNetCore.Http": "1.0.0", "Microsoft.AspNetCore.Mvc.Abstractions": "1.0.0", "Microsoft.AspNetCore.Routing": "1.0.0", "Microsoft.Extensions.DependencyModel": "1.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "1.0.0", "Microsoft.Extensions.Logging.Abstractions": "1.0.0", "Microsoft.Extensions.PlatformAbstractions": "1.0.0", "System.Buffers": "4.0.0", "System.Diagnostics.DiagnosticSource": "4.0.0", "System.Text.Encoding": "4.0.11"}, "compile": {"lib/netstandard1.6/Microsoft.AspNetCore.Mvc.Core.dll": {}}, "runtime": {"lib/netstandard1.6/Microsoft.AspNetCore.Mvc.Core.dll": {}}}, "Microsoft.AspNetCore.Mvc.Cors/1.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Cors": "1.0.0", "Microsoft.AspNetCore.Mvc.Core": "1.0.0"}, "compile": {"lib/netstandard1.6/Microsoft.AspNetCore.Mvc.Cors.dll": {}}, "runtime": {"lib/netstandard1.6/Microsoft.AspNetCore.Mvc.Cors.dll": {}}}, "Microsoft.AspNetCore.Mvc.DataAnnotations/1.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Mvc.Core": "1.0.0", "Microsoft.Extensions.Localization": "1.0.0", "System.ComponentModel.Annotations": "4.1.0"}, "compile": {"lib/netstandard1.6/Microsoft.AspNetCore.Mvc.DataAnnotations.dll": {}}, "runtime": {"lib/netstandard1.6/Microsoft.AspNetCore.Mvc.DataAnnotations.dll": {}}}, "Microsoft.AspNetCore.Mvc.Formatters.Json/1.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.JsonPatch": "1.0.0", "Microsoft.AspNetCore.Mvc.Core": "1.0.0"}, "compile": {"lib/netstandard1.6/Microsoft.AspNetCore.Mvc.Formatters.Json.dll": {}}, "runtime": {"lib/netstandard1.6/Microsoft.AspNetCore.Mvc.Formatters.Json.dll": {}}}, "Microsoft.AspNetCore.Mvc.Localization/1.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Localization": "1.0.0", "Microsoft.AspNetCore.Mvc.Razor": "1.0.0", "Microsoft.Extensions.DependencyInjection": "1.0.0", "Microsoft.Extensions.Localization": "1.0.0"}, "compile": {"lib/netstandard1.6/Microsoft.AspNetCore.Mvc.Localization.dll": {}}, "runtime": {"lib/netstandard1.6/Microsoft.AspNetCore.Mvc.Localization.dll": {}}}, "Microsoft.AspNetCore.Mvc.Razor/1.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Mvc.Razor.Host": "1.0.0", "Microsoft.AspNetCore.Mvc.ViewFeatures": "1.0.0", "Microsoft.CodeAnalysis.CSharp": "1.3.0", "Microsoft.Extensions.FileProviders.Composite": "1.0.0", "System.Runtime.Loader": "4.0.0", "System.Text.Encoding": "4.0.11"}, "compile": {"lib/netstandard1.6/Microsoft.AspNetCore.Mvc.Razor.dll": {}}, "runtime": {"lib/netstandard1.6/Microsoft.AspNetCore.Mvc.Razor.dll": {}}}, "Microsoft.AspNetCore.Mvc.Razor.Host/1.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Razor.Runtime": "1.0.0", "Microsoft.Extensions.Caching.Memory": "1.0.0", "Microsoft.Extensions.FileProviders.Physical": "1.0.0", "System.Collections.Concurrent": "4.0.12", "System.ComponentModel.TypeConverter": "4.1.0", "System.Reflection.Extensions": "4.0.1", "System.Text.Encoding.Extensions": "4.0.11"}, "compile": {"lib/netstandard1.6/Microsoft.AspNetCore.Mvc.Razor.Host.dll": {}}, "runtime": {"lib/netstandard1.6/Microsoft.AspNetCore.Mvc.Razor.Host.dll": {}}}, "Microsoft.AspNetCore.Mvc.TagHelpers/1.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Mvc.Razor": "1.0.0", "Microsoft.Extensions.Caching.Memory": "1.0.0", "Microsoft.Extensions.FileSystemGlobbing": "1.0.0", "Microsoft.Extensions.Primitives": "1.0.0"}, "compile": {"lib/netstandard1.6/Microsoft.AspNetCore.Mvc.TagHelpers.dll": {}}, "runtime": {"lib/netstandard1.6/Microsoft.AspNetCore.Mvc.TagHelpers.dll": {}}}, "Microsoft.AspNetCore.Mvc.ViewFeatures/1.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Antiforgery": "1.0.0", "Microsoft.AspNetCore.Diagnostics.Abstractions": "1.0.0", "Microsoft.AspNetCore.Html.Abstractions": "1.0.0", "Microsoft.AspNetCore.Mvc.Core": "1.0.0", "Microsoft.AspNetCore.Mvc.DataAnnotations": "1.0.0", "Microsoft.AspNetCore.Mvc.Formatters.Json": "1.0.0", "Microsoft.Extensions.WebEncoders": "1.0.0", "Newtonsoft.Json": "9.0.1", "System.Buffers": "4.0.0", "System.Runtime.Serialization.Primitives": "4.1.1"}, "compile": {"lib/netstandard1.6/Microsoft.AspNetCore.Mvc.ViewFeatures.dll": {}}, "runtime": {"lib/netstandard1.6/Microsoft.AspNetCore.Mvc.ViewFeatures.dll": {}}}, "Microsoft.AspNetCore.Razor/1.0.0": {"type": "package", "dependencies": {"System.Diagnostics.Debug": "4.0.11", "System.Linq": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime.Extensions": "4.1.0", "System.Security.Cryptography.Algorithms": "4.2.0", "System.Threading": "4.0.11", "System.Threading.Thread": "4.0.0"}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.Razor.dll": {}}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.Razor.dll": {}}}, "Microsoft.AspNetCore.Razor.Runtime/1.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Html.Abstractions": "1.0.0", "Microsoft.AspNetCore.Razor": "1.0.0", "System.Collections.Concurrent": "4.0.12", "System.IO.FileSystem": "4.0.1", "System.Reflection": "4.1.0", "System.Reflection.Extensions": "4.0.1", "System.Reflection.TypeExtensions": "4.1.0", "System.Text.RegularExpressions": "4.1.0", "System.Xml.XDocument": "4.0.11"}, "compile": {"lib/netstandard1.5/Microsoft.AspNetCore.Razor.Runtime.dll": {}}, "runtime": {"lib/netstandard1.5/Microsoft.AspNetCore.Razor.Runtime.dll": {}}}, "Microsoft.AspNetCore.Routing/1.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Extensions": "1.0.0", "Microsoft.AspNetCore.Routing.Abstractions": "1.0.0", "Microsoft.Extensions.Logging.Abstractions": "1.0.0", "Microsoft.Extensions.ObjectPool": "1.0.0", "Microsoft.Extensions.Options": "1.0.0", "System.Collections": "4.0.11", "System.Text.RegularExpressions": "4.1.0"}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.Routing.dll": {}}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.Routing.dll": {}}}, "Microsoft.AspNetCore.Routing.Abstractions/1.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "1.0.0", "System.Collections.Concurrent": "4.0.12", "System.Reflection.Extensions": "4.0.1", "System.Threading.Tasks": "4.0.11"}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.Routing.Abstractions.dll": {}}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.Routing.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Server.IISIntegration/1.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "1.0.0", "Microsoft.AspNetCore.Http": "1.0.0", "Microsoft.AspNetCore.Http.Extensions": "1.0.0", "Microsoft.AspNetCore.HttpOverrides": "1.0.0", "Microsoft.Extensions.Logging.Abstractions": "1.0.0", "Microsoft.Extensions.Options": "1.0.0", "System.Security.Principal.Windows": "4.0.0"}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.Server.IISIntegration.dll": {}}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.Server.IISIntegration.dll": {}}}, "Microsoft.AspNetCore.Server.Kestrel/1.0.0": {"type": "package", "dependencies": {"Libuv": "1.9.0", "Microsoft.AspNetCore.Hosting": "1.0.0", "Microsoft.Extensions.Logging.Abstractions": "1.0.0", "System.Buffers": "4.0.0", "System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.Linq": "4.1.0", "System.Net.Primitives": "4.0.11", "System.Numerics.Vectors": "4.1.1", "System.Runtime.Extensions": "4.1.0", "System.Runtime.InteropServices": "4.1.0", "System.Runtime.InteropServices.RuntimeInformation": "4.0.0", "System.Text.Encoding": "4.0.11", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.0.11", "System.Threading.Tasks.Extensions": "4.0.0", "System.Threading.Thread": "4.0.0", "System.Threading.ThreadPool": "4.0.10", "System.Threading.Timer": "4.0.1"}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.Server.Kestrel.dll": {}}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.Server.Kestrel.dll": {}}}, "Microsoft.AspNetCore.WebUtilities/1.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "1.0.0", "System.Buffers": "4.0.0", "System.Collections": "4.0.11", "System.IO": "4.1.0", "System.IO.FileSystem": "4.0.1", "System.Text.Encodings.Web": "4.0.0"}, "compile": {"lib/netstandard1.3/Microsoft.AspNetCore.WebUtilities.dll": {}}, "runtime": {"lib/netstandard1.3/Microsoft.AspNetCore.WebUtilities.dll": {}}}, "Microsoft.CodeAnalysis.Analyzers/1.1.0": {"type": "package"}, "Microsoft.CodeAnalysis.Common/1.3.0": {"type": "package", "dependencies": {"Microsoft.CodeAnalysis.Analyzers": "1.1.0", "System.AppContext": "4.1.0", "System.Collections": "4.0.11", "System.Collections.Concurrent": "4.0.12", "System.Collections.Immutable": "1.2.0", "System.Console": "4.0.0", "System.Diagnostics.Debug": "4.0.11", "System.Diagnostics.FileVersionInfo": "4.0.0", "System.Diagnostics.StackTrace": "4.0.1", "System.Diagnostics.Tools": "4.0.1", "System.Dynamic.Runtime": "4.0.11", "System.Globalization": "4.0.11", "System.IO.FileSystem": "4.0.1", "System.IO.FileSystem.Primitives": "4.0.1", "System.Linq": "4.1.0", "System.Linq.Expressions": "4.1.0", "System.Reflection": "4.1.0", "System.Reflection.Metadata": "1.3.0", "System.Reflection.Primitives": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Runtime.Numerics": "4.0.1", "System.Security.Cryptography.Algorithms": "4.2.0", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.X509Certificates": "4.1.0", "System.Text.Encoding": "4.0.11", "System.Text.Encoding.CodePages": "4.0.1", "System.Text.Encoding.Extensions": "4.0.11", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.0.11", "System.Threading.Tasks.Parallel": "4.0.1", "System.Threading.Thread": "4.0.0", "System.Xml.ReaderWriter": "4.0.11", "System.Xml.XDocument": "4.0.11", "System.Xml.XPath.XDocument": "4.0.1", "System.Xml.XmlDocument": "4.0.1"}, "compile": {"lib/netstandard1.3/Microsoft.CodeAnalysis.dll": {}}, "runtime": {"lib/netstandard1.3/Microsoft.CodeAnalysis.dll": {}}}, "Microsoft.CodeAnalysis.CSharp/1.3.0": {"type": "package", "dependencies": {"Microsoft.CodeAnalysis.Common": "[1.3.0]"}, "compile": {"lib/netstandard1.3/Microsoft.CodeAnalysis.CSharp.dll": {}}, "runtime": {"lib/netstandard1.3/Microsoft.CodeAnalysis.CSharp.dll": {}}}, "Microsoft.CodeAnalysis.VisualBasic/1.3.0": {"type": "package", "dependencies": {"Microsoft.CodeAnalysis.Common": "1.3.0"}, "compile": {"lib/netstandard1.3/_._": {}}, "runtime": {"lib/netstandard1.3/_._": {}}}, "Microsoft.CSharp/4.0.1": {"type": "package", "dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Dynamic.Runtime": "4.0.11", "System.Globalization": "4.0.11", "System.Linq": "4.1.0", "System.Linq.Expressions": "4.1.0", "System.ObjectModel": "4.0.12", "System.Reflection": "4.1.0", "System.Reflection.Extensions": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Reflection.TypeExtensions": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.InteropServices": "4.1.0", "System.Threading": "4.0.11"}, "compile": {"ref/netstandard1.0/Microsoft.CSharp.dll": {}}, "runtime": {"lib/netstandard1.3/Microsoft.CSharp.dll": {}}}, "Microsoft.DotNet.InternalAbstractions/1.0.0": {"type": "package", "dependencies": {"System.AppContext": "4.1.0", "System.Collections": "4.0.11", "System.IO": "4.1.0", "System.IO.FileSystem": "4.0.1", "System.Reflection.TypeExtensions": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.InteropServices": "4.1.0", "System.Runtime.InteropServices.RuntimeInformation": "4.0.0"}, "compile": {"lib/netstandard1.3/Microsoft.DotNet.InternalAbstractions.dll": {}}, "runtime": {"lib/netstandard1.3/Microsoft.DotNet.InternalAbstractions.dll": {}}}, "Microsoft.Extensions.Caching.Abstractions/1.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "1.0.0", "System.Collections": "4.0.11", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.0.11"}, "compile": {"lib/netstandard1.0/Microsoft.Extensions.Caching.Abstractions.dll": {}}, "runtime": {"lib/netstandard1.0/Microsoft.Extensions.Caching.Abstractions.dll": {}}}, "Microsoft.Extensions.Caching.Memory/1.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Caching.Abstractions": "1.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "1.0.0", "Microsoft.Extensions.Options": "1.0.0", "System.Linq": "4.1.0", "System.Threading": "4.0.11"}, "compile": {"lib/netstandard1.3/Microsoft.Extensions.Caching.Memory.dll": {}}, "runtime": {"lib/netstandard1.3/Microsoft.Extensions.Caching.Memory.dll": {}}}, "Microsoft.Extensions.Configuration/1.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "1.0.0", "System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.IO": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime.Extensions": "4.1.0", "System.Runtime.InteropServices": "4.1.0", "System.Threading": "4.0.11"}, "compile": {"lib/netstandard1.1/Microsoft.Extensions.Configuration.dll": {}}, "runtime": {"lib/netstandard1.1/Microsoft.Extensions.Configuration.dll": {}}}, "Microsoft.Extensions.Configuration.Abstractions/1.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "1.0.0", "System.Linq": "4.1.0"}, "compile": {"lib/netstandard1.0/Microsoft.Extensions.Configuration.Abstractions.dll": {}}, "runtime": {"lib/netstandard1.0/Microsoft.Extensions.Configuration.Abstractions.dll": {}}}, "Microsoft.Extensions.Configuration.Binder/1.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "1.0.0", "System.ComponentModel.TypeConverter": "4.1.0"}, "compile": {"lib/netstandard1.1/Microsoft.Extensions.Configuration.Binder.dll": {}}, "runtime": {"lib/netstandard1.1/Microsoft.Extensions.Configuration.Binder.dll": {}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/1.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "1.0.0"}, "compile": {"lib/netstandard1.3/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {}}, "runtime": {"lib/netstandard1.3/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {}}}, "Microsoft.Extensions.Configuration.FileExtensions/1.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "1.0.0", "Microsoft.Extensions.FileProviders.Physical": "1.0.0", "System.AppContext": "4.1.0"}, "compile": {"lib/netstandard1.3/Microsoft.Extensions.Configuration.FileExtensions.dll": {}}, "runtime": {"lib/netstandard1.3/Microsoft.Extensions.Configuration.FileExtensions.dll": {}}}, "Microsoft.Extensions.Configuration.Json/1.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "1.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "1.0.0", "Newtonsoft.Json": "9.0.1", "System.Dynamic.Runtime": "4.0.11", "System.IO.FileSystem": "4.0.1", "System.Runtime.Serialization.Primitives": "4.1.1"}, "compile": {"lib/netstandard1.3/Microsoft.Extensions.Configuration.Json.dll": {}}, "runtime": {"lib/netstandard1.3/Microsoft.Extensions.Configuration.Json.dll": {}}}, "Microsoft.Extensions.DependencyInjection/1.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "1.0.0", "System.Collections": "4.0.11", "System.Collections.Concurrent": "4.0.12", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.0.11"}, "compile": {"lib/netstandard1.1/Microsoft.Extensions.DependencyInjection.dll": {}}, "runtime": {"lib/netstandard1.1/Microsoft.Extensions.DependencyInjection.dll": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/1.0.0": {"type": "package", "dependencies": {"System.ComponentModel": "4.0.1", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.Linq": "4.1.0", "System.Linq.Expressions": "4.1.0", "System.Reflection": "4.1.0", "System.Resources.ResourceManager": "4.0.1"}, "compile": {"lib/netstandard1.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {}}, "runtime": {"lib/netstandard1.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {}}}, "Microsoft.Extensions.DependencyModel/1.0.0": {"type": "package", "dependencies": {"Microsoft.DotNet.InternalAbstractions": "1.0.0", "Newtonsoft.Json": "9.0.1", "System.Diagnostics.Debug": "4.0.11", "System.Dynamic.Runtime": "4.0.11", "System.Linq": "4.1.0"}, "compile": {"lib/netstandard1.6/Microsoft.Extensions.DependencyModel.dll": {}}, "runtime": {"lib/netstandard1.6/Microsoft.Extensions.DependencyModel.dll": {}}}, "Microsoft.Extensions.FileProviders.Abstractions/1.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "1.0.0", "System.IO": "4.1.0", "System.Resources.ResourceManager": "4.0.1"}, "compile": {"lib/netstandard1.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {}}, "runtime": {"lib/netstandard1.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {}}}, "Microsoft.Extensions.FileProviders.Composite/1.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "1.0.0"}, "compile": {"lib/netstandard1.0/Microsoft.Extensions.FileProviders.Composite.dll": {}}, "runtime": {"lib/netstandard1.0/Microsoft.Extensions.FileProviders.Composite.dll": {}}}, "Microsoft.Extensions.FileProviders.Physical/1.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "1.0.0", "Microsoft.Extensions.FileSystemGlobbing": "1.0.0", "System.Collections.Concurrent": "4.0.12", "System.IO.FileSystem": "4.0.1", "System.IO.FileSystem.Watcher": "4.0.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}, "compile": {"lib/netstandard1.3/Microsoft.Extensions.FileProviders.Physical.dll": {}}, "runtime": {"lib/netstandard1.3/Microsoft.Extensions.FileProviders.Physical.dll": {}}}, "Microsoft.Extensions.FileSystemGlobbing/1.0.0": {"type": "package", "dependencies": {"System.Collections": "4.0.11", "System.IO.FileSystem": "4.0.1", "System.Linq": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime.Extensions": "4.1.0"}, "compile": {"lib/netstandard1.3/Microsoft.Extensions.FileSystemGlobbing.dll": {}}, "runtime": {"lib/netstandard1.3/Microsoft.Extensions.FileSystemGlobbing.dll": {}}}, "Microsoft.Extensions.Globalization.CultureInfoCache/1.0.0": {"type": "package", "dependencies": {"System.Collections.Concurrent": "4.0.12", "System.Linq": "4.1.0", "System.Resources.ResourceManager": "4.0.1"}, "compile": {"lib/netstandard1.1/Microsoft.Extensions.Globalization.CultureInfoCache.dll": {}}, "runtime": {"lib/netstandard1.1/Microsoft.Extensions.Globalization.CultureInfoCache.dll": {}}}, "Microsoft.Extensions.Localization/1.0.0": {"type": "package", "dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "1.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "1.0.0", "Microsoft.Extensions.Localization.Abstractions": "1.0.0", "Microsoft.Extensions.Options": "1.0.0", "System.Collections.Concurrent": "4.0.12", "System.Resources.Reader": "4.0.0"}, "compile": {"lib/netstandard1.3/Microsoft.Extensions.Localization.dll": {}}, "runtime": {"lib/netstandard1.3/Microsoft.Extensions.Localization.dll": {}}}, "Microsoft.Extensions.Localization.Abstractions/1.0.0": {"type": "package", "dependencies": {"Microsoft.CSharp": "4.0.1", "System.Globalization": "4.0.11", "System.Resources.ResourceManager": "4.0.1"}, "compile": {"lib/netstandard1.0/Microsoft.Extensions.Localization.Abstractions.dll": {}}, "runtime": {"lib/netstandard1.0/Microsoft.Extensions.Localization.Abstractions.dll": {}}}, "Microsoft.Extensions.Logging/1.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "1.0.0", "Microsoft.Extensions.Logging.Abstractions": "1.0.0", "System.Threading": "4.0.11"}, "compile": {"lib/netstandard1.1/Microsoft.Extensions.Logging.dll": {}}, "runtime": {"lib/netstandard1.1/Microsoft.Extensions.Logging.dll": {}}}, "Microsoft.Extensions.Logging.Abstractions/1.0.0": {"type": "package", "dependencies": {"System.Collections": "4.0.11", "System.Collections.Concurrent": "4.0.12", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.Linq": "4.1.0", "System.Reflection": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime.Extensions": "4.1.0", "System.Runtime.InteropServices": "4.1.0"}, "compile": {"lib/netstandard1.1/Microsoft.Extensions.Logging.Abstractions.dll": {}}, "runtime": {"lib/netstandard1.1/Microsoft.Extensions.Logging.Abstractions.dll": {}}}, "Microsoft.Extensions.Logging.Console/1.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "1.0.0", "Microsoft.Extensions.Logging.Abstractions": "1.0.0", "System.Console": "4.0.0", "System.Runtime.InteropServices.RuntimeInformation": "4.0.0", "System.Threading": "4.0.11"}, "compile": {"lib/netstandard1.3/Microsoft.Extensions.Logging.Console.dll": {}}, "runtime": {"lib/netstandard1.3/Microsoft.Extensions.Logging.Console.dll": {}}}, "Microsoft.Extensions.Logging.Debug/1.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "1.0.0", "System.Diagnostics.Debug": "4.0.11"}, "compile": {"lib/netstandard1.3/Microsoft.Extensions.Logging.Debug.dll": {}}, "runtime": {"lib/netstandard1.3/Microsoft.Extensions.Logging.Debug.dll": {}}}, "Microsoft.Extensions.ObjectPool/1.0.0": {"type": "package", "dependencies": {"System.Diagnostics.Debug": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}, "compile": {"lib/netstandard1.3/Microsoft.Extensions.ObjectPool.dll": {}}, "runtime": {"lib/netstandard1.3/Microsoft.Extensions.ObjectPool.dll": {}}}, "Microsoft.Extensions.Options/1.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "1.0.0", "Microsoft.Extensions.Primitives": "1.0.0", "System.ComponentModel": "4.0.1", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.Linq": "4.1.0", "System.Linq.Expressions": "4.1.0", "System.Reflection": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}, "compile": {"lib/netstandard1.0/Microsoft.Extensions.Options.dll": {}}, "runtime": {"lib/netstandard1.0/Microsoft.Extensions.Options.dll": {}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/1.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "1.0.0", "Microsoft.Extensions.Configuration.Binder": "1.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "1.0.0", "Microsoft.Extensions.Options": "1.0.0"}, "compile": {"lib/netstandard1.1/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {}}, "runtime": {"lib/netstandard1.1/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {}}}, "Microsoft.Extensions.PlatformAbstractions/1.0.0": {"type": "package", "dependencies": {"System.AppContext": "4.1.0", "System.Reflection": "4.1.0", "System.Reflection.Extensions": "4.0.1", "System.Reflection.TypeExtensions": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime.Extensions": "4.1.0"}, "compile": {"lib/netstandard1.3/Microsoft.Extensions.PlatformAbstractions.dll": {}}, "runtime": {"lib/netstandard1.3/Microsoft.Extensions.PlatformAbstractions.dll": {}}}, "Microsoft.Extensions.Primitives/1.0.0": {"type": "package", "dependencies": {"System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0"}, "compile": {"lib/netstandard1.0/Microsoft.Extensions.Primitives.dll": {}}, "runtime": {"lib/netstandard1.0/Microsoft.Extensions.Primitives.dll": {}}}, "Microsoft.Extensions.WebEncoders/1.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "1.0.0", "Microsoft.Extensions.Options": "1.0.0", "System.Text.Encodings.Web": "4.0.0"}, "compile": {"lib/netstandard1.0/Microsoft.Extensions.WebEncoders.dll": {}}, "runtime": {"lib/netstandard1.0/Microsoft.Extensions.WebEncoders.dll": {}}}, "Microsoft.Net.Http.Headers/1.0.0": {"type": "package", "dependencies": {"System.Buffers": "4.0.0", "System.Collections": "4.0.11", "System.Diagnostics.Contracts": "4.0.1", "System.Globalization": "4.0.11", "System.Linq": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime.Extensions": "4.1.0", "System.Text.Encoding": "4.0.11"}, "compile": {"lib/netstandard1.1/Microsoft.Net.Http.Headers.dll": {}}, "runtime": {"lib/netstandard1.1/Microsoft.Net.Http.Headers.dll": {}}}, "Microsoft.NETCore.App/1.0.0": {"type": "package", "dependencies": {"Libuv": "1.9.0", "Microsoft.CSharp": "4.0.1", "Microsoft.CodeAnalysis.CSharp": "1.3.0", "Microsoft.CodeAnalysis.VisualBasic": "1.3.0", "Microsoft.NETCore.DotNetHostPolicy": "1.0.1", "Microsoft.NETCore.Runtime.CoreCLR": "1.0.2", "Microsoft.VisualBasic": "10.0.1", "NETStandard.Library": "1.6.0", "System.Buffers": "4.0.0", "System.Collections.Immutable": "1.2.0", "System.ComponentModel": "4.0.1", "System.ComponentModel.Annotations": "4.1.0", "System.Diagnostics.DiagnosticSource": "4.0.0", "System.Diagnostics.Process": "4.1.0", "System.Dynamic.Runtime": "4.0.11", "System.Globalization.Extensions": "4.0.1", "System.IO.FileSystem.Watcher": "4.0.0", "System.IO.MemoryMappedFiles": "4.0.0", "System.IO.UnmanagedMemoryStream": "4.0.1", "System.Linq.Expressions": "4.1.0", "System.Linq.Parallel": "4.0.1", "System.Linq.Queryable": "4.0.1", "System.Net.NameResolution": "4.0.0", "System.Net.Requests": "4.0.11", "System.Net.Security": "4.0.0", "System.Net.WebHeaderCollection": "4.0.1", "System.Numerics.Vectors": "4.1.1", "System.Reflection.DispatchProxy": "4.0.1", "System.Reflection.Metadata": "1.3.0", "System.Reflection.TypeExtensions": "4.1.0", "System.Resources.Reader": "4.0.0", "System.Runtime.Loader": "4.0.0", "System.Security.Cryptography.Algorithms": "4.2.0", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Security.Cryptography.X509Certificates": "4.1.0", "System.Threading.Tasks.Dataflow": "4.6.0", "System.Threading.Tasks.Extensions": "4.0.0", "System.Threading.Tasks.Parallel": "4.0.1", "System.Threading.Thread": "4.0.0", "System.Threading.ThreadPool": "4.0.10"}, "compile": {"lib/netcoreapp1.0/_._": {}}, "runtime": {"lib/netcoreapp1.0/_._": {}}}, "Microsoft.NETCore.DotNetHost/1.0.1": {"type": "package"}, "Microsoft.NETCore.DotNetHostPolicy/1.0.1": {"type": "package", "dependencies": {"Microsoft.NETCore.DotNetHostResolver": "1.0.1"}}, "Microsoft.NETCore.DotNetHostResolver/1.0.1": {"type": "package", "dependencies": {"Microsoft.NETCore.DotNetHost": "1.0.1"}}, "Microsoft.NETCore.Jit/1.0.2": {"type": "package"}, "Microsoft.NETCore.Platforms/1.0.1": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.NETCore.Runtime.CoreCLR/1.0.2": {"type": "package", "dependencies": {"Microsoft.NETCore.Jit": "1.0.2", "Microsoft.NETCore.Windows.ApiSets": "1.0.1"}}, "Microsoft.NETCore.Targets/1.0.1": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.NETCore.Windows.ApiSets/1.0.1": {"type": "package"}, "Microsoft.VisualBasic/10.0.1": {"type": "package", "dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Dynamic.Runtime": "4.0.11", "System.Globalization": "4.0.11", "System.Linq": "4.1.0", "System.Linq.Expressions": "4.1.0", "System.ObjectModel": "4.0.12", "System.Reflection": "4.1.0", "System.Reflection.Extensions": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Reflection.TypeExtensions": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.InteropServices": "4.1.0", "System.Threading": "4.0.11"}, "compile": {"ref/netstandard1.1/Microsoft.VisualBasic.dll": {}}, "runtime": {"lib/netstandard1.3/_._": {}}}, "Microsoft.Win32.Primitives/4.0.1": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.3/Microsoft.Win32.Primitives.dll": {}}}, "Microsoft.Win32.Registry/4.0.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.Collections": "4.0.11", "System.Globalization": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0"}, "compile": {"ref/netstandard1.3/Microsoft.Win32.Registry.dll": {}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.3/Microsoft.Win32.Registry.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.3/Microsoft.Win32.Registry.dll": {"assetType": "runtime", "rid": "win"}}}, "NETStandard.Library/1.6.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.Win32.Primitives": "4.0.1", "System.AppContext": "4.1.0", "System.Collections": "4.0.11", "System.Collections.Concurrent": "4.0.12", "System.Console": "4.0.0", "System.Diagnostics.Debug": "4.0.11", "System.Diagnostics.Tools": "4.0.1", "System.Diagnostics.Tracing": "4.1.0", "System.Globalization": "4.0.11", "System.Globalization.Calendars": "4.0.1", "System.IO": "4.1.0", "System.IO.Compression": "4.1.0", "System.IO.Compression.ZipFile": "4.0.1", "System.IO.FileSystem": "4.0.1", "System.IO.FileSystem.Primitives": "4.0.1", "System.Linq": "4.1.0", "System.Linq.Expressions": "4.1.0", "System.Net.Http": "4.1.0", "System.Net.Primitives": "4.0.11", "System.Net.Sockets": "4.1.0", "System.ObjectModel": "4.0.12", "System.Reflection": "4.1.0", "System.Reflection.Extensions": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Runtime.InteropServices.RuntimeInformation": "4.0.0", "System.Runtime.Numerics": "4.0.1", "System.Security.Cryptography.Algorithms": "4.2.0", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Security.Cryptography.X509Certificates": "4.1.0", "System.Text.Encoding": "4.0.11", "System.Text.Encoding.Extensions": "4.0.11", "System.Text.RegularExpressions": "4.1.0", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.0.11", "System.Threading.Timer": "4.0.1", "System.Xml.ReaderWriter": "4.0.11", "System.Xml.XDocument": "4.0.11"}}, "Newtonsoft.Json/9.0.1": {"type": "package", "dependencies": {"Microsoft.CSharp": "4.0.1", "System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Dynamic.Runtime": "4.0.11", "System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.Linq": "4.1.0", "System.Linq.Expressions": "4.1.0", "System.ObjectModel": "4.0.12", "System.Reflection": "4.1.0", "System.Reflection.Extensions": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Serialization.Primitives": "4.1.1", "System.Text.Encoding": "4.0.11", "System.Text.Encoding.Extensions": "4.0.11", "System.Text.RegularExpressions": "4.1.0", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.0.11", "System.Xml.ReaderWriter": "4.0.11", "System.Xml.XDocument": "4.0.11"}, "compile": {"lib/netstandard1.0/Newtonsoft.Json.dll": {}}, "runtime": {"lib/netstandard1.0/Newtonsoft.Json.dll": {}}}, "runtime.native.System/4.0.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1"}, "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "runtime.native.System.IO.Compression/4.1.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1"}, "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "runtime.native.System.Net.Http/4.0.1": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1"}, "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "runtime.native.System.Net.Security/4.0.1": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1"}, "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "runtime.native.System.Security.Cryptography/4.0.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1"}, "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "System.AppContext/4.1.0": {"type": "package", "dependencies": {"System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.6/System.AppContext.dll": {}}, "runtime": {"lib/netstandard1.6/System.AppContext.dll": {}}}, "System.Buffers/4.0.0": {"type": "package", "dependencies": {"System.Diagnostics.Debug": "4.0.11", "System.Diagnostics.Tracing": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Threading": "4.0.11"}, "compile": {"lib/netstandard1.1/System.Buffers.dll": {}}, "runtime": {"lib/netstandard1.1/System.Buffers.dll": {}}}, "System.Collections/4.0.11": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.3/System.Collections.dll": {}}, "runtime": {"lib/portable-net45+win8+wp8+wpa81/_._": {}}}, "System.Collections.Concurrent/4.0.12": {"type": "package", "dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Diagnostics.Tracing": "4.1.0", "System.Globalization": "4.0.11", "System.Reflection": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.0.11"}, "compile": {"ref/netstandard1.3/System.Collections.Concurrent.dll": {}}, "runtime": {"lib/netstandard1.3/System.Collections.Concurrent.dll": {}}}, "System.Collections.Immutable/1.2.0": {"type": "package", "dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.Linq": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}, "compile": {"lib/netstandard1.0/System.Collections.Immutable.dll": {}}, "runtime": {"lib/netstandard1.0/System.Collections.Immutable.dll": {}}}, "System.Collections.NonGeneric/4.0.1": {"type": "package", "dependencies": {"System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}, "compile": {"ref/netstandard1.3/System.Collections.NonGeneric.dll": {}}, "runtime": {"lib/netstandard1.3/System.Collections.NonGeneric.dll": {}}}, "System.Collections.Specialized/4.0.1": {"type": "package", "dependencies": {"System.Collections.NonGeneric": "4.0.1", "System.Globalization": "4.0.11", "System.Globalization.Extensions": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}, "compile": {"ref/netstandard1.3/_._": {}}, "runtime": {"lib/netstandard1.3/System.Collections.Specialized.dll": {}}}, "System.ComponentModel/4.0.1": {"type": "package", "dependencies": {"System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.0/System.ComponentModel.dll": {}}, "runtime": {"lib/netstandard1.3/System.ComponentModel.dll": {}}}, "System.ComponentModel.Annotations/4.1.0": {"type": "package", "dependencies": {"System.Collections": "4.0.11", "System.ComponentModel": "4.0.1", "System.Globalization": "4.0.11", "System.Linq": "4.1.0", "System.Reflection": "4.1.0", "System.Reflection.Extensions": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Text.RegularExpressions": "4.1.0", "System.Threading": "4.0.11"}, "compile": {"ref/netstandard1.4/System.ComponentModel.Annotations.dll": {}}, "runtime": {"lib/netstandard1.4/System.ComponentModel.Annotations.dll": {}}}, "System.ComponentModel.Primitives/4.1.0": {"type": "package", "dependencies": {"System.ComponentModel": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.0/System.ComponentModel.Primitives.dll": {}}, "runtime": {"lib/netstandard1.0/System.ComponentModel.Primitives.dll": {}}}, "System.ComponentModel.TypeConverter/4.1.0": {"type": "package", "dependencies": {"System.Collections": "4.0.11", "System.Collections.NonGeneric": "4.0.1", "System.Collections.Specialized": "4.0.1", "System.ComponentModel": "4.0.1", "System.ComponentModel.Primitives": "4.1.0", "System.Globalization": "4.0.11", "System.Linq": "4.1.0", "System.Reflection": "4.1.0", "System.Reflection.Extensions": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Reflection.TypeExtensions": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}, "compile": {"ref/netstandard1.5/System.ComponentModel.TypeConverter.dll": {}}, "runtime": {"lib/netstandard1.5/System.ComponentModel.TypeConverter.dll": {}}}, "System.Console/4.0.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.IO": "4.1.0", "System.Runtime": "4.1.0", "System.Text.Encoding": "4.0.11"}, "compile": {"ref/netstandard1.3/System.Console.dll": {}}}, "System.Diagnostics.Contracts/4.0.1": {"type": "package", "dependencies": {"System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.0/System.Diagnostics.Contracts.dll": {}}, "runtime": {"lib/netstandard1.0/System.Diagnostics.Contracts.dll": {}}}, "System.Diagnostics.Debug/4.0.11": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.3/System.Diagnostics.Debug.dll": {}}, "runtime": {"lib/portable-net45+win8+wp8+wpa81/_._": {}}}, "System.Diagnostics.DiagnosticSource/4.0.0": {"type": "package", "dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Tracing": "4.1.0", "System.Reflection": "4.1.0", "System.Runtime": "4.1.0", "System.Threading": "4.0.11"}, "compile": {"lib/netstandard1.3/System.Diagnostics.DiagnosticSource.dll": {}}, "runtime": {"lib/netstandard1.3/System.Diagnostics.DiagnosticSource.dll": {}}}, "System.Diagnostics.FileVersionInfo/4.0.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.IO.FileSystem": "4.0.1", "System.IO.FileSystem.Primitives": "4.0.1", "System.Reflection.Metadata": "1.3.0", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.InteropServices": "4.1.0"}, "compile": {"ref/netstandard1.3/_._": {}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.3/System.Diagnostics.FileVersionInfo.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.3/System.Diagnostics.FileVersionInfo.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Diagnostics.Process/4.1.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.Win32.Primitives": "4.0.1", "Microsoft.Win32.Registry": "4.0.0", "System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.IO.FileSystem": "4.0.1", "System.IO.FileSystem.Primitives": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Text.Encoding": "4.0.11", "System.Text.Encoding.Extensions": "4.0.11", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.0.11", "System.Threading.Thread": "4.0.0", "System.Threading.ThreadPool": "4.0.10", "runtime.native.System": "4.0.0"}, "compile": {"ref/netstandard1.4/System.Diagnostics.Process.dll": {}}, "runtimeTargets": {"runtimes/osx/lib/netstandard1.4/_._": {"assetType": "runtime", "rid": "osx"}}}, "System.Diagnostics.StackTrace/4.0.1": {"type": "package", "dependencies": {"System.Collections.Immutable": "1.2.0", "System.IO.FileSystem": "4.0.1", "System.Reflection": "4.1.0", "System.Reflection.Metadata": "1.3.0", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0"}, "compile": {"ref/netstandard1.3/System.Diagnostics.StackTrace.dll": {}}, "runtime": {"lib/netstandard1.3/System.Diagnostics.StackTrace.dll": {}}}, "System.Diagnostics.Tools/4.0.1": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.0/System.Diagnostics.Tools.dll": {}}, "runtime": {"lib/portable-net45+win8+wp8+wpa81/_._": {}}}, "System.Diagnostics.Tracing/4.1.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.5/System.Diagnostics.Tracing.dll": {}}, "runtime": {"lib/portable-net45+win8+wpa81/_._": {}}}, "System.Dynamic.Runtime/4.0.11": {"type": "package", "dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.Linq": "4.1.0", "System.Linq.Expressions": "4.1.0", "System.ObjectModel": "4.0.12", "System.Reflection": "4.1.0", "System.Reflection.Emit": "4.0.1", "System.Reflection.Emit.ILGeneration": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Reflection.TypeExtensions": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}, "compile": {"ref/netstandard1.3/System.Dynamic.Runtime.dll": {}}, "runtime": {"lib/netstandard1.3/System.Dynamic.Runtime.dll": {}}}, "System.Globalization/4.0.11": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.3/System.Globalization.dll": {}}, "runtime": {"lib/portable-net45+win8+wp8+wpa81/_._": {}}}, "System.Globalization.Calendars/4.0.1": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Globalization": "4.0.11", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.3/System.Globalization.Calendars.dll": {}}}, "System.Globalization.Extensions/4.0.1": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.Globalization": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.InteropServices": "4.1.0"}, "compile": {"ref/netstandard1.3/System.Globalization.Extensions.dll": {}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.3/System.Globalization.Extensions.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.3/System.Globalization.Extensions.dll": {"assetType": "runtime", "rid": "win"}}}, "System.IO/4.1.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0", "System.Text.Encoding": "4.0.11", "System.Threading.Tasks": "4.0.11"}, "compile": {"ref/netstandard1.5/System.IO.dll": {}}, "runtime": {"lib/portable-net45+win8+wp8+wpa81/_._": {}}}, "System.IO.Compression/4.1.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.IO": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Text.Encoding": "4.0.11", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.0.11", "runtime.native.System": "4.0.0", "runtime.native.System.IO.Compression": "4.1.0"}, "compile": {"ref/netstandard1.3/System.IO.Compression.dll": {}}, "runtime": {"lib/portable-net45+win8+wpa81/_._": {}}, "runtimeTargets": {"runtimes/win/lib/netstandard1.3/_._": {"assetType": "runtime", "rid": "win"}}}, "System.IO.Compression.ZipFile/4.0.1": {"type": "package", "dependencies": {"System.Buffers": "4.0.0", "System.IO": "4.1.0", "System.IO.Compression": "4.1.0", "System.IO.FileSystem": "4.0.1", "System.IO.FileSystem.Primitives": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Text.Encoding": "4.0.11"}, "compile": {"ref/netstandard1.3/System.IO.Compression.ZipFile.dll": {}}, "runtime": {"lib/netstandard1.3/_._": {}}}, "System.IO.FileSystem/4.0.1": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.IO": "4.1.0", "System.IO.FileSystem.Primitives": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Text.Encoding": "4.0.11", "System.Threading.Tasks": "4.0.11"}, "compile": {"ref/netstandard1.3/System.IO.FileSystem.dll": {}}}, "System.IO.FileSystem.Primitives/4.0.1": {"type": "package", "dependencies": {"System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.3/System.IO.FileSystem.Primitives.dll": {}}, "runtime": {"lib/netstandard1.3/System.IO.FileSystem.Primitives.dll": {}}}, "System.IO.FileSystem.Watcher/4.0.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.Win32.Primitives": "4.0.1", "System.Collections": "4.0.11", "System.IO.FileSystem": "4.0.1", "System.IO.FileSystem.Primitives": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Text.Encoding": "4.0.11", "System.Threading": "4.0.11", "System.Threading.Overlapped": "4.0.1", "System.Threading.Tasks": "4.0.11", "System.Threading.Thread": "4.0.0", "runtime.native.System": "4.0.0"}, "compile": {"ref/netstandard1.3/System.IO.FileSystem.Watcher.dll": {}}, "runtimeTargets": {"runtimes/linux/lib/netstandard1.3/System.IO.FileSystem.Watcher.dll": {"assetType": "runtime", "rid": "linux"}, "runtimes/osx/lib/netstandard1.3/System.IO.FileSystem.Watcher.dll": {"assetType": "runtime", "rid": "osx"}, "runtimes/win/lib/netstandard1.3/System.IO.FileSystem.Watcher.dll": {"assetType": "runtime", "rid": "win"}}}, "System.IO.MemoryMappedFiles/4.0.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.IO": "4.1.0", "System.IO.FileSystem": "4.0.1", "System.IO.FileSystem.Primitives": "4.0.1", "System.IO.UnmanagedMemoryStream": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.0.11", "runtime.native.System": "4.0.0"}, "compile": {"ref/netstandard1.3/System.IO.MemoryMappedFiles.dll": {}}, "runtimeTargets": {"runtimes/win/lib/netstandard1.3/_._": {"assetType": "runtime", "rid": "win"}}}, "System.IO.UnmanagedMemoryStream/4.0.1": {"type": "package", "dependencies": {"System.IO": "4.1.0", "System.IO.FileSystem.Primitives": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.InteropServices": "4.1.0", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.0.11"}, "compile": {"ref/netstandard1.3/System.IO.UnmanagedMemoryStream.dll": {}}, "runtime": {"lib/netstandard1.3/_._": {}}}, "System.Linq/4.1.0": {"type": "package", "dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0"}, "compile": {"ref/netstandard1.6/System.Linq.dll": {}}, "runtime": {"lib/netstandard1.6/System.Linq.dll": {}}}, "System.Linq.Expressions/4.1.0": {"type": "package", "dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.Linq": "4.1.0", "System.ObjectModel": "4.0.12", "System.Reflection": "4.1.0", "System.Reflection.Emit": "4.0.1", "System.Reflection.Emit.ILGeneration": "4.0.1", "System.Reflection.Emit.Lightweight": "4.0.1", "System.Reflection.Extensions": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Reflection.TypeExtensions": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}, "compile": {"ref/netstandard1.6/System.Linq.Expressions.dll": {}}, "runtime": {"lib/netstandard1.6/System.Linq.Expressions.dll": {}}}, "System.Linq.Parallel/4.0.1": {"type": "package", "dependencies": {"System.Collections": "4.0.11", "System.Collections.Concurrent": "4.0.12", "System.Diagnostics.Debug": "4.0.11", "System.Diagnostics.Tracing": "4.1.0", "System.Linq": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.0.11"}, "compile": {"ref/netstandard1.1/System.Linq.Parallel.dll": {}}, "runtime": {"lib/netstandard1.3/_._": {}}}, "System.Linq.Queryable/4.0.1": {"type": "package", "dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Linq": "4.1.0", "System.Linq.Expressions": "4.1.0", "System.Reflection": "4.1.0", "System.Reflection.Extensions": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.0/System.Linq.Queryable.dll": {}}, "runtime": {"lib/netstandard1.3/_._": {}}}, "System.Net.Http/4.1.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Diagnostics.DiagnosticSource": "4.0.0", "System.Diagnostics.Tracing": "4.1.0", "System.Globalization": "4.0.11", "System.Globalization.Extensions": "4.0.1", "System.IO": "4.1.0", "System.IO.FileSystem": "4.0.1", "System.Net.Primitives": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Security.Cryptography.Algorithms": "4.2.0", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.OpenSsl": "4.0.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Security.Cryptography.X509Certificates": "4.1.0", "System.Text.Encoding": "4.0.11", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.0.11", "runtime.native.System": "4.0.0", "runtime.native.System.Net.Http": "4.0.1", "runtime.native.System.Security.Cryptography": "4.0.0"}, "compile": {"ref/netstandard1.3/System.Net.Http.dll": {}}, "runtime": {"lib/portable-net45+win8+wpa81/_._": {}}, "runtimeTargets": {"runtimes/win/lib/netstandard1.3/_._": {"assetType": "runtime", "rid": "win"}}}, "System.Net.NameResolution/4.0.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.Collections": "4.0.11", "System.Diagnostics.Tracing": "4.1.0", "System.Globalization": "4.0.11", "System.Net.Primitives": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Security.Principal.Windows": "4.0.0", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.0.11", "runtime.native.System": "4.0.0"}, "compile": {"ref/netstandard1.3/System.Net.NameResolution.dll": {}}, "runtimeTargets": {"runtimes/win/lib/netstandard1.3/_._": {"assetType": "runtime", "rid": "win"}}}, "System.Net.Primitives/4.0.11": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0", "System.Runtime.Handles": "4.0.1"}, "compile": {"ref/netstandard1.3/System.Net.Primitives.dll": {}}, "runtime": {"lib/portable-net45+win8+wp8+wpa81/_._": {}}}, "System.Net.Requests/4.0.11": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Diagnostics.Tracing": "4.1.0", "System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.Net.Http": "4.1.0", "System.Net.Primitives": "4.0.11", "System.Net.WebHeaderCollection": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.0.11"}, "compile": {"ref/netstandard1.3/System.Net.Requests.dll": {}}, "runtime": {"lib/portable-net45+win8+wp8+wpa81/_._": {}}, "runtimeTargets": {"runtimes/win/lib/netstandard1.3/_._": {"assetType": "runtime", "rid": "win"}}}, "System.Net.Security/4.0.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.Win32.Primitives": "4.0.1", "System.Collections": "4.0.11", "System.Collections.Concurrent": "4.0.12", "System.Diagnostics.Tracing": "4.1.0", "System.Globalization": "4.0.11", "System.Globalization.Extensions": "4.0.1", "System.IO": "4.1.0", "System.Net.Primitives": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Security.Claims": "4.0.1", "System.Security.Cryptography.Algorithms": "4.2.0", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.OpenSsl": "4.0.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Security.Cryptography.X509Certificates": "4.1.0", "System.Security.Principal": "4.0.1", "System.Text.Encoding": "4.0.11", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.0.11", "System.Threading.ThreadPool": "4.0.10", "runtime.native.System": "4.0.0", "runtime.native.System.Net.Security": "4.0.1", "runtime.native.System.Security.Cryptography": "4.0.0"}, "compile": {"ref/netstandard1.3/System.Net.Security.dll": {}}, "runtimeTargets": {"runtimes/win/lib/netstandard1.3/_._": {"assetType": "runtime", "rid": "win"}}}, "System.Net.Sockets/4.1.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.IO": "4.1.0", "System.Net.Primitives": "4.0.11", "System.Runtime": "4.1.0", "System.Threading.Tasks": "4.0.11"}, "compile": {"ref/netstandard1.3/System.Net.Sockets.dll": {}}}, "System.Net.WebHeaderCollection/4.0.1": {"type": "package", "dependencies": {"System.Collections": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0"}, "compile": {"ref/netstandard1.3/System.Net.WebHeaderCollection.dll": {}}, "runtime": {"lib/netstandard1.3/_._": {}}}, "System.Net.WebSockets/4.0.0": {"type": "package", "dependencies": {"Microsoft.Win32.Primitives": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Threading.Tasks": "4.0.11"}, "compile": {"ref/netstandard1.3/System.Net.WebSockets.dll": {}}, "runtime": {"lib/netstandard1.3/System.Net.WebSockets.dll": {}}}, "System.Numerics.Vectors/4.1.1": {"type": "package", "dependencies": {"System.Globalization": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0"}, "compile": {"ref/netstandard1.0/System.Numerics.Vectors.dll": {}}, "runtime": {"lib/netstandard1.0/System.Numerics.Vectors.dll": {}}}, "System.ObjectModel/4.0.12": {"type": "package", "dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Threading": "4.0.11"}, "compile": {"ref/netstandard1.3/System.ObjectModel.dll": {}}, "runtime": {"lib/netstandard1.3/System.ObjectModel.dll": {}}}, "System.Reflection/4.1.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.IO": "4.1.0", "System.Reflection.Primitives": "4.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.5/System.Reflection.dll": {}}, "runtime": {"lib/portable-net45+win8+wp8+wpa81/_._": {}}}, "System.Reflection.DispatchProxy/4.0.1": {"type": "package", "dependencies": {"System.Collections": "4.0.11", "System.Linq": "4.1.0", "System.Reflection": "4.1.0", "System.Reflection.Emit": "4.0.1", "System.Reflection.Emit.ILGeneration": "4.0.1", "System.Reflection.Extensions": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Threading": "4.0.11"}, "compile": {"ref/netstandard1.3/System.Reflection.DispatchProxy.dll": {}}, "runtime": {"lib/netstandard1.3/_._": {}}}, "System.Reflection.Emit/4.0.1": {"type": "package", "dependencies": {"System.IO": "4.1.0", "System.Reflection": "4.1.0", "System.Reflection.Emit.ILGeneration": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.1/_._": {}}, "runtime": {"lib/netstandard1.3/System.Reflection.Emit.dll": {}}}, "System.Reflection.Emit.ILGeneration/4.0.1": {"type": "package", "dependencies": {"System.Reflection": "4.1.0", "System.Reflection.Primitives": "4.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.3/System.Reflection.Emit.ILGeneration.dll": {}}}, "System.Reflection.Emit.Lightweight/4.0.1": {"type": "package", "dependencies": {"System.Reflection": "4.1.0", "System.Reflection.Emit.ILGeneration": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.3/System.Reflection.Emit.Lightweight.dll": {}}}, "System.Reflection.Extensions/4.0.1": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Reflection": "4.1.0", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.0/System.Reflection.Extensions.dll": {}}, "runtime": {"lib/portable-net45+win8+wp8+wpa81/_._": {}}}, "System.Reflection.Metadata/1.3.0": {"type": "package", "dependencies": {"System.Collections": "4.0.11", "System.Collections.Immutable": "1.2.0", "System.Diagnostics.Debug": "4.0.11", "System.IO": "4.1.0", "System.Linq": "4.1.0", "System.Reflection": "4.1.0", "System.Reflection.Extensions": "4.0.1", "System.Reflection.Primitives": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.InteropServices": "4.1.0", "System.Text.Encoding": "4.0.11", "System.Text.Encoding.Extensions": "4.0.11", "System.Threading": "4.0.11"}, "compile": {"lib/netstandard1.1/System.Reflection.Metadata.dll": {}}, "runtime": {"lib/netstandard1.1/System.Reflection.Metadata.dll": {}}}, "System.Reflection.Primitives/4.0.1": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.0/System.Reflection.Primitives.dll": {}}, "runtime": {"lib/portable-net45+win8+wp8+wpa81/_._": {}}}, "System.Reflection.TypeExtensions/4.1.0": {"type": "package", "dependencies": {"System.Reflection": "4.1.0", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.5/System.Reflection.TypeExtensions.dll": {}}, "runtime": {"lib/netstandard1.5/System.Reflection.TypeExtensions.dll": {}}}, "System.Resources.Reader/4.0.0": {"type": "package", "dependencies": {"System.IO": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Text.Encoding": "4.0.11", "System.Threading": "4.0.11"}, "compile": {"lib/netstandard1.0/System.Resources.Reader.dll": {}}, "runtime": {"lib/netstandard1.0/System.Resources.Reader.dll": {}}}, "System.Resources.ResourceManager/4.0.1": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Globalization": "4.0.11", "System.Reflection": "4.1.0", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.0/System.Resources.ResourceManager.dll": {}}, "runtime": {"lib/portable-net45+win8+wp8+wpa81/_._": {}}}, "System.Runtime/4.1.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1"}, "compile": {"ref/netstandard1.5/System.Runtime.dll": {}}, "runtime": {"lib/portable-net45+win8+wp80+wpa81/_._": {}}}, "System.Runtime.Extensions/4.1.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.5/System.Runtime.Extensions.dll": {}}, "runtime": {"lib/portable-net45+win8+wp8+wpa81/_._": {}}}, "System.Runtime.Handles/4.0.1": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.3/System.Runtime.Handles.dll": {}}}, "System.Runtime.InteropServices/4.1.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Reflection": "4.1.0", "System.Reflection.Primitives": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Handles": "4.0.1"}, "compile": {"ref/netstandard1.5/System.Runtime.InteropServices.dll": {}}, "runtime": {"lib/portable-net45+win8+wpa81/_._": {}}}, "System.Runtime.InteropServices.RuntimeInformation/4.0.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.Reflection": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.InteropServices": "4.1.0", "System.Threading": "4.0.11", "runtime.native.System": "4.0.0"}, "compile": {"ref/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll": {}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Runtime.Loader/4.0.0": {"type": "package", "dependencies": {"System.IO": "4.1.0", "System.Reflection": "4.1.0", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.5/System.Runtime.Loader.dll": {}}, "runtime": {"lib/netstandard1.5/System.Runtime.Loader.dll": {}}}, "System.Runtime.Numerics/4.0.1": {"type": "package", "dependencies": {"System.Globalization": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0"}, "compile": {"ref/netstandard1.1/System.Runtime.Numerics.dll": {}}, "runtime": {"lib/netstandard1.3/System.Runtime.Numerics.dll": {}}}, "System.Runtime.Serialization.Primitives/4.1.1": {"type": "package", "dependencies": {"System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.3/System.Runtime.Serialization.Primitives.dll": {}}, "runtime": {"lib/netstandard1.3/System.Runtime.Serialization.Primitives.dll": {}}}, "System.Security.Claims/4.0.1": {"type": "package", "dependencies": {"System.Collections": "4.0.11", "System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Security.Principal": "4.0.1"}, "compile": {"ref/netstandard1.3/System.Security.Claims.dll": {}}, "runtime": {"lib/netstandard1.3/System.Security.Claims.dll": {}}}, "System.Security.Cryptography.Algorithms/4.2.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.Collections": "4.0.11", "System.IO": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Runtime.Numerics": "4.0.1", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Text.Encoding": "4.0.11", "runtime.native.System.Security.Cryptography": "4.0.0"}, "compile": {"ref/netstandard1.6/System.Security.Cryptography.Algorithms.dll": {}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.6/System.Security.Cryptography.Algorithms.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.6/System.Security.Cryptography.Algorithms.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Cng/4.2.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.IO": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Security.Cryptography.Algorithms": "4.2.0", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Text.Encoding": "4.0.11"}, "compile": {"ref/netstandard1.6/_._": {}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.6/System.Security.Cryptography.Cng.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.6/System.Security.Cryptography.Cng.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Csp/4.0.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.IO": "4.1.0", "System.Reflection": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Security.Cryptography.Algorithms": "4.2.0", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Text.Encoding": "4.0.11", "System.Threading": "4.0.11"}, "compile": {"ref/netstandard1.3/_._": {}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.3/System.Security.Cryptography.Csp.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.3/System.Security.Cryptography.Csp.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Encoding/4.0.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.Collections": "4.0.11", "System.Collections.Concurrent": "4.0.12", "System.Linq": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Text.Encoding": "4.0.11", "runtime.native.System.Security.Cryptography": "4.0.0"}, "compile": {"ref/netstandard1.3/System.Security.Cryptography.Encoding.dll": {}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.3/System.Security.Cryptography.Encoding.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.3/System.Security.Cryptography.Encoding.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.OpenSsl/4.0.0": {"type": "package", "dependencies": {"System.Collections": "4.0.11", "System.IO": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Runtime.Numerics": "4.0.1", "System.Security.Cryptography.Algorithms": "4.2.0", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Text.Encoding": "4.0.11", "runtime.native.System.Security.Cryptography": "4.0.0"}, "compile": {"ref/netstandard1.6/_._": {}}, "runtime": {"lib/netstandard1.6/System.Security.Cryptography.OpenSsl.dll": {}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.6/System.Security.Cryptography.OpenSsl.dll": {"assetType": "runtime", "rid": "unix"}}}, "System.Security.Cryptography.Primitives/4.0.0": {"type": "package", "dependencies": {"System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.0.11"}, "compile": {"ref/netstandard1.3/System.Security.Cryptography.Primitives.dll": {}}, "runtime": {"lib/netstandard1.3/System.Security.Cryptography.Primitives.dll": {}}}, "System.Security.Cryptography.X509Certificates/4.1.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.Globalization.Calendars": "4.0.1", "System.IO": "4.1.0", "System.IO.FileSystem": "4.0.1", "System.IO.FileSystem.Primitives": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Runtime.Numerics": "4.0.1", "System.Security.Cryptography.Algorithms": "4.2.0", "System.Security.Cryptography.Cng": "4.2.0", "System.Security.Cryptography.Csp": "4.0.0", "System.Security.Cryptography.Encoding": "4.0.0", "System.Security.Cryptography.OpenSsl": "4.0.0", "System.Security.Cryptography.Primitives": "4.0.0", "System.Text.Encoding": "4.0.11", "System.Threading": "4.0.11", "runtime.native.System": "4.0.0", "runtime.native.System.Net.Http": "4.0.1", "runtime.native.System.Security.Cryptography": "4.0.0"}, "compile": {"ref/netstandard1.4/System.Security.Cryptography.X509Certificates.dll": {}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.6/System.Security.Cryptography.X509Certificates.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.6/System.Security.Cryptography.X509Certificates.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Principal/4.0.1": {"type": "package", "dependencies": {"System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.0/System.Security.Principal.dll": {}}, "runtime": {"lib/netstandard1.0/System.Security.Principal.dll": {}}}, "System.Security.Principal.Windows/4.0.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.Win32.Primitives": "4.0.1", "System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Reflection": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Security.Claims": "4.0.1", "System.Security.Principal": "4.0.1", "System.Text.Encoding": "4.0.11", "System.Threading": "4.0.11"}, "compile": {"ref/netstandard1.3/System.Security.Principal.Windows.dll": {}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.3/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.3/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Text.Encoding/4.0.11": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.3/System.Text.Encoding.dll": {}}, "runtime": {"lib/portable-net45+win8+wp8+wpa81/_._": {}}}, "System.Text.Encoding.CodePages/4.0.1": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.Collections": "4.0.11", "System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.Reflection": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.Handles": "4.0.1", "System.Runtime.InteropServices": "4.1.0", "System.Text.Encoding": "4.0.11", "System.Threading": "4.0.11"}, "compile": {"ref/netstandard1.3/_._": {}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.3/System.Text.Encoding.CodePages.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.3/System.Text.Encoding.CodePages.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Text.Encoding.Extensions/4.0.11": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0", "System.Text.Encoding": "4.0.11"}, "compile": {"ref/netstandard1.3/System.Text.Encoding.Extensions.dll": {}}, "runtime": {"lib/portable-net45+win8+wp8+wpa81/_._": {}}}, "System.Text.Encodings.Web/4.0.0": {"type": "package", "dependencies": {"System.Diagnostics.Debug": "4.0.11", "System.IO": "4.1.0", "System.Reflection": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}, "compile": {"lib/netstandard1.0/System.Text.Encodings.Web.dll": {}}, "runtime": {"lib/netstandard1.0/System.Text.Encodings.Web.dll": {}}}, "System.Text.RegularExpressions/4.1.0": {"type": "package", "dependencies": {"System.Collections": "4.0.11", "System.Globalization": "4.0.11", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11"}, "compile": {"ref/netstandard1.6/System.Text.RegularExpressions.dll": {}}, "runtime": {"lib/netstandard1.6/System.Text.RegularExpressions.dll": {}}}, "System.Threading/4.0.11": {"type": "package", "dependencies": {"System.Runtime": "4.1.0", "System.Threading.Tasks": "4.0.11"}, "compile": {"ref/netstandard1.3/System.Threading.dll": {}}, "runtime": {"lib/netstandard1.3/System.Threading.dll": {}}}, "System.Threading.Overlapped/4.0.1": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Handles": "4.0.1"}, "compile": {"ref/netstandard1.3/_._": {}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.3/System.Threading.Overlapped.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.3/System.Threading.Overlapped.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Threading.Tasks/4.0.11": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.3/System.Threading.Tasks.dll": {}}, "runtime": {"lib/portable-net45+win8+wp8+wpa81/_._": {}}}, "System.Threading.Tasks.Dataflow/4.6.0": {"type": "package", "dependencies": {"System.Collections": "4.0.11", "System.Collections.Concurrent": "4.0.12", "System.Diagnostics.Debug": "4.0.11", "System.Diagnostics.Tracing": "4.1.0", "System.Dynamic.Runtime": "4.0.11", "System.Linq": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.0.11"}, "compile": {"lib/netstandard1.1/System.Threading.Tasks.Dataflow.dll": {}}, "runtime": {"lib/netstandard1.1/_._": {}}}, "System.Threading.Tasks.Extensions/4.0.0": {"type": "package", "dependencies": {"System.Collections": "4.0.11", "System.Runtime": "4.1.0", "System.Threading.Tasks": "4.0.11"}, "compile": {"lib/netstandard1.0/System.Threading.Tasks.Extensions.dll": {}}, "runtime": {"lib/netstandard1.0/System.Threading.Tasks.Extensions.dll": {}}}, "System.Threading.Tasks.Parallel/4.0.1": {"type": "package", "dependencies": {"System.Collections.Concurrent": "4.0.12", "System.Diagnostics.Debug": "4.0.11", "System.Diagnostics.Tracing": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11", "System.Threading.Tasks": "4.0.11"}, "compile": {"ref/netstandard1.1/System.Threading.Tasks.Parallel.dll": {}}, "runtime": {"lib/netstandard1.3/System.Threading.Tasks.Parallel.dll": {}}}, "System.Threading.Thread/4.0.0": {"type": "package", "dependencies": {"System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.3/System.Threading.Thread.dll": {}}, "runtime": {"lib/netstandard1.3/System.Threading.Thread.dll": {}}}, "System.Threading.ThreadPool/4.0.10": {"type": "package", "dependencies": {"System.Runtime": "4.1.0", "System.Runtime.Handles": "4.0.1"}, "compile": {"ref/netstandard1.3/System.Threading.ThreadPool.dll": {}}, "runtime": {"lib/netstandard1.3/System.Threading.ThreadPool.dll": {}}}, "System.Threading.Timer/4.0.1": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.0.1", "Microsoft.NETCore.Targets": "1.0.1", "System.Runtime": "4.1.0"}, "compile": {"ref/netstandard1.2/System.Threading.Timer.dll": {}}}, "System.Xml.ReaderWriter/4.0.11": {"type": "package", "dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.IO.FileSystem": "4.0.1", "System.IO.FileSystem.Primitives": "4.0.1", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Runtime.InteropServices": "4.1.0", "System.Text.Encoding": "4.0.11", "System.Text.Encoding.Extensions": "4.0.11", "System.Text.RegularExpressions": "4.1.0", "System.Threading.Tasks": "4.0.11", "System.Threading.Tasks.Extensions": "4.0.0"}, "compile": {"ref/netstandard1.3/System.Xml.ReaderWriter.dll": {}}, "runtime": {"lib/netstandard1.3/System.Xml.ReaderWriter.dll": {}}}, "System.Xml.XDocument/4.0.11": {"type": "package", "dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Diagnostics.Tools": "4.0.1", "System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.Reflection": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Text.Encoding": "4.0.11", "System.Threading": "4.0.11", "System.Xml.ReaderWriter": "4.0.11"}, "compile": {"ref/netstandard1.3/System.Xml.XDocument.dll": {}}, "runtime": {"lib/netstandard1.3/System.Xml.XDocument.dll": {}}}, "System.Xml.XmlDocument/4.0.1": {"type": "package", "dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Text.Encoding": "4.0.11", "System.Threading": "4.0.11", "System.Xml.ReaderWriter": "4.0.11"}, "compile": {"ref/netstandard1.3/_._": {}}, "runtime": {"lib/netstandard1.3/System.Xml.XmlDocument.dll": {}}}, "System.Xml.XPath/4.0.1": {"type": "package", "dependencies": {"System.Collections": "4.0.11", "System.Diagnostics.Debug": "4.0.11", "System.Globalization": "4.0.11", "System.IO": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11", "System.Xml.ReaderWriter": "4.0.11"}, "compile": {"ref/netstandard1.3/_._": {}}, "runtime": {"lib/netstandard1.3/System.Xml.XPath.dll": {}}}, "System.Xml.XPath.XDocument/4.0.1": {"type": "package", "dependencies": {"System.Diagnostics.Debug": "4.0.11", "System.Linq": "4.1.0", "System.Resources.ResourceManager": "4.0.1", "System.Runtime": "4.1.0", "System.Runtime.Extensions": "4.1.0", "System.Threading": "4.0.11", "System.Xml.ReaderWriter": "4.0.11", "System.Xml.XDocument": "4.0.11", "System.Xml.XPath": "4.0.1"}, "compile": {"ref/netstandard1.3/_._": {}}, "runtime": {"lib/netstandard1.3/System.Xml.XPath.XDocument.dll": {}}}}}, "libraries": {"Libuv/1.9.0": {"sha512": "9Q7AaqtQhS8JDSIvRBt6ODSLWDBI4c8YxNxyCQemWebBFUtBbc6M5Vi5Gz1ZyIUlTW3rZK9bIr5gnVyv0z7a2Q==", "type": "package", "path": "Libuv/1.9.0", "files": ["Libuv.1.9.0.nupkg.sha512", "Libuv.nuspec", "License.txt", "runtimes/debian-x64/native/libuv.so", "runtimes/fedora-x64/native/libuv.so", "runtimes/opensuse-x64/native/libuv.so", "runtimes/osx/native/libuv.dylib", "runtimes/rhel-x64/native/libuv.so", "runtimes/win7-arm/native/libuv.dll", "runtimes/win7-x64/native/libuv.dll", "runtimes/win7-x86/native/libuv.dll"]}, "Microsoft.AspNetCore.Antiforgery/1.0.0": {"sha512": "oJnrSvL6S7jM2eD/TR/Kyp/7O6pKvN+8FcnYvUaxaHbKlISwl98o44uidzePBjGxTf4fh9NFEx/q3OuuxAvBzw==", "type": "package", "path": "Microsoft.AspNetCore.Antiforgery/1.0.0", "files": ["Microsoft.AspNetCore.Antiforgery.1.0.0.nupkg.sha512", "Microsoft.AspNetCore.Antiforgery.nuspec", "lib/net451/Microsoft.AspNetCore.Antiforgery.dll", "lib/net451/Microsoft.AspNetCore.Antiforgery.xml", "lib/netstandard1.3/Microsoft.AspNetCore.Antiforgery.dll", "lib/netstandard1.3/Microsoft.AspNetCore.Antiforgery.xml"]}, "Microsoft.AspNetCore.Authorization/1.0.0": {"sha512": "iVFQ5xHSyxmfWYdl5B/xIFzXgm4SRgYQUKlLFVNGfEhbbjw0Ur2pfVrEvpENrhHFOQ2XAZcuFlGxSIzZwsVrMg==", "type": "package", "path": "Microsoft.AspNetCore.Authorization/1.0.0", "files": ["Microsoft.AspNetCore.Authorization.1.0.0.nupkg.sha512", "Microsoft.AspNetCore.Authorization.nuspec", "lib/net451/Microsoft.AspNetCore.Authorization.dll", "lib/net451/Microsoft.AspNetCore.Authorization.xml", "lib/netstandard1.3/Microsoft.AspNetCore.Authorization.dll", "lib/netstandard1.3/Microsoft.AspNetCore.Authorization.xml"]}, "Microsoft.AspNetCore.Cors/1.0.0": {"sha512": "fC8lWOU3+ltkbgQyD1P7eRQ66fGfZkPNU2UkwOI8tyF5FUsd8nRTfzvsO4mSyQfgmgfk2Hc8TGzx/okevZwXkg==", "type": "package", "path": "Microsoft.AspNetCore.Cors/1.0.0", "files": ["Microsoft.AspNetCore.Cors.1.0.0.nupkg.sha512", "Microsoft.AspNetCore.Cors.nuspec", "lib/net451/Microsoft.AspNetCore.Cors.dll", "lib/net451/Microsoft.AspNetCore.Cors.xml", "lib/netstandard1.3/Microsoft.AspNetCore.Cors.dll", "lib/netstandard1.3/Microsoft.AspNetCore.Cors.xml"]}, "Microsoft.AspNetCore.Cryptography.Internal/1.0.0": {"sha512": "0btvxwOqYNpKTUQrD7LA3p6Wi0vrhfWGBVqIKPS1KtEdkCv3QoVgFO4eJYuClGDS9NXhqk7TWh46/8x8wtZHaw==", "type": "package", "path": "Microsoft.AspNetCore.Cryptography.Internal/1.0.0", "files": ["Microsoft.AspNetCore.Cryptography.Internal.1.0.0.nupkg.sha512", "Microsoft.AspNetCore.Cryptography.Internal.nuspec", "lib/net451/Microsoft.AspNetCore.Cryptography.Internal.dll", "lib/net451/Microsoft.AspNetCore.Cryptography.Internal.xml", "lib/netstandard1.3/Microsoft.AspNetCore.Cryptography.Internal.dll", "lib/netstandard1.3/Microsoft.AspNetCore.Cryptography.Internal.xml"]}, "Microsoft.AspNetCore.DataProtection/1.0.0": {"sha512": "gt4URT+8ljPk0ePspLqOGPJBm+s6iMvsZqweplhf7wiZSjFiG1uYBNpQ/0dFY7wSx3NMRjekyXzCjvkGAV570g==", "type": "package", "path": "Microsoft.AspNetCore.DataProtection/1.0.0", "files": ["Microsoft.AspNetCore.DataProtection.1.0.0.nupkg.sha512", "Microsoft.AspNetCore.DataProtection.nuspec", "lib/net451/Microsoft.AspNetCore.DataProtection.dll", "lib/net451/Microsoft.AspNetCore.DataProtection.xml", "lib/netstandard1.3/Microsoft.AspNetCore.DataProtection.dll", "lib/netstandard1.3/Microsoft.AspNetCore.DataProtection.xml"]}, "Microsoft.AspNetCore.DataProtection.Abstractions/1.0.0": {"sha512": "h5ycDgkqmRdManmYMQVJgzNI7YtVp2X2/os1cKmdfrpfq+m9L8bMKhbd7PCksoLci+aYTOSn45khPl+hpPb9ug==", "type": "package", "path": "Microsoft.AspNetCore.DataProtection.Abstractions/1.0.0", "files": ["Microsoft.AspNetCore.DataProtection.Abstractions.1.0.0.nupkg.sha512", "Microsoft.AspNetCore.DataProtection.Abstractions.nuspec", "lib/net451/Microsoft.AspNetCore.DataProtection.Abstractions.dll", "lib/net451/Microsoft.AspNetCore.DataProtection.Abstractions.xml", "lib/netstandard1.3/Microsoft.AspNetCore.DataProtection.Abstractions.dll", "lib/netstandard1.3/Microsoft.AspNetCore.DataProtection.Abstractions.xml"]}, "Microsoft.AspNetCore.Diagnostics.Abstractions/1.0.0": {"sha512": "RrXsm5Xzvxs0OFDhRcIIUNOM5rXKnRWj/bIkuDkXNIBniGcPDrfGbACIatA127I6pmybNAE84puFAt3wsU2kww==", "type": "package", "path": "Microsoft.AspNetCore.Diagnostics.Abstractions/1.0.0", "files": ["Microsoft.AspNetCore.Diagnostics.Abstractions.1.0.0.nupkg.sha512", "Microsoft.AspNetCore.Diagnostics.Abstractions.nuspec", "lib/netstandard1.0/Microsoft.AspNetCore.Diagnostics.Abstractions.dll", "lib/netstandard1.0/Microsoft.AspNetCore.Diagnostics.Abstractions.xml"]}, "Microsoft.AspNetCore.Hosting/1.0.0": {"sha512": "0M7ZRAxTmGHOQV3B5Lm30VBg33uxxkPIKAxMc/C9yFBMPWPfk6V1uvb2ZL5eEPlo9/MZooITyMcGBQUHiakFjg==", "type": "package", "path": "Microsoft.AspNetCore.Hosting/1.0.0", "files": ["Microsoft.AspNetCore.Hosting.1.0.0.nupkg.sha512", "Microsoft.AspNetCore.Hosting.nuspec", "lib/net451/Microsoft.AspNetCore.Hosting.dll", "lib/net451/Microsoft.AspNetCore.Hosting.xml", "lib/netstandard1.3/Microsoft.AspNetCore.Hosting.dll", "lib/netstandard1.3/Microsoft.AspNetCore.Hosting.xml"]}, "Microsoft.AspNetCore.Hosting.Abstractions/1.0.0": {"sha512": "8r6qOl1jYyC523ZKM1QNl+6ijIoYWELWm0tpEWqtTIOg9DytHJWshB7usgqiuRmfHXM0EUziR6ouFY7iP7Tuzw==", "type": "package", "path": "Microsoft.AspNetCore.Hosting.Abstractions/1.0.0", "files": ["Microsoft.AspNetCore.Hosting.Abstractions.1.0.0.nupkg.sha512", "Microsoft.AspNetCore.Hosting.Abstractions.nuspec", "lib/net451/Microsoft.AspNetCore.Hosting.Abstractions.dll", "lib/net451/Microsoft.AspNetCore.Hosting.Abstractions.xml", "lib/netstandard1.3/Microsoft.AspNetCore.Hosting.Abstractions.dll", "lib/netstandard1.3/Microsoft.AspNetCore.Hosting.Abstractions.xml"]}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/1.0.0": {"sha512": "sHZyhQEoW15T9E36rfdm5Ux6a6RZB0KNM79ccf2IplWASqmlRGhX4ydU3dzQRLhkHpLx16fnWOL0KScsO6BevQ==", "type": "package", "path": "Microsoft.AspNetCore.Hosting.Server.Abstractions/1.0.0", "files": ["Microsoft.AspNetCore.Hosting.Server.Abstractions.1.0.0.nupkg.sha512", "Microsoft.AspNetCore.Hosting.Server.Abstractions.nuspec", "lib/net451/Microsoft.AspNetCore.Hosting.Server.Abstractions.dll", "lib/net451/Microsoft.AspNetCore.Hosting.Server.Abstractions.xml", "lib/netstandard1.3/Microsoft.AspNetCore.Hosting.Server.Abstractions.dll", "lib/netstandard1.3/Microsoft.AspNetCore.Hosting.Server.Abstractions.xml"]}, "Microsoft.AspNetCore.Html.Abstractions/1.0.0": {"sha512": "/JLMu2k8FiInLZC0SHXT+Cmdzi7AYa3B5v9w32Kd0mPTH4RYIQo/XNPIOr2HsPTXp3I9cZo1DajaMVGnJMN2QA==", "type": "package", "path": "Microsoft.AspNetCore.Html.Abstractions/1.0.0", "files": ["Microsoft.AspNetCore.Html.Abstractions.1.0.0.nupkg.sha512", "Microsoft.AspNetCore.Html.Abstractions.nuspec", "lib/netstandard1.0/Microsoft.AspNetCore.Html.Abstractions.dll", "lib/netstandard1.0/Microsoft.AspNetCore.Html.Abstractions.xml"]}, "Microsoft.AspNetCore.Http/1.0.0": {"sha512": "c/+eWVWQ8fX5hBHhL1BY4k2n4kVyUnqJLSCj0sTTXwRTU6IKoGbTOUqHT9as8C71Vk54YpAsi/VPmGW7T/ap3A==", "type": "package", "path": "Microsoft.AspNetCore.Http/1.0.0", "files": ["Microsoft.AspNetCore.Http.1.0.0.nupkg.sha512", "Microsoft.AspNetCore.Http.nuspec", "lib/net451/Microsoft.AspNetCore.Http.dll", "lib/net451/Microsoft.AspNetCore.Http.xml", "lib/netstandard1.3/Microsoft.AspNetCore.Http.dll", "lib/netstandard1.3/Microsoft.AspNetCore.Http.xml"]}, "Microsoft.AspNetCore.Http.Abstractions/1.0.0": {"sha512": "OJHlqdJOWKKBfsiVdX4Z4KCNuqvBIu6+1MVKuejRDyHnGyMkNHNoP/dtVzhPqvJXaJg9N4HlD0XNc6GDCFVffg==", "type": "package", "path": "Microsoft.AspNetCore.Http.Abstractions/1.0.0", "files": ["Microsoft.AspNetCore.Http.Abstractions.1.0.0.nupkg.sha512", "Microsoft.AspNetCore.Http.Abstractions.nuspec", "lib/net451/Microsoft.AspNetCore.Http.Abstractions.dll", "lib/net451/Microsoft.AspNetCore.Http.Abstractions.xml", "lib/netstandard1.3/Microsoft.AspNetCore.Http.Abstractions.dll", "lib/netstandard1.3/Microsoft.AspNetCore.Http.Abstractions.xml"]}, "Microsoft.AspNetCore.Http.Extensions/1.0.0": {"sha512": "GlvCPRpnw2jjHLdbGf/C28NQZLMeX1mugv5BS1a3FCQOJYyuwQZil4JwblR0frLyVrUVoJQ7UXRNZIzEVlO5XA==", "type": "package", "path": "Microsoft.AspNetCore.Http.Extensions/1.0.0", "files": ["Microsoft.AspNetCore.Http.Extensions.1.0.0.nupkg.sha512", "Microsoft.AspNetCore.Http.Extensions.nuspec", "lib/net451/Microsoft.AspNetCore.Http.Extensions.dll", "lib/net451/Microsoft.AspNetCore.Http.Extensions.xml", "lib/netstandard1.3/Microsoft.AspNetCore.Http.Extensions.dll", "lib/netstandard1.3/Microsoft.AspNetCore.Http.Extensions.xml"]}, "Microsoft.AspNetCore.Http.Features/1.0.0": {"sha512": "6x7zgfbTo1gL9xMEb7EMO2ES/48bqwnWyfH09z+ubWhnzxdhHls8rtqstPylu5FPD9nid6Vo2pgDm5vufRAy5Q==", "type": "package", "path": "Microsoft.AspNetCore.Http.Features/1.0.0", "files": ["Microsoft.AspNetCore.Http.Features.1.0.0.nupkg.sha512", "Microsoft.AspNetCore.Http.Features.nuspec", "lib/net451/Microsoft.AspNetCore.Http.Features.dll", "lib/net451/Microsoft.AspNetCore.Http.Features.xml", "lib/netstandard1.3/Microsoft.AspNetCore.Http.Features.dll", "lib/netstandard1.3/Microsoft.AspNetCore.Http.Features.xml"]}, "Microsoft.AspNetCore.HttpOverrides/1.0.0": {"sha512": "gHpdaaAzhaTWJZuJVo3ler2zzdQWrm8wnsoSjcNtoZZdTOkwImndRwK8o4GYoM18dfmfNheM7i4EENI7XHM/lA==", "type": "package", "path": "Microsoft.AspNetCore.HttpOverrides/1.0.0", "files": ["Microsoft.AspNetCore.HttpOverrides.1.0.0.nupkg.sha512", "Microsoft.AspNetCore.HttpOverrides.nuspec", "lib/net451/Microsoft.AspNetCore.HttpOverrides.dll", "lib/net451/Microsoft.AspNetCore.HttpOverrides.xml", "lib/netstandard1.3/Microsoft.AspNetCore.HttpOverrides.dll", "lib/netstandard1.3/Microsoft.AspNetCore.HttpOverrides.xml"]}, "Microsoft.AspNetCore.JsonPatch/1.0.0": {"sha512": "WVaSVS+dDlWCR/qerHnBxU9tIeJ9GMA3M5tg4cxH7/cJYZZLnr2zvaFHGB+cRRNCKKTJ0pFRxT7ES8knhgAAaA==", "type": "package", "path": "Microsoft.AspNetCore.JsonPatch/1.0.0", "files": ["Microsoft.AspNetCore.JsonPatch.1.0.0.nupkg.sha512", "Microsoft.AspNetCore.JsonPatch.nuspec", "lib/netstandard1.1/Microsoft.AspNetCore.JsonPatch.dll", "lib/netstandard1.1/Microsoft.AspNetCore.JsonPatch.xml"]}, "Microsoft.AspNetCore.Localization/1.0.0": {"sha512": "DF/maMd9f6ZPoTlU8n6/AGm3fpZNPiiip34bPrBQuloX2a5O0KwyV72qKhJhJNqmVVnDnTu8XYT16ysoFXRxQA==", "type": "package", "path": "Microsoft.AspNetCore.Localization/1.0.0", "files": ["Microsoft.AspNetCore.Localization.1.0.0.nupkg.sha512", "Microsoft.AspNetCore.Localization.nuspec", "lib/net451/Microsoft.AspNetCore.Localization.dll", "lib/net451/Microsoft.AspNetCore.Localization.xml", "lib/netstandard1.3/Microsoft.AspNetCore.Localization.dll", "lib/netstandard1.3/Microsoft.AspNetCore.Localization.xml"]}, "Microsoft.AspNetCore.Mvc/1.0.0": {"sha512": "nNiMnzdXHpMrsjnBRiYaVy5EMsCmTsqSIIOtJvMbqJldh1i3NCM9jgvp4Da+Ke1gkGd2/MK8rXp+8a5yF+QOOQ==", "type": "package", "path": "Microsoft.AspNetCore.Mvc/1.0.0", "files": ["Microsoft.AspNetCore.Mvc.1.0.0.nupkg.sha512", "Microsoft.AspNetCore.Mvc.nuspec", "lib/net451/Microsoft.AspNetCore.Mvc.dll", "lib/net451/Microsoft.AspNetCore.Mvc.xml", "lib/netstandard1.6/Microsoft.AspNetCore.Mvc.dll", "lib/netstandard1.6/Microsoft.AspNetCore.Mvc.xml"]}, "Microsoft.AspNetCore.Mvc.Abstractions/1.0.0": {"sha512": "d7KEexDwxSwVeZv+SDbsMRPl2WuKMVckOCp/KTGuI1NJhd/7GvNGW101iRIC3tC/yym0PaajcWwTZNVfjhyoJw==", "type": "package", "path": "Microsoft.AspNetCore.Mvc.Abstractions/1.0.0", "files": ["Microsoft.AspNetCore.Mvc.Abstractions.1.0.0.nupkg.sha512", "Microsoft.AspNetCore.Mvc.Abstractions.nuspec", "lib/net451/Microsoft.AspNetCore.Mvc.Abstractions.dll", "lib/net451/Microsoft.AspNetCore.Mvc.Abstractions.xml", "lib/netstandard1.3/Microsoft.AspNetCore.Mvc.Abstractions.dll", "lib/netstandard1.3/Microsoft.AspNetCore.Mvc.Abstractions.xml"]}, "Microsoft.AspNetCore.Mvc.ApiExplorer/1.0.0": {"sha512": "46aWHLmZ37c44bJzLdbSEmIxCwQo7BljHBoK8C9CPCEPOLPWmg0XyPhGyMSGY4woDmm9ukBOEpqT899BWSxhRw==", "type": "package", "path": "Microsoft.AspNetCore.Mvc.ApiExplorer/1.0.0", "files": ["Microsoft.AspNetCore.Mvc.ApiExplorer.1.0.0.nupkg.sha512", "Microsoft.AspNetCore.Mvc.ApiExplorer.nuspec", "lib/net451/Microsoft.AspNetCore.Mvc.ApiExplorer.dll", "lib/net451/Microsoft.AspNetCore.Mvc.ApiExplorer.xml", "lib/netstandard1.6/Microsoft.AspNetCore.Mvc.ApiExplorer.dll", "lib/netstandard1.6/Microsoft.AspNetCore.Mvc.ApiExplorer.xml"]}, "Microsoft.AspNetCore.Mvc.Core/1.0.0": {"sha512": "tjCOZJheOAKStHs4LIcrLsbF/00wEwSinC+vCFpsmdqGVl3/tX9jnID20E1NlkKOW68DOLBavoC23BWFiHa0JA==", "type": "package", "path": "Microsoft.AspNetCore.Mvc.Core/1.0.0", "files": ["Microsoft.AspNetCore.Mvc.Core.1.0.0.nupkg.sha512", "Microsoft.AspNetCore.Mvc.Core.nuspec", "lib/net451/Microsoft.AspNetCore.Mvc.Core.dll", "lib/net451/Microsoft.AspNetCore.Mvc.Core.xml", "lib/netstandard1.6/Microsoft.AspNetCore.Mvc.Core.dll", "lib/netstandard1.6/Microsoft.AspNetCore.Mvc.Core.xml"]}, "Microsoft.AspNetCore.Mvc.Cors/1.0.0": {"sha512": "jz3au6mm/O0ahotfUqZTGtsftcd4UYKIzl2l0+WRG817UJdMGLmnmgmUPcAQR1nrI0Dg49MsfTkjWoMQM9CsUw==", "type": "package", "path": "Microsoft.AspNetCore.Mvc.Cors/1.0.0", "files": ["Microsoft.AspNetCore.Mvc.Cors.1.0.0.nupkg.sha512", "Microsoft.AspNetCore.Mvc.Cors.nuspec", "lib/net451/Microsoft.AspNetCore.Mvc.Cors.dll", "lib/net451/Microsoft.AspNetCore.Mvc.Cors.xml", "lib/netstandard1.6/Microsoft.AspNetCore.Mvc.Cors.dll", "lib/netstandard1.6/Microsoft.AspNetCore.Mvc.Cors.xml"]}, "Microsoft.AspNetCore.Mvc.DataAnnotations/1.0.0": {"sha512": "ZU02Y2tnKu/lVv2ywnNO+nSRzDWiTlq+ZhSuR9L3Q9NqlCyQJXOgX+iD/BGshnMQ7ZTstjyO4h8WeF7Ii9vBWQ==", "type": "package", "path": "Microsoft.AspNetCore.Mvc.DataAnnotations/1.0.0", "files": ["Microsoft.AspNetCore.Mvc.DataAnnotations.1.0.0.nupkg.sha512", "Microsoft.AspNetCore.Mvc.DataAnnotations.nuspec", "lib/net451/Microsoft.AspNetCore.Mvc.DataAnnotations.dll", "lib/net451/Microsoft.AspNetCore.Mvc.DataAnnotations.xml", "lib/netstandard1.6/Microsoft.AspNetCore.Mvc.DataAnnotations.dll", "lib/netstandard1.6/Microsoft.AspNetCore.Mvc.DataAnnotations.xml"]}, "Microsoft.AspNetCore.Mvc.Formatters.Json/1.0.0": {"sha512": "XQQLbxYLmdRj2U685NxFIrxVxqsXHLO5zN4ZIhTQ23VxzI6Qk2WN9ska0tl4ZMDV/4pSyE8SlmNeKPCN3pW86w==", "type": "package", "path": "Microsoft.AspNetCore.Mvc.Formatters.Json/1.0.0", "files": ["Microsoft.AspNetCore.Mvc.Formatters.Json.1.0.0.nupkg.sha512", "Microsoft.AspNetCore.Mvc.Formatters.Json.nuspec", "lib/net451/Microsoft.AspNetCore.Mvc.Formatters.Json.dll", "lib/net451/Microsoft.AspNetCore.Mvc.Formatters.Json.xml", "lib/netstandard1.6/Microsoft.AspNetCore.Mvc.Formatters.Json.dll", "lib/netstandard1.6/Microsoft.AspNetCore.Mvc.Formatters.Json.xml"]}, "Microsoft.AspNetCore.Mvc.Localization/1.0.0": {"sha512": "+w4s6j88pzJmc++3IozCmo0AIOF8ks/LrOAuMTRm6ve/l+wTp/oqXu2tjLA3QAvP6n6hC3cm40qW69UhYUtSIQ==", "type": "package", "path": "Microsoft.AspNetCore.Mvc.Localization/1.0.0", "files": ["Microsoft.AspNetCore.Mvc.Localization.1.0.0.nupkg.sha512", "Microsoft.AspNetCore.Mvc.Localization.nuspec", "lib/net451/Microsoft.AspNetCore.Mvc.Localization.dll", "lib/net451/Microsoft.AspNetCore.Mvc.Localization.xml", "lib/netstandard1.6/Microsoft.AspNetCore.Mvc.Localization.dll", "lib/netstandard1.6/Microsoft.AspNetCore.Mvc.Localization.xml"]}, "Microsoft.AspNetCore.Mvc.Razor/1.0.0": {"sha512": "G17pVnANhBj6AdpzTnJV36MRx4KNLQao0NqGUyKFvtKjy77KR55Fmt6/MVykbOB5xH33fbMIveTiSF3h4kWSQA==", "type": "package", "path": "Microsoft.AspNetCore.Mvc.Razor/1.0.0", "files": ["Microsoft.AspNetCore.Mvc.Razor.1.0.0.nupkg.sha512", "Microsoft.AspNetCore.Mvc.Razor.nuspec", "lib/net451/Microsoft.AspNetCore.Mvc.Razor.dll", "lib/net451/Microsoft.AspNetCore.Mvc.Razor.xml", "lib/netstandard1.6/Microsoft.AspNetCore.Mvc.Razor.dll", "lib/netstandard1.6/Microsoft.AspNetCore.Mvc.Razor.xml"]}, "Microsoft.AspNetCore.Mvc.Razor.Host/1.0.0": {"sha512": "cMdbvKf56IRyviirKFAgwcUSxwzLVASRA8cgxQD6Bw/JO9uwpG33mWjMnsdmZveW0y/ek1FjHTx6Zd4UpZfQ6A==", "type": "package", "path": "Microsoft.AspNetCore.Mvc.Razor.Host/1.0.0", "files": ["Microsoft.AspNetCore.Mvc.Razor.Host.1.0.0.nupkg.sha512", "Microsoft.AspNetCore.Mvc.Razor.Host.nuspec", "lib/net451/Microsoft.AspNetCore.Mvc.Razor.Host.dll", "lib/net451/Microsoft.AspNetCore.Mvc.Razor.Host.xml", "lib/netstandard1.6/Microsoft.AspNetCore.Mvc.Razor.Host.dll", "lib/netstandard1.6/Microsoft.AspNetCore.Mvc.Razor.Host.xml"]}, "Microsoft.AspNetCore.Mvc.TagHelpers/1.0.0": {"sha512": "5IT4kddg3Tz3Ki53HvP3fvjnpYzKjY5mFWhmpPQvE2vzfMr7zU6X1Cls2SnJPMcV6sAqzTB4j6AmUmcEpFNMqg==", "type": "package", "path": "Microsoft.AspNetCore.Mvc.TagHelpers/1.0.0", "files": ["Microsoft.AspNetCore.Mvc.TagHelpers.1.0.0.nupkg.sha512", "Microsoft.AspNetCore.Mvc.TagHelpers.nuspec", "lib/net451/Microsoft.AspNetCore.Mvc.TagHelpers.dll", "lib/net451/Microsoft.AspNetCore.Mvc.TagHelpers.xml", "lib/netstandard1.6/Microsoft.AspNetCore.Mvc.TagHelpers.dll", "lib/netstandard1.6/Microsoft.AspNetCore.Mvc.TagHelpers.xml"]}, "Microsoft.AspNetCore.Mvc.ViewFeatures/1.0.0": {"sha512": "DNMCqY+TX5jgO3M1C7Lf5E61llWZ+QgtjLYfrIkq7yfZjhzI52nprFE3mh66HahKU1EvyOz9+ISdaSmTimfNbQ==", "type": "package", "path": "Microsoft.AspNetCore.Mvc.ViewFeatures/1.0.0", "files": ["Microsoft.AspNetCore.Mvc.ViewFeatures.1.0.0.nupkg.sha512", "Microsoft.AspNetCore.Mvc.ViewFeatures.nuspec", "lib/net451/Microsoft.AspNetCore.Mvc.ViewFeatures.dll", "lib/net451/Microsoft.AspNetCore.Mvc.ViewFeatures.xml", "lib/netstandard1.6/Microsoft.AspNetCore.Mvc.ViewFeatures.dll", "lib/netstandard1.6/Microsoft.AspNetCore.Mvc.ViewFeatures.xml"]}, "Microsoft.AspNetCore.Razor/1.0.0": {"sha512": "+vhlFn8n45hj1M91HYVm2ryLMZ+ZYR/OUdBVE8aUzkvkTVF+3UnNxSY3hAEugcgcbf9/XQTE+DDxEgN4LdYEjg==", "type": "package", "path": "Microsoft.AspNetCore.Razor/1.0.0", "files": ["Microsoft.AspNetCore.Razor.1.0.0.nupkg.sha512", "Microsoft.AspNetCore.Razor.nuspec", "lib/net451/Microsoft.AspNetCore.Razor.dll", "lib/net451/Microsoft.AspNetCore.Razor.xml", "lib/netstandard1.3/Microsoft.AspNetCore.Razor.dll", "lib/netstandard1.3/Microsoft.AspNetCore.Razor.xml"]}, "Microsoft.AspNetCore.Razor.Runtime/1.0.0": {"sha512": "hsq6xJeqDDb78akZuy79QE3kaCxcigD3vccbIaNrrz7JSXOzayfteF06ToK+J1SXSDRtrBj3XZZfrjiqIY/vCw==", "type": "package", "path": "Microsoft.AspNetCore.Razor.Runtime/1.0.0", "files": ["Microsoft.AspNetCore.Razor.Runtime.1.0.0.nupkg.sha512", "Microsoft.AspNetCore.Razor.Runtime.nuspec", "lib/net451/Microsoft.AspNetCore.Razor.Runtime.dll", "lib/net451/Microsoft.AspNetCore.Razor.Runtime.xml", "lib/netstandard1.5/Microsoft.AspNetCore.Razor.Runtime.dll", "lib/netstandard1.5/Microsoft.AspNetCore.Razor.Runtime.xml"]}, "Microsoft.AspNetCore.Routing/1.0.0": {"sha512": "NvFvRtYHXWjBbXz5/7F7JDNcdhrE+tG1/Q9R6LmMxFgu8tkl1bqtFZQbMy17FYFkmm8Fn/T81blRGE2nxCbDRA==", "type": "package", "path": "Microsoft.AspNetCore.Routing/1.0.0", "files": ["Microsoft.AspNetCore.Routing.1.0.0.nupkg.sha512", "Microsoft.AspNetCore.Routing.nuspec", "lib/net451/Microsoft.AspNetCore.Routing.dll", "lib/net451/Microsoft.AspNetCore.Routing.xml", "lib/netstandard1.3/Microsoft.AspNetCore.Routing.dll", "lib/netstandard1.3/Microsoft.AspNetCore.Routing.xml"]}, "Microsoft.AspNetCore.Routing.Abstractions/1.0.0": {"sha512": "Ne5CFiD1xCGSHrGICw7PsVnj7gijfkMfsw52AO6ingcUhE01dc87cJPpfGLnY22MIvqn11ECLbNZYmzFp/Rs+A==", "type": "package", "path": "Microsoft.AspNetCore.Routing.Abstractions/1.0.0", "files": ["Microsoft.AspNetCore.Routing.Abstractions.1.0.0.nupkg.sha512", "Microsoft.AspNetCore.Routing.Abstractions.nuspec", "lib/net451/Microsoft.AspNetCore.Routing.Abstractions.dll", "lib/net451/Microsoft.AspNetCore.Routing.Abstractions.xml", "lib/netstandard1.3/Microsoft.AspNetCore.Routing.Abstractions.dll", "lib/netstandard1.3/Microsoft.AspNetCore.Routing.Abstractions.xml"]}, "Microsoft.AspNetCore.Server.IISIntegration/1.0.0": {"sha512": "xmn6EivvL4Ymo7LP+Jc49WLcIiYsUiujZo0loEbAg473nY2dIHxcxncpFAKzPf/MzqN0qBtaXEP0igYJ813H3Q==", "type": "package", "path": "Microsoft.AspNetCore.Server.IISIntegration/1.0.0", "files": ["Microsoft.AspNetCore.Server.IISIntegration.1.0.0.nupkg.sha512", "Microsoft.AspNetCore.Server.IISIntegration.nuspec", "lib/net451/Microsoft.AspNetCore.Server.IISIntegration.dll", "lib/net451/Microsoft.AspNetCore.Server.IISIntegration.xml", "lib/netstandard1.3/Microsoft.AspNetCore.Server.IISIntegration.dll", "lib/netstandard1.3/Microsoft.AspNetCore.Server.IISIntegration.xml"]}, "Microsoft.AspNetCore.Server.Kestrel/1.0.0": {"sha512": "TNRTsufpdeoa88kR2NU+mO0IZIyJCcBurkdLx4I9d7MpLV1MCnRCrIeTgFIOWpB+j6kytUUXblzhsd0rfk6+bQ==", "type": "package", "path": "Microsoft.AspNetCore.Server.Kestrel/1.0.0", "files": ["Microsoft.AspNetCore.Server.Kestrel.1.0.0.nupkg.sha512", "Microsoft.AspNetCore.Server.Kestrel.nuspec", "lib/net451/Microsoft.AspNetCore.Server.Kestrel.dll", "lib/net451/Microsoft.AspNetCore.Server.Kestrel.xml", "lib/netstandard1.3/Microsoft.AspNetCore.Server.Kestrel.dll", "lib/netstandard1.3/Microsoft.AspNetCore.Server.Kestrel.xml"]}, "Microsoft.AspNetCore.WebUtilities/1.0.0": {"sha512": "D0licSnS1JgqQ/gYlN41wXbeYG3dFIdjY781YzMHZ5gBB7kczacshW+H6plZkXRr/cCnAJWGa31o1R8c5GEy/A==", "type": "package", "path": "Microsoft.AspNetCore.WebUtilities/1.0.0", "files": ["Microsoft.AspNetCore.WebUtilities.1.0.0.nupkg.sha512", "Microsoft.AspNetCore.WebUtilities.nuspec", "lib/net451/Microsoft.AspNetCore.WebUtilities.dll", "lib/net451/Microsoft.AspNetCore.WebUtilities.xml", "lib/netstandard1.3/Microsoft.AspNetCore.WebUtilities.dll", "lib/netstandard1.3/Microsoft.AspNetCore.WebUtilities.xml"]}, "Microsoft.CodeAnalysis.Analyzers/1.1.0": {"sha512": "HS3iRWZKcUw/8eZ/08GXKY2Bn7xNzQPzf8gRPHGSowX7u7XXu9i9YEaBeBNKUXWfI7qjvT2zXtLUvbN0hds8vg==", "type": "package", "path": "Microsoft.CodeAnalysis.Analyzers/1.1.0", "files": ["Microsoft.CodeAnalysis.Analyzers.1.1.0.nupkg.sha512", "Microsoft.CodeAnalysis.Analyzers.nuspec", "ThirdPartyNotices.rtf", "analyzers/dotnet/cs/Microsoft.CodeAnalysis.Analyzers.dll", "analyzers/dotnet/cs/Microsoft.CodeAnalysis.CSharp.Analyzers.dll", "analyzers/dotnet/vb/Microsoft.CodeAnalysis.Analyzers.dll", "analyzers/dotnet/vb/Microsoft.CodeAnalysis.VisualBasic.Analyzers.dll", "tools/install.ps1", "tools/uninstall.ps1"]}, "Microsoft.CodeAnalysis.Common/1.3.0": {"sha512": "V09G35cs0CT1C4Dr1IEOh8IGfnWALEVAOO5JXsqagxXwmYR012TlorQ+vx2eXxfZRKs3gAS/r92gN9kRBLba5A==", "type": "package", "path": "Microsoft.CodeAnalysis.Common/1.3.0", "files": ["Microsoft.CodeAnalysis.Common.1.3.0.nupkg.sha512", "Microsoft.CodeAnalysis.Common.nuspec", "ThirdPartyNotices.rtf", "lib/net45/Microsoft.CodeAnalysis.dll", "lib/net45/Microsoft.CodeAnalysis.xml", "lib/netstandard1.3/Microsoft.CodeAnalysis.dll", "lib/netstandard1.3/Microsoft.CodeAnalysis.xml", "lib/portable-net45+win8/Microsoft.CodeAnalysis.dll", "lib/portable-net45+win8/Microsoft.CodeAnalysis.xml"]}, "Microsoft.CodeAnalysis.CSharp/1.3.0": {"sha512": "BgWDIAbSFsHuGeLSn/rljLi51nXqkSo4DZ0qEIrHyPVasrhxEVq7aV8KKZ3HEfSFB+GIhBmOogE+mlOLYg19eg==", "type": "package", "path": "Microsoft.CodeAnalysis.CSharp/1.3.0", "files": ["Microsoft.CodeAnalysis.CSharp.1.3.0.nupkg.sha512", "Microsoft.CodeAnalysis.CSharp.nuspec", "ThirdPartyNotices.rtf", "lib/net45/Microsoft.CodeAnalysis.CSharp.dll", "lib/net45/Microsoft.CodeAnalysis.CSharp.xml", "lib/netstandard1.3/Microsoft.CodeAnalysis.CSharp.dll", "lib/netstandard1.3/Microsoft.CodeAnalysis.CSharp.xml", "lib/portable-net45+win8/Microsoft.CodeAnalysis.CSharp.dll", "lib/portable-net45+win8/Microsoft.CodeAnalysis.CSharp.xml"]}, "Microsoft.CodeAnalysis.VisualBasic/1.3.0": {"sha512": "Sf3k8PkTkWqBmXnnblJbvb7ewO6mJzX6WO2t7m04BmOY5qBq6yhhyXnn/BMM+QCec3Arw3X35Zd8f9eBql0qgg==", "type": "package", "path": "Microsoft.CodeAnalysis.VisualBasic/1.3.0", "files": ["Microsoft.CodeAnalysis.VisualBasic.1.3.0.nupkg.sha512", "Microsoft.CodeAnalysis.VisualBasic.nuspec", "ThirdPartyNotices.rtf", "lib/net45/Microsoft.CodeAnalysis.VisualBasic.dll", "lib/net45/Microsoft.CodeAnalysis.VisualBasic.xml", "lib/netstandard1.3/Microsoft.CodeAnalysis.VisualBasic.dll", "lib/netstandard1.3/Microsoft.CodeAnalysis.VisualBasic.xml", "lib/portable-net45+win8/Microsoft.CodeAnalysis.VisualBasic.dll", "lib/portable-net45+win8/Microsoft.CodeAnalysis.VisualBasic.xml"]}, "Microsoft.CSharp/4.0.1": {"sha512": "17h8b5mXa87XYKrrVqdgZ38JefSUqLChUQpXgSnpzsM0nDOhE40FTeNWOJ/YmySGV6tG6T8+hjz6vxbknHJr6A==", "type": "package", "path": "Microsoft.CSharp/4.0.1", "files": ["Microsoft.CSharp.4.0.1.nupkg.sha512", "Microsoft.CSharp.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/Microsoft.CSharp.dll", "lib/netstandard1.3/Microsoft.CSharp.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/Microsoft.CSharp.dll", "ref/netcore50/Microsoft.CSharp.xml", "ref/netcore50/de/Microsoft.CSharp.xml", "ref/netcore50/es/Microsoft.CSharp.xml", "ref/netcore50/fr/Microsoft.CSharp.xml", "ref/netcore50/it/Microsoft.CSharp.xml", "ref/netcore50/ja/Microsoft.CSharp.xml", "ref/netcore50/ko/Microsoft.CSharp.xml", "ref/netcore50/ru/Microsoft.CSharp.xml", "ref/netcore50/zh-hans/Microsoft.CSharp.xml", "ref/netcore50/zh-hant/Microsoft.CSharp.xml", "ref/netstandard1.0/Microsoft.CSharp.dll", "ref/netstandard1.0/Microsoft.CSharp.xml", "ref/netstandard1.0/de/Microsoft.CSharp.xml", "ref/netstandard1.0/es/Microsoft.CSharp.xml", "ref/netstandard1.0/fr/Microsoft.CSharp.xml", "ref/netstandard1.0/it/Microsoft.CSharp.xml", "ref/netstandard1.0/ja/Microsoft.CSharp.xml", "ref/netstandard1.0/ko/Microsoft.CSharp.xml", "ref/netstandard1.0/ru/Microsoft.CSharp.xml", "ref/netstandard1.0/zh-hans/Microsoft.CSharp.xml", "ref/netstandard1.0/zh-hant/Microsoft.CSharp.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "Microsoft.DotNet.InternalAbstractions/1.0.0": {"sha512": "AAguUq7YyKk3yDWPoWA8DrLZvURxB/LrDdTn1h5lmPeznkFUpfC3p459w5mQYQE0qpquf/CkSQZ0etiV5vRHFA==", "type": "package", "path": "Microsoft.DotNet.InternalAbstractions/1.0.0", "files": ["Microsoft.DotNet.InternalAbstractions.1.0.0.nupkg.sha512", "Microsoft.DotNet.InternalAbstractions.nuspec", "lib/net451/Microsoft.DotNet.InternalAbstractions.dll", "lib/netstandard1.3/Microsoft.DotNet.InternalAbstractions.dll"]}, "Microsoft.Extensions.Caching.Abstractions/1.0.0": {"sha512": "IxlFDVOchL6tdR05bk7EiJvMtvZrVkZXBhkbXqc3GxOHOrHFGcN+92WoWFPeBpdpy8ot/Px5ZdXzt7k+2n1Bdg==", "type": "package", "path": "Microsoft.Extensions.Caching.Abstractions/1.0.0", "files": ["Microsoft.Extensions.Caching.Abstractions.1.0.0.nupkg.sha512", "Microsoft.Extensions.Caching.Abstractions.nuspec", "lib/netstandard1.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/netstandard1.0/Microsoft.Extensions.Caching.Abstractions.xml"]}, "Microsoft.Extensions.Caching.Memory/1.0.0": {"sha512": "6+7zTufCnZ+tfrUo7RbIRR3LB0BxwOwxfXuo0IbLyIvgoToGpWuz5wYEDfCYNOvpig9tY8FA0I1uRHYmITMXMQ==", "type": "package", "path": "Microsoft.Extensions.Caching.Memory/1.0.0", "files": ["Microsoft.Extensions.Caching.Memory.1.0.0.nupkg.sha512", "Microsoft.Extensions.Caching.Memory.nuspec", "lib/net451/Microsoft.Extensions.Caching.Memory.dll", "lib/net451/Microsoft.Extensions.Caching.Memory.xml", "lib/netstandard1.3/Microsoft.Extensions.Caching.Memory.dll", "lib/netstandard1.3/Microsoft.Extensions.Caching.Memory.xml"]}, "Microsoft.Extensions.Configuration/1.0.0": {"sha512": "hR4yYebruRp6qyFnV3RW4qrnEb0J1LnMmQbj50AUA423V8dMs4E3YAohsyRyGBFnpbJ+KKzieSG/n2A6T0klZQ==", "type": "package", "path": "Microsoft.Extensions.Configuration/1.0.0", "files": ["Microsoft.Extensions.Configuration.1.0.0.nupkg.sha512", "Microsoft.Extensions.Configuration.nuspec", "lib/netstandard1.1/Microsoft.Extensions.Configuration.dll", "lib/netstandard1.1/Microsoft.Extensions.Configuration.xml"]}, "Microsoft.Extensions.Configuration.Abstractions/1.0.0": {"sha512": "nJ+Et/rnDMDmGhxvFAKdN3va7y+YDPICv1nUEP8I4IKgOkWwr/dCZHMqxVhJFrkbW9ux8Kd7erC4mvxfZh0WnA==", "type": "package", "path": "Microsoft.Extensions.Configuration.Abstractions/1.0.0", "files": ["Microsoft.Extensions.Configuration.Abstractions.1.0.0.nupkg.sha512", "Microsoft.Extensions.Configuration.Abstractions.nuspec", "lib/netstandard1.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netstandard1.0/Microsoft.Extensions.Configuration.Abstractions.xml"]}, "Microsoft.Extensions.Configuration.Binder/1.0.0": {"sha512": "kK8QuBcPQtmKJCkC9enc1uMRFa++mPTuVNm2K5jDVXcAYKRBcFSbdEBvIe1JIgA6dEsAQeqjfHfKSaUJ8f5NFQ==", "type": "package", "path": "Microsoft.Extensions.Configuration.Binder/1.0.0", "files": ["Microsoft.Extensions.Configuration.Binder.1.0.0.nupkg.sha512", "Microsoft.Extensions.Configuration.Binder.nuspec", "lib/netstandard1.1/Microsoft.Extensions.Configuration.Binder.dll", "lib/netstandard1.1/Microsoft.Extensions.Configuration.Binder.xml"]}, "Microsoft.Extensions.Configuration.EnvironmentVariables/1.0.0": {"sha512": "A0yqS98VtPNlFkFI7YBlwkAekUHE/9mMeNc+K4RmgTjCrskuk6pX3LGhDU7aD5CPYc9Px7M2To/2u4xDSnRikg==", "type": "package", "path": "Microsoft.Extensions.Configuration.EnvironmentVariables/1.0.0", "files": ["Microsoft.Extensions.Configuration.EnvironmentVariables.1.0.0.nupkg.sha512", "Microsoft.Extensions.Configuration.EnvironmentVariables.nuspec", "lib/net451/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/net451/Microsoft.Extensions.Configuration.EnvironmentVariables.xml", "lib/netstandard1.3/Microsoft.Extensions.Configuration.EnvironmentVariables.dll", "lib/netstandard1.3/Microsoft.Extensions.Configuration.EnvironmentVariables.xml"]}, "Microsoft.Extensions.Configuration.FileExtensions/1.0.0": {"sha512": "MO7XtmLiqnpgVTX34uzFPvIS7jPmBUGLN0MP5MsYu6CqYTIs90ULjtrV5TegH5mTqKTXWjZRGXL26R6apTyc4w==", "type": "package", "path": "Microsoft.Extensions.Configuration.FileExtensions/1.0.0", "files": ["Microsoft.Extensions.Configuration.FileExtensions.1.0.0.nupkg.sha512", "Microsoft.Extensions.Configuration.FileExtensions.nuspec", "lib/net451/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net451/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/netstandard1.3/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/netstandard1.3/Microsoft.Extensions.Configuration.FileExtensions.xml"]}, "Microsoft.Extensions.Configuration.Json/1.0.0": {"sha512": "KRyEOe5/Xi3qyDMdEVh++e2pQRsI6C3wzINVExOcu9lOsFmXK/k4qOf244fyo59rnn6s5xKnIW3WbhxWS1hu2w==", "type": "package", "path": "Microsoft.Extensions.Configuration.Json/1.0.0", "files": ["Microsoft.Extensions.Configuration.Json.1.0.0.nupkg.sha512", "Microsoft.Extensions.Configuration.Json.nuspec", "lib/net451/Microsoft.Extensions.Configuration.Json.dll", "lib/net451/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard1.3/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard1.3/Microsoft.Extensions.Configuration.Json.xml"]}, "Microsoft.Extensions.DependencyInjection/1.0.0": {"sha512": "zdtkiZNV6LB8xtpmfyUjP/9N9ZCL/ydQ+0bfjun38fbrk+MDEm9M2yeLzRdq+OIt5xExw/KU04wFaVwJ1bhQPg==", "type": "package", "path": "Microsoft.Extensions.DependencyInjection/1.0.0", "files": ["Microsoft.Extensions.DependencyInjection.1.0.0.nupkg.sha512", "Microsoft.Extensions.DependencyInjection.nuspec", "lib/netstandard1.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard1.1/Microsoft.Extensions.DependencyInjection.xml"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/1.0.0": {"sha512": "+XwaNo3o9RhLQhUnnOBCaukeRi1X9yYc0Fzye9RlErSflKZdw0VgHtn6rvKo0FTionsW0x8QVULhKH+nkqVjQA==", "type": "package", "path": "Microsoft.Extensions.DependencyInjection.Abstractions/1.0.0", "files": ["Microsoft.Extensions.DependencyInjection.Abstractions.1.0.0.nupkg.sha512", "Microsoft.Extensions.DependencyInjection.Abstractions.nuspec", "lib/netstandard1.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard1.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml"]}, "Microsoft.Extensions.DependencyModel/1.0.0": {"sha512": "n55Y2T4qMgCNMrJaqAN+nlG2EH4XL+e9uxIg4vdFsQeF+L8UKxRdD3C35Bt+xk3vO3Zwp3g+6KFq2VPH2COSmg==", "type": "package", "path": "Microsoft.Extensions.DependencyModel/1.0.0", "files": ["Microsoft.Extensions.DependencyModel.1.0.0.nupkg.sha512", "Microsoft.Extensions.DependencyModel.nuspec", "lib/net451/Microsoft.Extensions.DependencyModel.dll", "lib/netstandard1.6/Microsoft.Extensions.DependencyModel.dll"]}, "Microsoft.Extensions.FileProviders.Abstractions/1.0.0": {"sha512": "4jsqTxG3py/hYSsOtZMkNJ2/CQqPdpwyK7bDUkrwHgqowCFSmx/C+R4IzQ+2AK2Up1fVcu+ldC0gktwidL828A==", "type": "package", "path": "Microsoft.Extensions.FileProviders.Abstractions/1.0.0", "files": ["Microsoft.Extensions.FileProviders.Abstractions.1.0.0.nupkg.sha512", "Microsoft.Extensions.FileProviders.Abstractions.nuspec", "lib/netstandard1.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/netstandard1.0/Microsoft.Extensions.FileProviders.Abstractions.xml"]}, "Microsoft.Extensions.FileProviders.Composite/1.0.0": {"sha512": "4nbDQfagNr1eILXSFZbTNuAKuZ6SsOyK6ySTMryo67ECi8+EcZBQ12E0aXcxX/aT3v+3pbWSt71NXlEm8tKIxw==", "type": "package", "path": "Microsoft.Extensions.FileProviders.Composite/1.0.0", "files": ["Microsoft.Extensions.FileProviders.Composite.1.0.0.nupkg.sha512", "Microsoft.Extensions.FileProviders.Composite.nuspec", "lib/netstandard1.0/Microsoft.Extensions.FileProviders.Composite.dll", "lib/netstandard1.0/Microsoft.Extensions.FileProviders.Composite.xml"]}, "Microsoft.Extensions.FileProviders.Physical/1.0.0": {"sha512": "Ej5hGWtK3xM9YU+B2O8EdlMcJf5utbDQs9ecnfvwhENQeeNU7iI2jjnRB2d7V6o9SQZmNHPzdPvaNb3PlSMz+Q==", "type": "package", "path": "Microsoft.Extensions.FileProviders.Physical/1.0.0", "files": ["Microsoft.Extensions.FileProviders.Physical.1.0.0.nupkg.sha512", "Microsoft.Extensions.FileProviders.Physical.nuspec", "lib/net451/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net451/Microsoft.Extensions.FileProviders.Physical.xml", "lib/netstandard1.3/Microsoft.Extensions.FileProviders.Physical.dll", "lib/netstandard1.3/Microsoft.Extensions.FileProviders.Physical.xml"]}, "Microsoft.Extensions.FileSystemGlobbing/1.0.0": {"sha512": "scXp1Y+hmhQKLe57Z7cSjsAEFtE4zSHHydkg1SpvG56nWwWQVpVcRAbRZsv1qIBR5/vNB4LA9xiOKnvKO/Halg==", "type": "package", "path": "Microsoft.Extensions.FileSystemGlobbing/1.0.0", "files": ["Microsoft.Extensions.FileSystemGlobbing.1.0.0.nupkg.sha512", "Microsoft.Extensions.FileSystemGlobbing.nuspec", "lib/net451/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net451/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/netstandard1.3/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/netstandard1.3/Microsoft.Extensions.FileSystemGlobbing.xml"]}, "Microsoft.Extensions.Globalization.CultureInfoCache/1.0.0": {"sha512": "nxGoN8o+4clQk103krYRqS5FVVCZc3Tlc09AYj4W8gZ9Q5Jxa2BLW7ss+ogKU/hvNSg2NkJyQTfi9SegGU6ssQ==", "type": "package", "path": "Microsoft.Extensions.Globalization.CultureInfoCache/1.0.0", "files": ["Microsoft.Extensions.Globalization.CultureInfoCache.1.0.0.nupkg.sha512", "Microsoft.Extensions.Globalization.CultureInfoCache.nuspec", "lib/netstandard1.1/Microsoft.Extensions.Globalization.CultureInfoCache.dll", "lib/netstandard1.1/Microsoft.Extensions.Globalization.CultureInfoCache.xml"]}, "Microsoft.Extensions.Localization/1.0.0": {"sha512": "nkDgz++GXjMSEIiVS6CpeirV8m8zvc/vUN2sq5sPnqG8PZltCMSNmqrwyL1onx6A6aRNdTr1nVfvYHwWAmS4vg==", "type": "package", "path": "Microsoft.Extensions.Localization/1.0.0", "files": ["Microsoft.Extensions.Localization.1.0.0.nupkg.sha512", "Microsoft.Extensions.Localization.nuspec", "lib/net451/Microsoft.Extensions.Localization.dll", "lib/net451/Microsoft.Extensions.Localization.xml", "lib/netstandard1.3/Microsoft.Extensions.Localization.dll", "lib/netstandard1.3/Microsoft.Extensions.Localization.xml"]}, "Microsoft.Extensions.Localization.Abstractions/1.0.0": {"sha512": "hQ2sEJf7swsD5jk4DogLI3DazGvsvbz0IuSbxPFDjcvP0PRdxgCsyGpg70LD+3tRmxZcE1uh5jtcAi4X2BcB9w==", "type": "package", "path": "Microsoft.Extensions.Localization.Abstractions/1.0.0", "files": ["Microsoft.Extensions.Localization.Abstractions.1.0.0.nupkg.sha512", "Microsoft.Extensions.Localization.Abstractions.nuspec", "lib/netstandard1.0/Microsoft.Extensions.Localization.Abstractions.dll", "lib/netstandard1.0/Microsoft.Extensions.Localization.Abstractions.xml"]}, "Microsoft.Extensions.Logging/1.0.0": {"sha512": "0mDuASVrd/nMeBYIJSK+9lT3TSmWxUXP/ipVB1pF1ApMN5fqGCckPTNwmOfT4Z9wPkXGnhbwFTGrxZvbzTWxOg==", "type": "package", "path": "Microsoft.Extensions.Logging/1.0.0", "files": ["Microsoft.Extensions.Logging.1.0.0.nupkg.sha512", "Microsoft.Extensions.Logging.nuspec", "lib/netstandard1.1/Microsoft.Extensions.Logging.dll", "lib/netstandard1.1/Microsoft.Extensions.Logging.xml"]}, "Microsoft.Extensions.Logging.Abstractions/1.0.0": {"sha512": "wHT6oY50q36mAXBRKtFaB7u07WxKC5u2M8fi3PqHOOnHyUo9gD0u1TlCNR8UObHQxKMYwqlgI8TLcErpt29n8A==", "type": "package", "path": "Microsoft.Extensions.Logging.Abstractions/1.0.0", "files": ["Microsoft.Extensions.Logging.Abstractions.1.0.0.nupkg.sha512", "Microsoft.Extensions.Logging.Abstractions.nuspec", "lib/netstandard1.1/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard1.1/Microsoft.Extensions.Logging.Abstractions.xml"]}, "Microsoft.Extensions.Logging.Console/1.0.0": {"sha512": "GN4gFFONP12KbFEG9rNFpXuz6D2Tybcm8+c1wilaQ1eSl9zVX0gVRrKw/YRwxdwbM3eK7nWfRRqJaQPzOjtLnA==", "type": "package", "path": "Microsoft.Extensions.Logging.Console/1.0.0", "files": ["Microsoft.Extensions.Logging.Console.1.0.0.nupkg.sha512", "Microsoft.Extensions.Logging.Console.nuspec", "lib/net451/Microsoft.Extensions.Logging.Console.dll", "lib/net451/Microsoft.Extensions.Logging.Console.xml", "lib/netstandard1.3/Microsoft.Extensions.Logging.Console.dll", "lib/netstandard1.3/Microsoft.Extensions.Logging.Console.xml"]}, "Microsoft.Extensions.Logging.Debug/1.0.0": {"sha512": "V8fP8pGJxieGa1DAYOF1RX+cCGGqdOGNoAQUzxmy27+qNzbHB/cUXc7mCZT72jPZMB4U12zSVWlIt26GMsUIOg==", "type": "package", "path": "Microsoft.Extensions.Logging.Debug/1.0.0", "files": ["Microsoft.Extensions.Logging.Debug.1.0.0.nupkg.sha512", "Microsoft.Extensions.Logging.Debug.nuspec", "lib/net451/Microsoft.Extensions.Logging.Debug.dll", "lib/net451/Microsoft.Extensions.Logging.Debug.xml", "lib/netstandard1.3/Microsoft.Extensions.Logging.Debug.dll", "lib/netstandard1.3/Microsoft.Extensions.Logging.Debug.xml"]}, "Microsoft.Extensions.ObjectPool/1.0.0": {"sha512": "BTXoWSTrv/saLlNSg8l41YOoSKeUUanQLykUqRTtiUJz2xxQOCgm4ckPzrdmSK6w0mdjR2h7IrUDGdBF78Z7yg==", "type": "package", "path": "Microsoft.Extensions.ObjectPool/1.0.0", "files": ["Microsoft.Extensions.ObjectPool.1.0.0.nupkg.sha512", "Microsoft.Extensions.ObjectPool.nuspec", "lib/net451/Microsoft.Extensions.ObjectPool.dll", "lib/net451/Microsoft.Extensions.ObjectPool.xml", "lib/netstandard1.3/Microsoft.Extensions.ObjectPool.dll", "lib/netstandard1.3/Microsoft.Extensions.ObjectPool.xml"]}, "Microsoft.Extensions.Options/1.0.0": {"sha512": "SdP3yPKF++JTkoa91pBDiE70uQkR/gdXWzOnMPbSj+eOqY1vgY+b8RVl+gh7TrJ2wlCK2QqnQtvCQlPPZRK36w==", "type": "package", "path": "Microsoft.Extensions.Options/1.0.0", "files": ["Microsoft.Extensions.Options.1.0.0.nupkg.sha512", "Microsoft.Extensions.Options.nuspec", "lib/netstandard1.0/Microsoft.Extensions.Options.dll", "lib/netstandard1.0/Microsoft.Extensions.Options.xml"]}, "Microsoft.Extensions.Options.ConfigurationExtensions/1.0.0": {"sha512": "ov+cv1w81/lVrjqorjSpUvTzJhRVFu2GSRyRQYcGfGWPwrCz6473YoiqK/XNbeBmmF9VVyGPHVMubAH+Atsr3g==", "type": "package", "path": "Microsoft.Extensions.Options.ConfigurationExtensions/1.0.0", "files": ["Microsoft.Extensions.Options.ConfigurationExtensions.1.0.0.nupkg.sha512", "Microsoft.Extensions.Options.ConfigurationExtensions.nuspec", "lib/netstandard1.1/Microsoft.Extensions.Options.ConfigurationExtensions.dll", "lib/netstandard1.1/Microsoft.Extensions.Options.ConfigurationExtensions.xml"]}, "Microsoft.Extensions.PlatformAbstractions/1.0.0": {"sha512": "zyjUzrOmuevOAJpIo3Mt5GmpALVYCVdLZ99keMbmCxxgQH7oxzU58kGHzE6hAgYEiWsdfMJLjVR7r+vSmaJmtg==", "type": "package", "path": "Microsoft.Extensions.PlatformAbstractions/1.0.0", "files": ["Microsoft.Extensions.PlatformAbstractions.1.0.0.nupkg.sha512", "Microsoft.Extensions.PlatformAbstractions.nuspec", "lib/net451/Microsoft.Extensions.PlatformAbstractions.dll", "lib/net451/Microsoft.Extensions.PlatformAbstractions.xml", "lib/netstandard1.3/Microsoft.Extensions.PlatformAbstractions.dll", "lib/netstandard1.3/Microsoft.Extensions.PlatformAbstractions.xml"]}, "Microsoft.Extensions.Primitives/1.0.0": {"sha512": "3q2vzfKEDjL6JFkRpk5SFA3zarYsO6+ZYgoucNImrUMzDn0mFbEOL5p9oPoWiypwypbJVVjWTf557bXZ0YFLig==", "type": "package", "path": "Microsoft.Extensions.Primitives/1.0.0", "files": ["Microsoft.Extensions.Primitives.1.0.0.nupkg.sha512", "Microsoft.Extensions.Primitives.nuspec", "lib/netstandard1.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard1.0/Microsoft.Extensions.Primitives.xml"]}, "Microsoft.Extensions.WebEncoders/1.0.0": {"sha512": "NSSIBREmHHiyoAFXV2LMA+a6RMZtTHxgUbHJGHRtnjmTKnRyticx5HAzNpy8VG9+HCCHenL9QD7zSA8jjgAi5A==", "type": "package", "path": "Microsoft.Extensions.WebEncoders/1.0.0", "files": ["Microsoft.Extensions.WebEncoders.1.0.0.nupkg.sha512", "Microsoft.Extensions.WebEncoders.nuspec", "lib/netstandard1.0/Microsoft.Extensions.WebEncoders.dll", "lib/netstandard1.0/Microsoft.Extensions.WebEncoders.xml"]}, "Microsoft.Net.Http.Headers/1.0.0": {"sha512": "1lr92itF1fKR2oEQ6gk1IUsuCgp7UMlf/b1sjlAyuDeUnttj39ra59GQHYpomglJX1UVNpi1/cSBbEsXoNeIhw==", "type": "package", "path": "Microsoft.Net.Http.Headers/1.0.0", "files": ["Microsoft.Net.Http.Headers.1.0.0.nupkg.sha512", "Microsoft.Net.Http.Headers.nuspec", "lib/netstandard1.1/Microsoft.Net.Http.Headers.dll", "lib/netstandard1.1/Microsoft.Net.Http.Headers.xml"]}, "Microsoft.NETCore.App/1.0.0": {"sha512": "Bv40dLDrT+Igcg1e6otW3D8voeJCfcAxOlsxSVlDz+J+cdWls5kblZvPHHvx7gX3/oJoQVIkEeO3sMyv5PSVJA==", "type": "package", "path": "Microsoft.NETCore.App/1.0.0", "files": ["Microsoft.NETCore.App.1.0.0.nupkg.sha512", "Microsoft.NETCore.App.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netcoreapp1.0/_._"]}, "Microsoft.NETCore.DotNetHost/1.0.1": {"sha512": "uaMgykq6AckP3hZW4dsD6zjocxyXPz0tcTl8OX7mlSUWsyFXdtf45sjdwI0JIHxt3gnI6GihAlOAwYK8HE4niQ==", "type": "package", "path": "Microsoft.NETCore.DotNetHost/1.0.1", "files": ["Microsoft.NETCore.DotNetHost.1.0.1.nupkg.sha512", "Microsoft.NETCore.DotNetHost.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.json"]}, "Microsoft.NETCore.DotNetHostPolicy/1.0.1": {"sha512": "d8AQ+ZVj2iK9sbgl3IEsshCSaumhM1PNTPHxldZAQLOoI1BKF8QZ1zPCNqwBGisPiWOE3f/1SHDbQi1BTRBxuA==", "type": "package", "path": "Microsoft.NETCore.DotNetHostPolicy/1.0.1", "files": ["Microsoft.NETCore.DotNetHostPolicy.1.0.1.nupkg.sha512", "Microsoft.NETCore.DotNetHostPolicy.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.json"]}, "Microsoft.NETCore.DotNetHostResolver/1.0.1": {"sha512": "GEXgpAHB9E0OhfcmNJ664Xcd2bJkz2qkGIAFmCgEI5ANlQy4qEEmBVfUqA+Z9HB85ZwWxZc1eIJ6fxdxcjrctg==", "type": "package", "path": "Microsoft.NETCore.DotNetHostResolver/1.0.1", "files": ["Microsoft.NETCore.DotNetHostResolver.1.0.1.nupkg.sha512", "Microsoft.NETCore.DotNetHostResolver.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.json"]}, "Microsoft.NETCore.Jit/1.0.2": {"sha512": "Ok2vWofa6X8WD9vc4pfLHwvJz1/B6t3gOAoZcjrjrQf7lQOlNIuZIZtLn3wnWX28DuQGpPJkRlBxFj7Z5txNqw==", "type": "package", "path": "Microsoft.NETCore.Jit/1.0.2", "files": ["Microsoft.NETCore.Jit.1.0.2.nupkg.sha512", "Microsoft.NETCore.Jit.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.json"]}, "Microsoft.NETCore.Platforms/1.0.1": {"sha512": "2G6OjjJzwBfNOO8myRV/nFrbTw5iA+DEm0N+qUqhrOmaVtn4pC77h38I1jsXGw5VH55+dPfQsqHD0We9sCl9FQ==", "type": "package", "path": "Microsoft.NETCore.Platforms/1.0.1", "files": ["Microsoft.NETCore.Platforms.1.0.1.nupkg.sha512", "Microsoft.NETCore.Platforms.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "runtime.json"]}, "Microsoft.NETCore.Runtime.CoreCLR/1.0.2": {"sha512": "A0x1xtTjYJWZr2DRzgfCOXgB0JkQg8twnmtTJ79wFje+IihlLbXtx6Z2AxyVokBM5ruwTedR6YdCmHk39QJdtQ==", "type": "package", "path": "Microsoft.NETCore.Runtime.CoreCLR/1.0.2", "files": ["Microsoft.NETCore.Runtime.CoreCLR.1.0.2.nupkg.sha512", "Microsoft.NETCore.Runtime.CoreCLR.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.json"]}, "Microsoft.NETCore.Targets/1.0.1": {"sha512": "rkn+fKobF/cbWfnnfBOQHKVKIOpxMZBvlSHkqDWgBpwGDcLRduvs3D9OLGeV6GWGvVwNlVi2CBbTjuPmtHvyNw==", "type": "package", "path": "Microsoft.NETCore.Targets/1.0.1", "files": ["Microsoft.NETCore.Targets.1.0.1.nupkg.sha512", "Microsoft.NETCore.Targets.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "runtime.json"]}, "Microsoft.NETCore.Windows.ApiSets/1.0.1": {"sha512": "SaToCvvsGMxTgtLv/BrFQ5IFMPRE1zpWbnqbpwykJa8W5XiX82CXI6K2o7yf5xS7EP6t/JzFLV0SIDuWpvBZVw==", "type": "package", "path": "Microsoft.NETCore.Windows.ApiSets/1.0.1", "files": ["Microsoft.NETCore.Windows.ApiSets.1.0.1.nupkg.sha512", "Microsoft.NETCore.Windows.ApiSets.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "runtime.json"]}, "Microsoft.VisualBasic/10.0.1": {"sha512": "HpNyOf/4Tp2lh4FyywB55VITk0SqVxEjDzsVDDyF1yafDN6Bq18xcHowzCPINyYHUTgGcEtmpYiRsFdSo0KKdQ==", "type": "package", "path": "Microsoft.VisualBasic/10.0.1", "files": ["Microsoft.VisualBasic.10.0.1.nupkg.sha512", "Microsoft.VisualBasic.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/net45/_._", "lib/netcore50/Microsoft.VisualBasic.dll", "lib/netstandard1.3/Microsoft.VisualBasic.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "ref/net45/_._", "ref/netcore50/Microsoft.VisualBasic.dll", "ref/netcore50/Microsoft.VisualBasic.xml", "ref/netcore50/de/Microsoft.VisualBasic.xml", "ref/netcore50/es/Microsoft.VisualBasic.xml", "ref/netcore50/fr/Microsoft.VisualBasic.xml", "ref/netcore50/it/Microsoft.VisualBasic.xml", "ref/netcore50/ja/Microsoft.VisualBasic.xml", "ref/netcore50/ko/Microsoft.VisualBasic.xml", "ref/netcore50/ru/Microsoft.VisualBasic.xml", "ref/netcore50/zh-hans/Microsoft.VisualBasic.xml", "ref/netcore50/zh-hant/Microsoft.VisualBasic.xml", "ref/netstandard1.1/Microsoft.VisualBasic.dll", "ref/netstandard1.1/Microsoft.VisualBasic.xml", "ref/netstandard1.1/de/Microsoft.VisualBasic.xml", "ref/netstandard1.1/es/Microsoft.VisualBasic.xml", "ref/netstandard1.1/fr/Microsoft.VisualBasic.xml", "ref/netstandard1.1/it/Microsoft.VisualBasic.xml", "ref/netstandard1.1/ja/Microsoft.VisualBasic.xml", "ref/netstandard1.1/ko/Microsoft.VisualBasic.xml", "ref/netstandard1.1/ru/Microsoft.VisualBasic.xml", "ref/netstandard1.1/zh-hans/Microsoft.VisualBasic.xml", "ref/netstandard1.1/zh-hant/Microsoft.VisualBasic.xml", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._"]}, "Microsoft.Win32.Primitives/4.0.1": {"sha512": "fQnBHO9DgcmkC9dYSJoBqo6sH1VJwJprUHh8F3hbcRlxiQiBUuTntdk8tUwV490OqC2kQUrinGwZyQHTieuXRA==", "type": "package", "path": "Microsoft.Win32.Primitives/4.0.1", "files": ["Microsoft.Win32.Primitives.4.0.1.nupkg.sha512", "Microsoft.Win32.Primitives.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/Microsoft.Win32.Primitives.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/Microsoft.Win32.Primitives.dll", "ref/netstandard1.3/Microsoft.Win32.Primitives.dll", "ref/netstandard1.3/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/de/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/es/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/fr/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/it/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/ja/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/ko/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/ru/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/zh-hans/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/zh-hant/Microsoft.Win32.Primitives.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "Microsoft.Win32.Registry/4.0.0": {"sha512": "q+eLtROUAQ3OxYA5mpQrgyFgzLQxIyrfT2eLpYX5IEPlHmIio2nh4F5bgOaQoGOV865kFKZZso9Oq9RlazvXtg==", "type": "package", "path": "Microsoft.Win32.Registry/4.0.0", "files": ["Microsoft.Win32.Registry.4.0.0.nupkg.sha512", "Microsoft.Win32.Registry.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/net46/Microsoft.Win32.Registry.dll", "ref/net46/Microsoft.Win32.Registry.dll", "ref/netstandard1.3/Microsoft.Win32.Registry.dll", "ref/netstandard1.3/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/de/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/es/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/fr/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/it/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ja/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ko/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ru/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hans/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hant/Microsoft.Win32.Registry.xml", "runtimes/unix/lib/netstandard1.3/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net46/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netcore50/_._", "runtimes/win/lib/netstandard1.3/Microsoft.Win32.Registry.dll"]}, "NETStandard.Library/1.6.0": {"sha512": "ypsCvIdCZ4IoYASJHt6tF2fMo7N30NLgV1EbmC+snO490OMl9FvVxmumw14rhReWU3j3g7BYudG6YCrchwHJlA==", "type": "package", "path": "NETStandard.Library/1.6.0", "files": ["NETStandard.Library.1.6.0.nupkg.sha512", "NETStandard.Library.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt"]}, "Newtonsoft.Json/9.0.1": {"sha512": "U82mHQSKaIk+lpSVCbWYKNavmNH1i5xrExDEquU1i6I5pV6UMOqRnJRSlKO3cMPfcpp0RgDY+8jUXHdQ4IfXvw==", "type": "package", "path": "Newtonsoft.Json/9.0.1", "files": ["Newtonsoft.Json.9.0.1.nupkg.sha512", "Newtonsoft.Json.nuspec", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/portable-net40+sl5+wp80+win8+wpa81/Newtonsoft.Json.dll", "lib/portable-net40+sl5+wp80+win8+wpa81/Newtonsoft.Json.xml", "lib/portable-net45+wp80+win8+wpa81/Newtonsoft.Json.dll", "lib/portable-net45+wp80+win8+wpa81/Newtonsoft.Json.xml", "tools/install.ps1"]}, "runtime.native.System/4.0.0": {"sha512": "QfS/nQI7k/BLgmLrw7qm7YBoULEvgWnPI+cYsbfCVFTW8Aj+i8JhccxcFMu1RWms0YZzF+UHguNBK4Qn89e2Sg==", "type": "package", "path": "runtime.native.System/4.0.0", "files": ["ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "runtime.native.System.4.0.0.nupkg.sha512", "runtime.native.System.nuspec"]}, "runtime.native.System.IO.Compression/4.1.0": {"sha512": "Ob7nvnJBox1aaB222zSVZSkf4WrebPG4qFscfK7vmD7P7NxoSxACQLtO7ytWpqXDn2wcd/+45+EAZ7xjaPip8A==", "type": "package", "path": "runtime.native.System.IO.Compression/4.1.0", "files": ["ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "runtime.native.System.IO.Compression.4.1.0.nupkg.sha512", "runtime.native.System.IO.Compression.nuspec"]}, "runtime.native.System.Net.Http/4.0.1": {"sha512": "Nh0UPZx2Vifh8r+J+H2jxifZUD3sBrmolgiFWJd2yiNrxO0xTa6bAw3YwRn1VOiSen/tUXMS31ttNItCZ6lKuA==", "type": "package", "path": "runtime.native.System.Net.Http/4.0.1", "files": ["ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "runtime.native.System.Net.Http.4.0.1.nupkg.sha512", "runtime.native.System.Net.Http.nuspec"]}, "runtime.native.System.Net.Security/4.0.1": {"sha512": "Az6Ff6rZFb8nYGAaejFR6jr8ktt9f3e1Q/yKdw0pwHNTLaO/1eCAC9vzBoR9YAb0QeZD6fZXl1A9tRB5stpzXA==", "type": "package", "path": "runtime.native.System.Net.Security/4.0.1", "files": ["ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "runtime.native.System.Net.Security.4.0.1.nupkg.sha512", "runtime.native.System.Net.Security.nuspec"]}, "runtime.native.System.Security.Cryptography/4.0.0": {"sha512": "2CQK0jmO6Eu7ZeMgD+LOFbNJSXHFVQbCJJkEyEwowh1SCgYnrn9W9RykMfpeeVGw7h4IBvYikzpGUlmZTUafJw==", "type": "package", "path": "runtime.native.System.Security.Cryptography/4.0.0", "files": ["ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "runtime.native.System.Security.Cryptography.4.0.0.nupkg.sha512", "runtime.native.System.Security.Cryptography.nuspec"]}, "System.AppContext/4.1.0": {"sha512": "3QjO4jNV7PdKkmQAVp9atA+usVnKRwI3Kx1nMwJ93T0LcQfx7pKAYk0nKz5wn1oP5iqlhZuy6RXOFdhr7rDwow==", "type": "package", "path": "System.AppContext/4.1.0", "files": ["System.AppContext.4.1.0.nupkg.sha512", "System.AppContext.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.AppContext.dll", "lib/net463/System.AppContext.dll", "lib/netcore50/System.AppContext.dll", "lib/netstandard1.6/System.AppContext.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.AppContext.dll", "ref/net463/System.AppContext.dll", "ref/netstandard/_._", "ref/netstandard1.3/System.AppContext.dll", "ref/netstandard1.3/System.AppContext.xml", "ref/netstandard1.3/de/System.AppContext.xml", "ref/netstandard1.3/es/System.AppContext.xml", "ref/netstandard1.3/fr/System.AppContext.xml", "ref/netstandard1.3/it/System.AppContext.xml", "ref/netstandard1.3/ja/System.AppContext.xml", "ref/netstandard1.3/ko/System.AppContext.xml", "ref/netstandard1.3/ru/System.AppContext.xml", "ref/netstandard1.3/zh-hans/System.AppContext.xml", "ref/netstandard1.3/zh-hant/System.AppContext.xml", "ref/netstandard1.6/System.AppContext.dll", "ref/netstandard1.6/System.AppContext.xml", "ref/netstandard1.6/de/System.AppContext.xml", "ref/netstandard1.6/es/System.AppContext.xml", "ref/netstandard1.6/fr/System.AppContext.xml", "ref/netstandard1.6/it/System.AppContext.xml", "ref/netstandard1.6/ja/System.AppContext.xml", "ref/netstandard1.6/ko/System.AppContext.xml", "ref/netstandard1.6/ru/System.AppContext.xml", "ref/netstandard1.6/zh-hans/System.AppContext.xml", "ref/netstandard1.6/zh-hant/System.AppContext.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.AppContext.dll"]}, "System.Buffers/4.0.0": {"sha512": "msXumHfjjURSkvxUjYuq4N2ghHoRi2VpXcKMA7gK6ujQfU3vGpl+B6ld0ATRg+FZFpRyA6PgEPA+VlIkTeNf2w==", "type": "package", "path": "System.Buffers/4.0.0", "files": ["System.Buffers.4.0.0.nupkg.sha512", "System.Buffers.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.1/.xml", "lib/netstandard1.1/System.Buffers.dll"]}, "System.Collections/4.0.11": {"sha512": "YUJGz6eFKqS0V//mLt25vFGrrCvOnsXjlvFQs+KimpwNxug9x0Pzy4PlFMU3Q2IzqAa9G2L4LsK3+9vCBK7oTg==", "type": "package", "path": "System.Collections/4.0.11", "files": ["System.Collections.4.0.11.nupkg.sha512", "System.Collections.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Collections.dll", "ref/netcore50/System.Collections.xml", "ref/netcore50/de/System.Collections.xml", "ref/netcore50/es/System.Collections.xml", "ref/netcore50/fr/System.Collections.xml", "ref/netcore50/it/System.Collections.xml", "ref/netcore50/ja/System.Collections.xml", "ref/netcore50/ko/System.Collections.xml", "ref/netcore50/ru/System.Collections.xml", "ref/netcore50/zh-hans/System.Collections.xml", "ref/netcore50/zh-hant/System.Collections.xml", "ref/netstandard1.0/System.Collections.dll", "ref/netstandard1.0/System.Collections.xml", "ref/netstandard1.0/de/System.Collections.xml", "ref/netstandard1.0/es/System.Collections.xml", "ref/netstandard1.0/fr/System.Collections.xml", "ref/netstandard1.0/it/System.Collections.xml", "ref/netstandard1.0/ja/System.Collections.xml", "ref/netstandard1.0/ko/System.Collections.xml", "ref/netstandard1.0/ru/System.Collections.xml", "ref/netstandard1.0/zh-hans/System.Collections.xml", "ref/netstandard1.0/zh-hant/System.Collections.xml", "ref/netstandard1.3/System.Collections.dll", "ref/netstandard1.3/System.Collections.xml", "ref/netstandard1.3/de/System.Collections.xml", "ref/netstandard1.3/es/System.Collections.xml", "ref/netstandard1.3/fr/System.Collections.xml", "ref/netstandard1.3/it/System.Collections.xml", "ref/netstandard1.3/ja/System.Collections.xml", "ref/netstandard1.3/ko/System.Collections.xml", "ref/netstandard1.3/ru/System.Collections.xml", "ref/netstandard1.3/zh-hans/System.Collections.xml", "ref/netstandard1.3/zh-hant/System.Collections.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.Collections.Concurrent/4.0.12": {"sha512": "2gBcbb3drMLgxlI0fBfxMA31ec6AEyYCHygGse4vxceJan8mRIWeKJ24BFzN7+bi/NFTgdIgufzb94LWO5EERQ==", "type": "package", "path": "System.Collections.Concurrent/4.0.12", "files": ["System.Collections.Concurrent.4.0.12.nupkg.sha512", "System.Collections.Concurrent.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Collections.Concurrent.dll", "lib/netstandard1.3/System.Collections.Concurrent.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Collections.Concurrent.dll", "ref/netcore50/System.Collections.Concurrent.xml", "ref/netcore50/de/System.Collections.Concurrent.xml", "ref/netcore50/es/System.Collections.Concurrent.xml", "ref/netcore50/fr/System.Collections.Concurrent.xml", "ref/netcore50/it/System.Collections.Concurrent.xml", "ref/netcore50/ja/System.Collections.Concurrent.xml", "ref/netcore50/ko/System.Collections.Concurrent.xml", "ref/netcore50/ru/System.Collections.Concurrent.xml", "ref/netcore50/zh-hans/System.Collections.Concurrent.xml", "ref/netcore50/zh-hant/System.Collections.Concurrent.xml", "ref/netstandard1.1/System.Collections.Concurrent.dll", "ref/netstandard1.1/System.Collections.Concurrent.xml", "ref/netstandard1.1/de/System.Collections.Concurrent.xml", "ref/netstandard1.1/es/System.Collections.Concurrent.xml", "ref/netstandard1.1/fr/System.Collections.Concurrent.xml", "ref/netstandard1.1/it/System.Collections.Concurrent.xml", "ref/netstandard1.1/ja/System.Collections.Concurrent.xml", "ref/netstandard1.1/ko/System.Collections.Concurrent.xml", "ref/netstandard1.1/ru/System.Collections.Concurrent.xml", "ref/netstandard1.1/zh-hans/System.Collections.Concurrent.xml", "ref/netstandard1.1/zh-hant/System.Collections.Concurrent.xml", "ref/netstandard1.3/System.Collections.Concurrent.dll", "ref/netstandard1.3/System.Collections.Concurrent.xml", "ref/netstandard1.3/de/System.Collections.Concurrent.xml", "ref/netstandard1.3/es/System.Collections.Concurrent.xml", "ref/netstandard1.3/fr/System.Collections.Concurrent.xml", "ref/netstandard1.3/it/System.Collections.Concurrent.xml", "ref/netstandard1.3/ja/System.Collections.Concurrent.xml", "ref/netstandard1.3/ko/System.Collections.Concurrent.xml", "ref/netstandard1.3/ru/System.Collections.Concurrent.xml", "ref/netstandard1.3/zh-hans/System.Collections.Concurrent.xml", "ref/netstandard1.3/zh-hant/System.Collections.Concurrent.xml", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.Collections.Immutable/1.2.0": {"sha512": "Cma8cBW6di16ZLibL8LYQ+cLjGzoKxpOTu/faZfDcx94ZjAGq6Nv5RO7+T1YZXqEXTZP9rt1wLVEONVpURtUqw==", "type": "package", "path": "System.Collections.Immutable/1.2.0", "files": ["System.Collections.Immutable.1.2.0.nupkg.sha512", "System.Collections.Immutable.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/System.Collections.Immutable.dll", "lib/netstandard1.0/System.Collections.Immutable.xml", "lib/portable-net45+win8+wp8+wpa81/System.Collections.Immutable.dll", "lib/portable-net45+win8+wp8+wpa81/System.Collections.Immutable.xml"]}, "System.Collections.NonGeneric/4.0.1": {"sha512": "hMxFT2RhhlffyCdKLDXjx8WEC5JfCvNozAZxCablAuFRH74SCV4AgzE8yJCh/73bFnEoZgJ9MJmkjQ0dJmnKqA==", "type": "package", "path": "System.Collections.NonGeneric/4.0.1", "files": ["System.Collections.NonGeneric.4.0.1.nupkg.sha512", "System.Collections.NonGeneric.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Collections.NonGeneric.dll", "lib/netstandard1.3/System.Collections.NonGeneric.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Collections.NonGeneric.dll", "ref/netstandard1.3/System.Collections.NonGeneric.dll", "ref/netstandard1.3/System.Collections.NonGeneric.xml", "ref/netstandard1.3/de/System.Collections.NonGeneric.xml", "ref/netstandard1.3/es/System.Collections.NonGeneric.xml", "ref/netstandard1.3/fr/System.Collections.NonGeneric.xml", "ref/netstandard1.3/it/System.Collections.NonGeneric.xml", "ref/netstandard1.3/ja/System.Collections.NonGeneric.xml", "ref/netstandard1.3/ko/System.Collections.NonGeneric.xml", "ref/netstandard1.3/ru/System.Collections.NonGeneric.xml", "ref/netstandard1.3/zh-hans/System.Collections.NonGeneric.xml", "ref/netstandard1.3/zh-hant/System.Collections.NonGeneric.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.Collections.Specialized/4.0.1": {"sha512": "/HKQyVP0yH1I0YtK7KJL/28snxHNH/bi+0lgk/+MbURF6ULhAE31MDI+NZDerNWu264YbxklXCCygISgm+HMug==", "type": "package", "path": "System.Collections.Specialized/4.0.1", "files": ["System.Collections.Specialized.4.0.1.nupkg.sha512", "System.Collections.Specialized.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Collections.Specialized.dll", "lib/netstandard1.3/System.Collections.Specialized.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Collections.Specialized.dll", "ref/netstandard1.3/System.Collections.Specialized.dll", "ref/netstandard1.3/System.Collections.Specialized.xml", "ref/netstandard1.3/de/System.Collections.Specialized.xml", "ref/netstandard1.3/es/System.Collections.Specialized.xml", "ref/netstandard1.3/fr/System.Collections.Specialized.xml", "ref/netstandard1.3/it/System.Collections.Specialized.xml", "ref/netstandard1.3/ja/System.Collections.Specialized.xml", "ref/netstandard1.3/ko/System.Collections.Specialized.xml", "ref/netstandard1.3/ru/System.Collections.Specialized.xml", "ref/netstandard1.3/zh-hans/System.Collections.Specialized.xml", "ref/netstandard1.3/zh-hant/System.Collections.Specialized.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.ComponentModel/4.0.1": {"sha512": "oBZFnm7seFiVfugsIyOvQCWobNZs7FzqDV/B7tx20Ep/l3UUFCPDkdTnCNaJZTU27zjeODmy2C/cP60u3D4c9w==", "type": "package", "path": "System.ComponentModel/4.0.1", "files": ["System.ComponentModel.4.0.1.nupkg.sha512", "System.ComponentModel.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.ComponentModel.dll", "lib/netstandard1.3/System.ComponentModel.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.ComponentModel.dll", "ref/netcore50/System.ComponentModel.xml", "ref/netcore50/de/System.ComponentModel.xml", "ref/netcore50/es/System.ComponentModel.xml", "ref/netcore50/fr/System.ComponentModel.xml", "ref/netcore50/it/System.ComponentModel.xml", "ref/netcore50/ja/System.ComponentModel.xml", "ref/netcore50/ko/System.ComponentModel.xml", "ref/netcore50/ru/System.ComponentModel.xml", "ref/netcore50/zh-hans/System.ComponentModel.xml", "ref/netcore50/zh-hant/System.ComponentModel.xml", "ref/netstandard1.0/System.ComponentModel.dll", "ref/netstandard1.0/System.ComponentModel.xml", "ref/netstandard1.0/de/System.ComponentModel.xml", "ref/netstandard1.0/es/System.ComponentModel.xml", "ref/netstandard1.0/fr/System.ComponentModel.xml", "ref/netstandard1.0/it/System.ComponentModel.xml", "ref/netstandard1.0/ja/System.ComponentModel.xml", "ref/netstandard1.0/ko/System.ComponentModel.xml", "ref/netstandard1.0/ru/System.ComponentModel.xml", "ref/netstandard1.0/zh-hans/System.ComponentModel.xml", "ref/netstandard1.0/zh-hant/System.ComponentModel.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.ComponentModel.Annotations/4.1.0": {"sha512": "rhnz80h8NnHJzoi0nbQJLRR2cJznyqG168q1bgoSpe5qpaME2SguXzuEzpY68nFCi2kBgHpbU4bRN2cP3unYRA==", "type": "package", "path": "System.ComponentModel.Annotations/4.1.0", "files": ["System.ComponentModel.Annotations.4.1.0.nupkg.sha512", "System.ComponentModel.Annotations.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net461/System.ComponentModel.Annotations.dll", "lib/netcore50/System.ComponentModel.Annotations.dll", "lib/netstandard1.4/System.ComponentModel.Annotations.dll", "lib/portable-net45+win8/_._", "lib/win8/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net461/System.ComponentModel.Annotations.dll", "ref/netcore50/System.ComponentModel.Annotations.dll", "ref/netcore50/System.ComponentModel.Annotations.xml", "ref/netcore50/de/System.ComponentModel.Annotations.xml", "ref/netcore50/es/System.ComponentModel.Annotations.xml", "ref/netcore50/fr/System.ComponentModel.Annotations.xml", "ref/netcore50/it/System.ComponentModel.Annotations.xml", "ref/netcore50/ja/System.ComponentModel.Annotations.xml", "ref/netcore50/ko/System.ComponentModel.Annotations.xml", "ref/netcore50/ru/System.ComponentModel.Annotations.xml", "ref/netcore50/zh-hans/System.ComponentModel.Annotations.xml", "ref/netcore50/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/System.ComponentModel.Annotations.dll", "ref/netstandard1.1/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/System.ComponentModel.Annotations.dll", "ref/netstandard1.3/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/System.ComponentModel.Annotations.dll", "ref/netstandard1.4/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/zh-hant/System.ComponentModel.Annotations.xml", "ref/portable-net45+win8/_._", "ref/win8/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.ComponentModel.Primitives/4.1.0": {"sha512": "sc/7eVCdxPrp3ljpgTKVaQGUXiW05phNWvtv/m2kocXqrUQvTVWKou1Edas2aDjTThLPZOxPYIGNb/HN0QjURg==", "type": "package", "path": "System.ComponentModel.Primitives/4.1.0", "files": ["System.ComponentModel.Primitives.4.1.0.nupkg.sha512", "System.ComponentModel.Primitives.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/System.ComponentModel.Primitives.dll", "lib/netstandard1.0/System.ComponentModel.Primitives.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/System.ComponentModel.Primitives.dll", "ref/netstandard1.0/System.ComponentModel.Primitives.dll", "ref/netstandard1.0/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/de/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/es/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/fr/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/it/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/ja/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/ko/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/ru/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/zh-hans/System.ComponentModel.Primitives.xml", "ref/netstandard1.0/zh-hant/System.ComponentModel.Primitives.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.ComponentModel.TypeConverter/4.1.0": {"sha512": "MnDAlaeJZy9pdB5ZdOlwdxfpI+LJQ6e0hmH7d2+y2LkiD8DRJynyDYl4Xxf3fWFm7SbEwBZh4elcfzONQLOoQw==", "type": "package", "path": "System.ComponentModel.TypeConverter/4.1.0", "files": ["System.ComponentModel.TypeConverter.4.1.0.nupkg.sha512", "System.ComponentModel.TypeConverter.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/System.ComponentModel.TypeConverter.dll", "lib/net462/System.ComponentModel.TypeConverter.dll", "lib/netstandard1.0/System.ComponentModel.TypeConverter.dll", "lib/netstandard1.5/System.ComponentModel.TypeConverter.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/System.ComponentModel.TypeConverter.dll", "ref/net462/System.ComponentModel.TypeConverter.dll", "ref/netstandard1.0/System.ComponentModel.TypeConverter.dll", "ref/netstandard1.0/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/de/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/es/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/fr/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/it/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/ja/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/ko/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/ru/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/zh-hans/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.0/zh-hant/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/System.ComponentModel.TypeConverter.dll", "ref/netstandard1.5/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/de/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/es/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/fr/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/it/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/ja/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/ko/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/ru/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/zh-hans/System.ComponentModel.TypeConverter.xml", "ref/netstandard1.5/zh-hant/System.ComponentModel.TypeConverter.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.Console/4.0.0": {"sha512": "qSKUSOIiYA/a0g5XXdxFcUFmv1hNICBD7QZ0QhGYVipPIhvpiydY8VZqr1thmCXvmn8aipMg64zuanB4eotK9A==", "type": "package", "path": "System.Console/4.0.0", "files": ["System.Console.4.0.0.nupkg.sha512", "System.Console.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Console.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Console.dll", "ref/netstandard1.3/System.Console.dll", "ref/netstandard1.3/System.Console.xml", "ref/netstandard1.3/de/System.Console.xml", "ref/netstandard1.3/es/System.Console.xml", "ref/netstandard1.3/fr/System.Console.xml", "ref/netstandard1.3/it/System.Console.xml", "ref/netstandard1.3/ja/System.Console.xml", "ref/netstandard1.3/ko/System.Console.xml", "ref/netstandard1.3/ru/System.Console.xml", "ref/netstandard1.3/zh-hans/System.Console.xml", "ref/netstandard1.3/zh-hant/System.Console.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.Diagnostics.Contracts/4.0.1": {"sha512": "HvQQjy712vnlpPxaloZYkuE78Gn353L0SJLJVeLcNASeg9c4qla2a1Xq8I7B3jZoDzKPtHTkyVO7AZ5tpeQGuA==", "type": "package", "path": "System.Diagnostics.Contracts/4.0.1", "files": ["System.Diagnostics.Contracts.4.0.1.nupkg.sha512", "System.Diagnostics.Contracts.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Diagnostics.Contracts.dll", "lib/netstandard1.0/System.Diagnostics.Contracts.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Diagnostics.Contracts.dll", "ref/netcore50/System.Diagnostics.Contracts.xml", "ref/netcore50/de/System.Diagnostics.Contracts.xml", "ref/netcore50/es/System.Diagnostics.Contracts.xml", "ref/netcore50/fr/System.Diagnostics.Contracts.xml", "ref/netcore50/it/System.Diagnostics.Contracts.xml", "ref/netcore50/ja/System.Diagnostics.Contracts.xml", "ref/netcore50/ko/System.Diagnostics.Contracts.xml", "ref/netcore50/ru/System.Diagnostics.Contracts.xml", "ref/netcore50/zh-hans/System.Diagnostics.Contracts.xml", "ref/netcore50/zh-hant/System.Diagnostics.Contracts.xml", "ref/netstandard1.0/System.Diagnostics.Contracts.dll", "ref/netstandard1.0/System.Diagnostics.Contracts.xml", "ref/netstandard1.0/de/System.Diagnostics.Contracts.xml", "ref/netstandard1.0/es/System.Diagnostics.Contracts.xml", "ref/netstandard1.0/fr/System.Diagnostics.Contracts.xml", "ref/netstandard1.0/it/System.Diagnostics.Contracts.xml", "ref/netstandard1.0/ja/System.Diagnostics.Contracts.xml", "ref/netstandard1.0/ko/System.Diagnostics.Contracts.xml", "ref/netstandard1.0/ru/System.Diagnostics.Contracts.xml", "ref/netstandard1.0/zh-hans/System.Diagnostics.Contracts.xml", "ref/netstandard1.0/zh-hant/System.Diagnostics.Contracts.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Diagnostics.Contracts.dll"]}, "System.Diagnostics.Debug/4.0.11": {"sha512": "w5U95fVKHY4G8ASs/K5iK3J5LY+/dLFd4vKejsnI/ZhBsWS9hQakfx3Zr7lRWKg4tAw9r4iktyvsTagWkqYCiw==", "type": "package", "path": "System.Diagnostics.Debug/4.0.11", "files": ["System.Diagnostics.Debug.4.0.11.nupkg.sha512", "System.Diagnostics.Debug.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Diagnostics.Debug.dll", "ref/netcore50/System.Diagnostics.Debug.xml", "ref/netcore50/de/System.Diagnostics.Debug.xml", "ref/netcore50/es/System.Diagnostics.Debug.xml", "ref/netcore50/fr/System.Diagnostics.Debug.xml", "ref/netcore50/it/System.Diagnostics.Debug.xml", "ref/netcore50/ja/System.Diagnostics.Debug.xml", "ref/netcore50/ko/System.Diagnostics.Debug.xml", "ref/netcore50/ru/System.Diagnostics.Debug.xml", "ref/netcore50/zh-hans/System.Diagnostics.Debug.xml", "ref/netcore50/zh-hant/System.Diagnostics.Debug.xml", "ref/netstandard1.0/System.Diagnostics.Debug.dll", "ref/netstandard1.0/System.Diagnostics.Debug.xml", "ref/netstandard1.0/de/System.Diagnostics.Debug.xml", "ref/netstandard1.0/es/System.Diagnostics.Debug.xml", "ref/netstandard1.0/fr/System.Diagnostics.Debug.xml", "ref/netstandard1.0/it/System.Diagnostics.Debug.xml", "ref/netstandard1.0/ja/System.Diagnostics.Debug.xml", "ref/netstandard1.0/ko/System.Diagnostics.Debug.xml", "ref/netstandard1.0/ru/System.Diagnostics.Debug.xml", "ref/netstandard1.0/zh-hans/System.Diagnostics.Debug.xml", "ref/netstandard1.0/zh-hant/System.Diagnostics.Debug.xml", "ref/netstandard1.3/System.Diagnostics.Debug.dll", "ref/netstandard1.3/System.Diagnostics.Debug.xml", "ref/netstandard1.3/de/System.Diagnostics.Debug.xml", "ref/netstandard1.3/es/System.Diagnostics.Debug.xml", "ref/netstandard1.3/fr/System.Diagnostics.Debug.xml", "ref/netstandard1.3/it/System.Diagnostics.Debug.xml", "ref/netstandard1.3/ja/System.Diagnostics.Debug.xml", "ref/netstandard1.3/ko/System.Diagnostics.Debug.xml", "ref/netstandard1.3/ru/System.Diagnostics.Debug.xml", "ref/netstandard1.3/zh-hans/System.Diagnostics.Debug.xml", "ref/netstandard1.3/zh-hant/System.Diagnostics.Debug.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.Diagnostics.DiagnosticSource/4.0.0": {"sha512": "YKglnq4BMTJxfcr6nuT08g+yJ0UxdePIHxosiLuljuHIUR6t4KhFsyaHOaOc1Ofqp0PUvJ0EmcgiEz6T7vEx3w==", "type": "package", "path": "System.Diagnostics.DiagnosticSource/4.0.0", "files": ["System.Diagnostics.DiagnosticSource.4.0.0.nupkg.sha512", "System.Diagnostics.DiagnosticSource.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/net46/System.Diagnostics.DiagnosticSource.dll", "lib/net46/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard1.1/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard1.1/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard1.3/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard1.3/System.Diagnostics.DiagnosticSource.xml", "lib/portable-net45+win8+wpa81/System.Diagnostics.DiagnosticSource.dll", "lib/portable-net45+win8+wpa81/System.Diagnostics.DiagnosticSource.xml"]}, "System.Diagnostics.FileVersionInfo/4.0.0": {"sha512": "qjF74OTAU+mRhLaL4YSfiWy3vj6T3AOz8AW37l5zCwfbBfj0k7E94XnEsRaf2TnhE/7QaV6Hvqakoy2LoV8MVg==", "type": "package", "path": "System.Diagnostics.FileVersionInfo/4.0.0", "files": ["System.Diagnostics.FileVersionInfo.4.0.0.nupkg.sha512", "System.Diagnostics.FileVersionInfo.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Diagnostics.FileVersionInfo.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Diagnostics.FileVersionInfo.dll", "ref/netstandard1.3/System.Diagnostics.FileVersionInfo.dll", "ref/netstandard1.3/System.Diagnostics.FileVersionInfo.xml", "ref/netstandard1.3/de/System.Diagnostics.FileVersionInfo.xml", "ref/netstandard1.3/es/System.Diagnostics.FileVersionInfo.xml", "ref/netstandard1.3/fr/System.Diagnostics.FileVersionInfo.xml", "ref/netstandard1.3/it/System.Diagnostics.FileVersionInfo.xml", "ref/netstandard1.3/ja/System.Diagnostics.FileVersionInfo.xml", "ref/netstandard1.3/ko/System.Diagnostics.FileVersionInfo.xml", "ref/netstandard1.3/ru/System.Diagnostics.FileVersionInfo.xml", "ref/netstandard1.3/zh-hans/System.Diagnostics.FileVersionInfo.xml", "ref/netstandard1.3/zh-hant/System.Diagnostics.FileVersionInfo.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.3/System.Diagnostics.FileVersionInfo.dll", "runtimes/win/lib/net46/System.Diagnostics.FileVersionInfo.dll", "runtimes/win/lib/netcore50/System.Diagnostics.FileVersionInfo.dll", "runtimes/win/lib/netstandard1.3/System.Diagnostics.FileVersionInfo.dll"]}, "System.Diagnostics.Process/4.1.0": {"sha512": "mpVZ5bnlSs3tTeJ6jYyDJEIa6tavhAd88lxq1zbYhkkCu0Pno2+gHXcvZcoygq2d8JxW3gojXqNJMTAshduqZA==", "type": "package", "path": "System.Diagnostics.Process/4.1.0", "files": ["System.Diagnostics.Process.4.1.0.nupkg.sha512", "System.Diagnostics.Process.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Diagnostics.Process.dll", "lib/net461/System.Diagnostics.Process.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Diagnostics.Process.dll", "ref/net461/System.Diagnostics.Process.dll", "ref/netstandard1.3/System.Diagnostics.Process.dll", "ref/netstandard1.3/System.Diagnostics.Process.xml", "ref/netstandard1.3/de/System.Diagnostics.Process.xml", "ref/netstandard1.3/es/System.Diagnostics.Process.xml", "ref/netstandard1.3/fr/System.Diagnostics.Process.xml", "ref/netstandard1.3/it/System.Diagnostics.Process.xml", "ref/netstandard1.3/ja/System.Diagnostics.Process.xml", "ref/netstandard1.3/ko/System.Diagnostics.Process.xml", "ref/netstandard1.3/ru/System.Diagnostics.Process.xml", "ref/netstandard1.3/zh-hans/System.Diagnostics.Process.xml", "ref/netstandard1.3/zh-hant/System.Diagnostics.Process.xml", "ref/netstandard1.4/System.Diagnostics.Process.dll", "ref/netstandard1.4/System.Diagnostics.Process.xml", "ref/netstandard1.4/de/System.Diagnostics.Process.xml", "ref/netstandard1.4/es/System.Diagnostics.Process.xml", "ref/netstandard1.4/fr/System.Diagnostics.Process.xml", "ref/netstandard1.4/it/System.Diagnostics.Process.xml", "ref/netstandard1.4/ja/System.Diagnostics.Process.xml", "ref/netstandard1.4/ko/System.Diagnostics.Process.xml", "ref/netstandard1.4/ru/System.Diagnostics.Process.xml", "ref/netstandard1.4/zh-hans/System.Diagnostics.Process.xml", "ref/netstandard1.4/zh-hant/System.Diagnostics.Process.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/linux/lib/netstandard1.4/System.Diagnostics.Process.dll", "runtimes/osx/lib/netstandard1.4/System.Diagnostics.Process.dll", "runtimes/win/lib/net46/System.Diagnostics.Process.dll", "runtimes/win/lib/net461/System.Diagnostics.Process.dll", "runtimes/win/lib/netstandard1.4/System.Diagnostics.Process.dll", "runtimes/win7/lib/netcore50/_._"]}, "System.Diagnostics.StackTrace/4.0.1": {"sha512": "6i2EbRq0lgGfiZ+FDf0gVaw9qeEU+7IS2+wbZJmFVpvVzVOgZEt0ScZtyenuBvs6iDYbGiF51bMAa0oDP/tujQ==", "type": "package", "path": "System.Diagnostics.StackTrace/4.0.1", "files": ["System.Diagnostics.StackTrace.4.0.1.nupkg.sha512", "System.Diagnostics.StackTrace.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Diagnostics.StackTrace.dll", "lib/netstandard1.3/System.Diagnostics.StackTrace.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Diagnostics.StackTrace.dll", "ref/netstandard1.3/System.Diagnostics.StackTrace.dll", "ref/netstandard1.3/System.Diagnostics.StackTrace.xml", "ref/netstandard1.3/de/System.Diagnostics.StackTrace.xml", "ref/netstandard1.3/es/System.Diagnostics.StackTrace.xml", "ref/netstandard1.3/fr/System.Diagnostics.StackTrace.xml", "ref/netstandard1.3/it/System.Diagnostics.StackTrace.xml", "ref/netstandard1.3/ja/System.Diagnostics.StackTrace.xml", "ref/netstandard1.3/ko/System.Diagnostics.StackTrace.xml", "ref/netstandard1.3/ru/System.Diagnostics.StackTrace.xml", "ref/netstandard1.3/zh-hans/System.Diagnostics.StackTrace.xml", "ref/netstandard1.3/zh-hant/System.Diagnostics.StackTrace.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Diagnostics.StackTrace.dll"]}, "System.Diagnostics.Tools/4.0.1": {"sha512": "xBfJ8pnd4C17dWaC9FM6aShzbJcRNMChUMD42I6772KGGrqaFdumwhn9OdM68erj1ueNo3xdQ1EwiFjK5k8p0g==", "type": "package", "path": "System.Diagnostics.Tools/4.0.1", "files": ["System.Diagnostics.Tools.4.0.1.nupkg.sha512", "System.Diagnostics.Tools.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Diagnostics.Tools.dll", "ref/netcore50/System.Diagnostics.Tools.xml", "ref/netcore50/de/System.Diagnostics.Tools.xml", "ref/netcore50/es/System.Diagnostics.Tools.xml", "ref/netcore50/fr/System.Diagnostics.Tools.xml", "ref/netcore50/it/System.Diagnostics.Tools.xml", "ref/netcore50/ja/System.Diagnostics.Tools.xml", "ref/netcore50/ko/System.Diagnostics.Tools.xml", "ref/netcore50/ru/System.Diagnostics.Tools.xml", "ref/netcore50/zh-hans/System.Diagnostics.Tools.xml", "ref/netcore50/zh-hant/System.Diagnostics.Tools.xml", "ref/netstandard1.0/System.Diagnostics.Tools.dll", "ref/netstandard1.0/System.Diagnostics.Tools.xml", "ref/netstandard1.0/de/System.Diagnostics.Tools.xml", "ref/netstandard1.0/es/System.Diagnostics.Tools.xml", "ref/netstandard1.0/fr/System.Diagnostics.Tools.xml", "ref/netstandard1.0/it/System.Diagnostics.Tools.xml", "ref/netstandard1.0/ja/System.Diagnostics.Tools.xml", "ref/netstandard1.0/ko/System.Diagnostics.Tools.xml", "ref/netstandard1.0/ru/System.Diagnostics.Tools.xml", "ref/netstandard1.0/zh-hans/System.Diagnostics.Tools.xml", "ref/netstandard1.0/zh-hant/System.Diagnostics.Tools.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.Diagnostics.Tracing/4.1.0": {"sha512": "vDN1PoMZCkkdNjvZLql592oYJZgS7URcJzJ7bxeBgGtx5UtR5leNm49VmfHGqIffX4FKacHbI3H6UyNSHQknBg==", "type": "package", "path": "System.Diagnostics.Tracing/4.1.0", "files": ["System.Diagnostics.Tracing.4.1.0.nupkg.sha512", "System.Diagnostics.Tracing.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Diagnostics.Tracing.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Diagnostics.Tracing.dll", "ref/netcore50/System.Diagnostics.Tracing.dll", "ref/netcore50/System.Diagnostics.Tracing.xml", "ref/netcore50/de/System.Diagnostics.Tracing.xml", "ref/netcore50/es/System.Diagnostics.Tracing.xml", "ref/netcore50/fr/System.Diagnostics.Tracing.xml", "ref/netcore50/it/System.Diagnostics.Tracing.xml", "ref/netcore50/ja/System.Diagnostics.Tracing.xml", "ref/netcore50/ko/System.Diagnostics.Tracing.xml", "ref/netcore50/ru/System.Diagnostics.Tracing.xml", "ref/netcore50/zh-hans/System.Diagnostics.Tracing.xml", "ref/netcore50/zh-hant/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/System.Diagnostics.Tracing.dll", "ref/netstandard1.1/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/de/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/es/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/fr/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/it/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/ja/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/ko/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/ru/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/zh-hans/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/zh-hant/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/System.Diagnostics.Tracing.dll", "ref/netstandard1.2/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/de/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/es/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/fr/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/it/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/ja/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/ko/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/ru/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/zh-hans/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/zh-hant/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/System.Diagnostics.Tracing.dll", "ref/netstandard1.3/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/de/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/es/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/fr/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/it/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/ja/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/ko/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/ru/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/zh-hans/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/zh-hant/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/System.Diagnostics.Tracing.dll", "ref/netstandard1.5/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/de/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/es/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/fr/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/it/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/ja/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/ko/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/ru/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/zh-hans/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/zh-hant/System.Diagnostics.Tracing.xml", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.Dynamic.Runtime/4.0.11": {"sha512": "db34f6LHYM0U0JpE+sOmjar27BnqTVkbLJhgfwMpTdgTigG/Hna3m2MYVwnFzGGKnEJk2UXFuoVTr8WUbU91/A==", "type": "package", "path": "System.Dynamic.Runtime/4.0.11", "files": ["System.Dynamic.Runtime.4.0.11.nupkg.sha512", "System.Dynamic.Runtime.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Dynamic.Runtime.dll", "lib/netstandard1.3/System.Dynamic.Runtime.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Dynamic.Runtime.dll", "ref/netcore50/System.Dynamic.Runtime.xml", "ref/netcore50/de/System.Dynamic.Runtime.xml", "ref/netcore50/es/System.Dynamic.Runtime.xml", "ref/netcore50/fr/System.Dynamic.Runtime.xml", "ref/netcore50/it/System.Dynamic.Runtime.xml", "ref/netcore50/ja/System.Dynamic.Runtime.xml", "ref/netcore50/ko/System.Dynamic.Runtime.xml", "ref/netcore50/ru/System.Dynamic.Runtime.xml", "ref/netcore50/zh-hans/System.Dynamic.Runtime.xml", "ref/netcore50/zh-hant/System.Dynamic.Runtime.xml", "ref/netstandard1.0/System.Dynamic.Runtime.dll", "ref/netstandard1.0/System.Dynamic.Runtime.xml", "ref/netstandard1.0/de/System.Dynamic.Runtime.xml", "ref/netstandard1.0/es/System.Dynamic.Runtime.xml", "ref/netstandard1.0/fr/System.Dynamic.Runtime.xml", "ref/netstandard1.0/it/System.Dynamic.Runtime.xml", "ref/netstandard1.0/ja/System.Dynamic.Runtime.xml", "ref/netstandard1.0/ko/System.Dynamic.Runtime.xml", "ref/netstandard1.0/ru/System.Dynamic.Runtime.xml", "ref/netstandard1.0/zh-hans/System.Dynamic.Runtime.xml", "ref/netstandard1.0/zh-hant/System.Dynamic.Runtime.xml", "ref/netstandard1.3/System.Dynamic.Runtime.dll", "ref/netstandard1.3/System.Dynamic.Runtime.xml", "ref/netstandard1.3/de/System.Dynamic.Runtime.xml", "ref/netstandard1.3/es/System.Dynamic.Runtime.xml", "ref/netstandard1.3/fr/System.Dynamic.Runtime.xml", "ref/netstandard1.3/it/System.Dynamic.Runtime.xml", "ref/netstandard1.3/ja/System.Dynamic.Runtime.xml", "ref/netstandard1.3/ko/System.Dynamic.Runtime.xml", "ref/netstandard1.3/ru/System.Dynamic.Runtime.xml", "ref/netstandard1.3/zh-hans/System.Dynamic.Runtime.xml", "ref/netstandard1.3/zh-hant/System.Dynamic.Runtime.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Dynamic.Runtime.dll"]}, "System.Globalization/4.0.11": {"sha512": "B95h0YLEL2oSnwF/XjqSWKnwKOy/01VWkNlsCeMTFJLLabflpGV26nK164eRs5GiaRSBGpOxQ3pKoSnnyZN5pg==", "type": "package", "path": "System.Globalization/4.0.11", "files": ["System.Globalization.4.0.11.nupkg.sha512", "System.Globalization.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Globalization.dll", "ref/netcore50/System.Globalization.xml", "ref/netcore50/de/System.Globalization.xml", "ref/netcore50/es/System.Globalization.xml", "ref/netcore50/fr/System.Globalization.xml", "ref/netcore50/it/System.Globalization.xml", "ref/netcore50/ja/System.Globalization.xml", "ref/netcore50/ko/System.Globalization.xml", "ref/netcore50/ru/System.Globalization.xml", "ref/netcore50/zh-hans/System.Globalization.xml", "ref/netcore50/zh-hant/System.Globalization.xml", "ref/netstandard1.0/System.Globalization.dll", "ref/netstandard1.0/System.Globalization.xml", "ref/netstandard1.0/de/System.Globalization.xml", "ref/netstandard1.0/es/System.Globalization.xml", "ref/netstandard1.0/fr/System.Globalization.xml", "ref/netstandard1.0/it/System.Globalization.xml", "ref/netstandard1.0/ja/System.Globalization.xml", "ref/netstandard1.0/ko/System.Globalization.xml", "ref/netstandard1.0/ru/System.Globalization.xml", "ref/netstandard1.0/zh-hans/System.Globalization.xml", "ref/netstandard1.0/zh-hant/System.Globalization.xml", "ref/netstandard1.3/System.Globalization.dll", "ref/netstandard1.3/System.Globalization.xml", "ref/netstandard1.3/de/System.Globalization.xml", "ref/netstandard1.3/es/System.Globalization.xml", "ref/netstandard1.3/fr/System.Globalization.xml", "ref/netstandard1.3/it/System.Globalization.xml", "ref/netstandard1.3/ja/System.Globalization.xml", "ref/netstandard1.3/ko/System.Globalization.xml", "ref/netstandard1.3/ru/System.Globalization.xml", "ref/netstandard1.3/zh-hans/System.Globalization.xml", "ref/netstandard1.3/zh-hant/System.Globalization.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.Globalization.Calendars/4.0.1": {"sha512": "L1c6IqeQ88vuzC1P81JeHmHA8mxq8a18NUBNXnIY/BVb+TCyAaGIFbhpZt60h9FJNmisymoQkHEFSE9Vslja1Q==", "type": "package", "path": "System.Globalization.Calendars/4.0.1", "files": ["System.Globalization.Calendars.4.0.1.nupkg.sha512", "System.Globalization.Calendars.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Globalization.Calendars.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Globalization.Calendars.dll", "ref/netstandard1.3/System.Globalization.Calendars.dll", "ref/netstandard1.3/System.Globalization.Calendars.xml", "ref/netstandard1.3/de/System.Globalization.Calendars.xml", "ref/netstandard1.3/es/System.Globalization.Calendars.xml", "ref/netstandard1.3/fr/System.Globalization.Calendars.xml", "ref/netstandard1.3/it/System.Globalization.Calendars.xml", "ref/netstandard1.3/ja/System.Globalization.Calendars.xml", "ref/netstandard1.3/ko/System.Globalization.Calendars.xml", "ref/netstandard1.3/ru/System.Globalization.Calendars.xml", "ref/netstandard1.3/zh-hans/System.Globalization.Calendars.xml", "ref/netstandard1.3/zh-hant/System.Globalization.Calendars.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.Globalization.Extensions/4.0.1": {"sha512": "KKo23iKeOaIg61SSXwjANN7QYDr/3op3OWGGzDzz7mypx0Za0fZSeG0l6cco8Ntp8YMYkIQcAqlk8yhm5/Uhcg==", "type": "package", "path": "System.Globalization.Extensions/4.0.1", "files": ["System.Globalization.Extensions.4.0.1.nupkg.sha512", "System.Globalization.Extensions.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Globalization.Extensions.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Globalization.Extensions.dll", "ref/netstandard1.3/System.Globalization.Extensions.dll", "ref/netstandard1.3/System.Globalization.Extensions.xml", "ref/netstandard1.3/de/System.Globalization.Extensions.xml", "ref/netstandard1.3/es/System.Globalization.Extensions.xml", "ref/netstandard1.3/fr/System.Globalization.Extensions.xml", "ref/netstandard1.3/it/System.Globalization.Extensions.xml", "ref/netstandard1.3/ja/System.Globalization.Extensions.xml", "ref/netstandard1.3/ko/System.Globalization.Extensions.xml", "ref/netstandard1.3/ru/System.Globalization.Extensions.xml", "ref/netstandard1.3/zh-hans/System.Globalization.Extensions.xml", "ref/netstandard1.3/zh-hant/System.Globalization.Extensions.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.3/System.Globalization.Extensions.dll", "runtimes/win/lib/net46/System.Globalization.Extensions.dll", "runtimes/win/lib/netstandard1.3/System.Globalization.Extensions.dll"]}, "System.IO/4.1.0": {"sha512": "3KlTJceQc3gnGIaHZ7UBZO26SHL1SHE4ddrmiwumFnId+CEHP+O8r386tZKaE6zlk5/mF8vifMBzHj9SaXN+mQ==", "type": "package", "path": "System.IO/4.1.0", "files": ["System.IO.4.1.0.nupkg.sha512", "System.IO.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.IO.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.IO.dll", "ref/netcore50/System.IO.dll", "ref/netcore50/System.IO.xml", "ref/netcore50/de/System.IO.xml", "ref/netcore50/es/System.IO.xml", "ref/netcore50/fr/System.IO.xml", "ref/netcore50/it/System.IO.xml", "ref/netcore50/ja/System.IO.xml", "ref/netcore50/ko/System.IO.xml", "ref/netcore50/ru/System.IO.xml", "ref/netcore50/zh-hans/System.IO.xml", "ref/netcore50/zh-hant/System.IO.xml", "ref/netstandard1.0/System.IO.dll", "ref/netstandard1.0/System.IO.xml", "ref/netstandard1.0/de/System.IO.xml", "ref/netstandard1.0/es/System.IO.xml", "ref/netstandard1.0/fr/System.IO.xml", "ref/netstandard1.0/it/System.IO.xml", "ref/netstandard1.0/ja/System.IO.xml", "ref/netstandard1.0/ko/System.IO.xml", "ref/netstandard1.0/ru/System.IO.xml", "ref/netstandard1.0/zh-hans/System.IO.xml", "ref/netstandard1.0/zh-hant/System.IO.xml", "ref/netstandard1.3/System.IO.dll", "ref/netstandard1.3/System.IO.xml", "ref/netstandard1.3/de/System.IO.xml", "ref/netstandard1.3/es/System.IO.xml", "ref/netstandard1.3/fr/System.IO.xml", "ref/netstandard1.3/it/System.IO.xml", "ref/netstandard1.3/ja/System.IO.xml", "ref/netstandard1.3/ko/System.IO.xml", "ref/netstandard1.3/ru/System.IO.xml", "ref/netstandard1.3/zh-hans/System.IO.xml", "ref/netstandard1.3/zh-hant/System.IO.xml", "ref/netstandard1.5/System.IO.dll", "ref/netstandard1.5/System.IO.xml", "ref/netstandard1.5/de/System.IO.xml", "ref/netstandard1.5/es/System.IO.xml", "ref/netstandard1.5/fr/System.IO.xml", "ref/netstandard1.5/it/System.IO.xml", "ref/netstandard1.5/ja/System.IO.xml", "ref/netstandard1.5/ko/System.IO.xml", "ref/netstandard1.5/ru/System.IO.xml", "ref/netstandard1.5/zh-hans/System.IO.xml", "ref/netstandard1.5/zh-hant/System.IO.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.IO.Compression/4.1.0": {"sha512": "TjnBS6eztThSzeSib+WyVbLzEdLKUcEHN69VtS3u8aAsSc18FU6xCZlNWWsEd8SKcXAE+y1sOu7VbU8sUeM0sg==", "type": "package", "path": "System.IO.Compression/4.1.0", "files": ["System.IO.Compression.4.1.0.nupkg.sha512", "System.IO.Compression.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net46/System.IO.Compression.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net46/System.IO.Compression.dll", "ref/netcore50/System.IO.Compression.dll", "ref/netcore50/System.IO.Compression.xml", "ref/netcore50/de/System.IO.Compression.xml", "ref/netcore50/es/System.IO.Compression.xml", "ref/netcore50/fr/System.IO.Compression.xml", "ref/netcore50/it/System.IO.Compression.xml", "ref/netcore50/ja/System.IO.Compression.xml", "ref/netcore50/ko/System.IO.Compression.xml", "ref/netcore50/ru/System.IO.Compression.xml", "ref/netcore50/zh-hans/System.IO.Compression.xml", "ref/netcore50/zh-hant/System.IO.Compression.xml", "ref/netstandard1.1/System.IO.Compression.dll", "ref/netstandard1.1/System.IO.Compression.xml", "ref/netstandard1.1/de/System.IO.Compression.xml", "ref/netstandard1.1/es/System.IO.Compression.xml", "ref/netstandard1.1/fr/System.IO.Compression.xml", "ref/netstandard1.1/it/System.IO.Compression.xml", "ref/netstandard1.1/ja/System.IO.Compression.xml", "ref/netstandard1.1/ko/System.IO.Compression.xml", "ref/netstandard1.1/ru/System.IO.Compression.xml", "ref/netstandard1.1/zh-hans/System.IO.Compression.xml", "ref/netstandard1.1/zh-hant/System.IO.Compression.xml", "ref/netstandard1.3/System.IO.Compression.dll", "ref/netstandard1.3/System.IO.Compression.xml", "ref/netstandard1.3/de/System.IO.Compression.xml", "ref/netstandard1.3/es/System.IO.Compression.xml", "ref/netstandard1.3/fr/System.IO.Compression.xml", "ref/netstandard1.3/it/System.IO.Compression.xml", "ref/netstandard1.3/ja/System.IO.Compression.xml", "ref/netstandard1.3/ko/System.IO.Compression.xml", "ref/netstandard1.3/ru/System.IO.Compression.xml", "ref/netstandard1.3/zh-hans/System.IO.Compression.xml", "ref/netstandard1.3/zh-hant/System.IO.Compression.xml", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.3/System.IO.Compression.dll", "runtimes/win/lib/net46/System.IO.Compression.dll", "runtimes/win/lib/netstandard1.3/System.IO.Compression.dll"]}, "System.IO.Compression.ZipFile/4.0.1": {"sha512": "hBQYJzfTbQURF10nLhd+az2NHxsU6MU7AB8RUf4IolBP5lOAm4Luho851xl+CqslmhI5ZH/el8BlngEk4lBkaQ==", "type": "package", "path": "System.IO.Compression.ZipFile/4.0.1", "files": ["System.IO.Compression.ZipFile.4.0.1.nupkg.sha512", "System.IO.Compression.ZipFile.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.IO.Compression.ZipFile.dll", "lib/netstandard1.3/System.IO.Compression.ZipFile.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.IO.Compression.ZipFile.dll", "ref/netstandard1.3/System.IO.Compression.ZipFile.dll", "ref/netstandard1.3/System.IO.Compression.ZipFile.xml", "ref/netstandard1.3/de/System.IO.Compression.ZipFile.xml", "ref/netstandard1.3/es/System.IO.Compression.ZipFile.xml", "ref/netstandard1.3/fr/System.IO.Compression.ZipFile.xml", "ref/netstandard1.3/it/System.IO.Compression.ZipFile.xml", "ref/netstandard1.3/ja/System.IO.Compression.ZipFile.xml", "ref/netstandard1.3/ko/System.IO.Compression.ZipFile.xml", "ref/netstandard1.3/ru/System.IO.Compression.ZipFile.xml", "ref/netstandard1.3/zh-hans/System.IO.Compression.ZipFile.xml", "ref/netstandard1.3/zh-hant/System.IO.Compression.ZipFile.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.IO.FileSystem/4.0.1": {"sha512": "IBErlVq5jOggAD69bg1t0pJcHaDbJbWNUZTPI96fkYWzwYbN6D9wRHMULLDd9dHsl7C2YsxXL31LMfPI1SWt8w==", "type": "package", "path": "System.IO.FileSystem/4.0.1", "files": ["System.IO.FileSystem.4.0.1.nupkg.sha512", "System.IO.FileSystem.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.IO.FileSystem.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.IO.FileSystem.dll", "ref/netstandard1.3/System.IO.FileSystem.dll", "ref/netstandard1.3/System.IO.FileSystem.xml", "ref/netstandard1.3/de/System.IO.FileSystem.xml", "ref/netstandard1.3/es/System.IO.FileSystem.xml", "ref/netstandard1.3/fr/System.IO.FileSystem.xml", "ref/netstandard1.3/it/System.IO.FileSystem.xml", "ref/netstandard1.3/ja/System.IO.FileSystem.xml", "ref/netstandard1.3/ko/System.IO.FileSystem.xml", "ref/netstandard1.3/ru/System.IO.FileSystem.xml", "ref/netstandard1.3/zh-hans/System.IO.FileSystem.xml", "ref/netstandard1.3/zh-hant/System.IO.FileSystem.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.IO.FileSystem.Primitives/4.0.1": {"sha512": "kWkKD203JJKxJeE74p8aF8y4Qc9r9WQx4C0cHzHPrY3fv/L/IhWnyCHaFJ3H1QPOH6A93whlQ2vG5nHlBDvzWQ==", "type": "package", "path": "System.IO.FileSystem.Primitives/4.0.1", "files": ["System.IO.FileSystem.Primitives.4.0.1.nupkg.sha512", "System.IO.FileSystem.Primitives.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.IO.FileSystem.Primitives.dll", "lib/netstandard1.3/System.IO.FileSystem.Primitives.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.IO.FileSystem.Primitives.dll", "ref/netstandard1.3/System.IO.FileSystem.Primitives.dll", "ref/netstandard1.3/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/de/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/es/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/fr/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/it/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/ja/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/ko/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/ru/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/zh-hans/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/zh-hant/System.IO.FileSystem.Primitives.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.IO.FileSystem.Watcher/4.0.0": {"sha512": "qM4Wr3La+RYb/03B0mZZjbA7tHsGzDffnuXP8Sl48HW2JwCjn3kfD5qdw0sqyNNowUipcJMi9/q6sMUrOIJ6UQ==", "type": "package", "path": "System.IO.FileSystem.Watcher/4.0.0", "files": ["System.IO.FileSystem.Watcher.4.0.0.nupkg.sha512", "System.IO.FileSystem.Watcher.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.IO.FileSystem.Watcher.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.IO.FileSystem.Watcher.dll", "ref/netstandard1.3/System.IO.FileSystem.Watcher.dll", "ref/netstandard1.3/System.IO.FileSystem.Watcher.xml", "ref/netstandard1.3/de/System.IO.FileSystem.Watcher.xml", "ref/netstandard1.3/es/System.IO.FileSystem.Watcher.xml", "ref/netstandard1.3/fr/System.IO.FileSystem.Watcher.xml", "ref/netstandard1.3/it/System.IO.FileSystem.Watcher.xml", "ref/netstandard1.3/ja/System.IO.FileSystem.Watcher.xml", "ref/netstandard1.3/ko/System.IO.FileSystem.Watcher.xml", "ref/netstandard1.3/ru/System.IO.FileSystem.Watcher.xml", "ref/netstandard1.3/zh-hans/System.IO.FileSystem.Watcher.xml", "ref/netstandard1.3/zh-hant/System.IO.FileSystem.Watcher.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/linux/lib/netstandard1.3/System.IO.FileSystem.Watcher.dll", "runtimes/osx/lib/netstandard1.3/System.IO.FileSystem.Watcher.dll", "runtimes/win/lib/net46/System.IO.FileSystem.Watcher.dll", "runtimes/win/lib/netstandard1.3/System.IO.FileSystem.Watcher.dll", "runtimes/win7/lib/netcore50/_._"]}, "System.IO.MemoryMappedFiles/4.0.0": {"sha512": "Xqj4xaFAnLVpss9ZSUIvB/VdJAA7GxZDnFGDKJfiGAnZ5VnFROn6eOHWepFpujCYTsh6wlZ3B33bqYkF0QJ7Eg==", "type": "package", "path": "System.IO.MemoryMappedFiles/4.0.0", "files": ["System.IO.MemoryMappedFiles.4.0.0.nupkg.sha512", "System.IO.MemoryMappedFiles.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.IO.MemoryMappedFiles.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.IO.MemoryMappedFiles.dll", "ref/netstandard1.3/System.IO.MemoryMappedFiles.dll", "ref/netstandard1.3/System.IO.MemoryMappedFiles.xml", "ref/netstandard1.3/de/System.IO.MemoryMappedFiles.xml", "ref/netstandard1.3/es/System.IO.MemoryMappedFiles.xml", "ref/netstandard1.3/fr/System.IO.MemoryMappedFiles.xml", "ref/netstandard1.3/it/System.IO.MemoryMappedFiles.xml", "ref/netstandard1.3/ja/System.IO.MemoryMappedFiles.xml", "ref/netstandard1.3/ko/System.IO.MemoryMappedFiles.xml", "ref/netstandard1.3/ru/System.IO.MemoryMappedFiles.xml", "ref/netstandard1.3/zh-hans/System.IO.MemoryMappedFiles.xml", "ref/netstandard1.3/zh-hant/System.IO.MemoryMappedFiles.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.3/System.IO.MemoryMappedFiles.dll", "runtimes/win/lib/net46/System.IO.MemoryMappedFiles.dll", "runtimes/win/lib/netcore50/System.IO.MemoryMappedFiles.dll", "runtimes/win/lib/netstandard1.3/System.IO.MemoryMappedFiles.dll"]}, "System.IO.UnmanagedMemoryStream/4.0.1": {"sha512": "wcq0kXcpfJwdl1Y4/ZjDk7Dhx5HdLyRYYWYmD8Nn8skoGYYQd2BQWbXwjWSczip8AL4Z57o2dWWXAl4aABAKiQ==", "type": "package", "path": "System.IO.UnmanagedMemoryStream/4.0.1", "files": ["System.IO.UnmanagedMemoryStream.4.0.1.nupkg.sha512", "System.IO.UnmanagedMemoryStream.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.IO.UnmanagedMemoryStream.dll", "lib/netstandard1.3/System.IO.UnmanagedMemoryStream.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.IO.UnmanagedMemoryStream.dll", "ref/netstandard1.3/System.IO.UnmanagedMemoryStream.dll", "ref/netstandard1.3/System.IO.UnmanagedMemoryStream.xml", "ref/netstandard1.3/de/System.IO.UnmanagedMemoryStream.xml", "ref/netstandard1.3/es/System.IO.UnmanagedMemoryStream.xml", "ref/netstandard1.3/fr/System.IO.UnmanagedMemoryStream.xml", "ref/netstandard1.3/it/System.IO.UnmanagedMemoryStream.xml", "ref/netstandard1.3/ja/System.IO.UnmanagedMemoryStream.xml", "ref/netstandard1.3/ko/System.IO.UnmanagedMemoryStream.xml", "ref/netstandard1.3/ru/System.IO.UnmanagedMemoryStream.xml", "ref/netstandard1.3/zh-hans/System.IO.UnmanagedMemoryStream.xml", "ref/netstandard1.3/zh-hant/System.IO.UnmanagedMemoryStream.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.Linq/4.1.0": {"sha512": "bQ0iYFOQI0nuTnt+NQADns6ucV4DUvMdwN6CbkB1yj8i7arTGiTN5eok1kQwdnnNWSDZfIUySQY+J3d5KjWn0g==", "type": "package", "path": "System.Linq/4.1.0", "files": ["System.Linq.4.1.0.nupkg.sha512", "System.Linq.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net463/System.Linq.dll", "lib/netcore50/System.Linq.dll", "lib/netstandard1.6/System.Linq.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net463/System.Linq.dll", "ref/netcore50/System.Linq.dll", "ref/netcore50/System.Linq.xml", "ref/netcore50/de/System.Linq.xml", "ref/netcore50/es/System.Linq.xml", "ref/netcore50/fr/System.Linq.xml", "ref/netcore50/it/System.Linq.xml", "ref/netcore50/ja/System.Linq.xml", "ref/netcore50/ko/System.Linq.xml", "ref/netcore50/ru/System.Linq.xml", "ref/netcore50/zh-hans/System.Linq.xml", "ref/netcore50/zh-hant/System.Linq.xml", "ref/netstandard1.0/System.Linq.dll", "ref/netstandard1.0/System.Linq.xml", "ref/netstandard1.0/de/System.Linq.xml", "ref/netstandard1.0/es/System.Linq.xml", "ref/netstandard1.0/fr/System.Linq.xml", "ref/netstandard1.0/it/System.Linq.xml", "ref/netstandard1.0/ja/System.Linq.xml", "ref/netstandard1.0/ko/System.Linq.xml", "ref/netstandard1.0/ru/System.Linq.xml", "ref/netstandard1.0/zh-hans/System.Linq.xml", "ref/netstandard1.0/zh-hant/System.Linq.xml", "ref/netstandard1.6/System.Linq.dll", "ref/netstandard1.6/System.Linq.xml", "ref/netstandard1.6/de/System.Linq.xml", "ref/netstandard1.6/es/System.Linq.xml", "ref/netstandard1.6/fr/System.Linq.xml", "ref/netstandard1.6/it/System.Linq.xml", "ref/netstandard1.6/ja/System.Linq.xml", "ref/netstandard1.6/ko/System.Linq.xml", "ref/netstandard1.6/ru/System.Linq.xml", "ref/netstandard1.6/zh-hans/System.Linq.xml", "ref/netstandard1.6/zh-hant/System.Linq.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.Linq.Expressions/4.1.0": {"sha512": "I+y02iqkgmCAyfbqOmSDOgqdZQ5tTj80Akm5BPSS8EeB0VGWdy6X1KCoYe8Pk6pwDoAKZUOdLVxnTJcExiv5zw==", "type": "package", "path": "System.Linq.Expressions/4.1.0", "files": ["System.Linq.Expressions.4.1.0.nupkg.sha512", "System.Linq.Expressions.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net463/System.Linq.Expressions.dll", "lib/netcore50/System.Linq.Expressions.dll", "lib/netstandard1.6/System.Linq.Expressions.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net463/System.Linq.Expressions.dll", "ref/netcore50/System.Linq.Expressions.dll", "ref/netcore50/System.Linq.Expressions.xml", "ref/netcore50/de/System.Linq.Expressions.xml", "ref/netcore50/es/System.Linq.Expressions.xml", "ref/netcore50/fr/System.Linq.Expressions.xml", "ref/netcore50/it/System.Linq.Expressions.xml", "ref/netcore50/ja/System.Linq.Expressions.xml", "ref/netcore50/ko/System.Linq.Expressions.xml", "ref/netcore50/ru/System.Linq.Expressions.xml", "ref/netcore50/zh-hans/System.Linq.Expressions.xml", "ref/netcore50/zh-hant/System.Linq.Expressions.xml", "ref/netstandard1.0/System.Linq.Expressions.dll", "ref/netstandard1.0/System.Linq.Expressions.xml", "ref/netstandard1.0/de/System.Linq.Expressions.xml", "ref/netstandard1.0/es/System.Linq.Expressions.xml", "ref/netstandard1.0/fr/System.Linq.Expressions.xml", "ref/netstandard1.0/it/System.Linq.Expressions.xml", "ref/netstandard1.0/ja/System.Linq.Expressions.xml", "ref/netstandard1.0/ko/System.Linq.Expressions.xml", "ref/netstandard1.0/ru/System.Linq.Expressions.xml", "ref/netstandard1.0/zh-hans/System.Linq.Expressions.xml", "ref/netstandard1.0/zh-hant/System.Linq.Expressions.xml", "ref/netstandard1.3/System.Linq.Expressions.dll", "ref/netstandard1.3/System.Linq.Expressions.xml", "ref/netstandard1.3/de/System.Linq.Expressions.xml", "ref/netstandard1.3/es/System.Linq.Expressions.xml", "ref/netstandard1.3/fr/System.Linq.Expressions.xml", "ref/netstandard1.3/it/System.Linq.Expressions.xml", "ref/netstandard1.3/ja/System.Linq.Expressions.xml", "ref/netstandard1.3/ko/System.Linq.Expressions.xml", "ref/netstandard1.3/ru/System.Linq.Expressions.xml", "ref/netstandard1.3/zh-hans/System.Linq.Expressions.xml", "ref/netstandard1.3/zh-hant/System.Linq.Expressions.xml", "ref/netstandard1.6/System.Linq.Expressions.dll", "ref/netstandard1.6/System.Linq.Expressions.xml", "ref/netstandard1.6/de/System.Linq.Expressions.xml", "ref/netstandard1.6/es/System.Linq.Expressions.xml", "ref/netstandard1.6/fr/System.Linq.Expressions.xml", "ref/netstandard1.6/it/System.Linq.Expressions.xml", "ref/netstandard1.6/ja/System.Linq.Expressions.xml", "ref/netstandard1.6/ko/System.Linq.Expressions.xml", "ref/netstandard1.6/ru/System.Linq.Expressions.xml", "ref/netstandard1.6/zh-hans/System.Linq.Expressions.xml", "ref/netstandard1.6/zh-hant/System.Linq.Expressions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Linq.Expressions.dll"]}, "System.Linq.Parallel/4.0.1": {"sha512": "J7XCa7n2cFn32uLbtceXfBFhgCk5M++50lylHKNbqTiJkw5y4Tglpi6amuJNPCvj9bLzNSI7rs1fi4joLMNRgg==", "type": "package", "path": "System.Linq.Parallel/4.0.1", "files": ["System.Linq.Parallel.4.0.1.nupkg.sha512", "System.Linq.Parallel.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Linq.Parallel.dll", "lib/netstandard1.3/System.Linq.Parallel.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Linq.Parallel.dll", "ref/netcore50/System.Linq.Parallel.xml", "ref/netcore50/de/System.Linq.Parallel.xml", "ref/netcore50/es/System.Linq.Parallel.xml", "ref/netcore50/fr/System.Linq.Parallel.xml", "ref/netcore50/it/System.Linq.Parallel.xml", "ref/netcore50/ja/System.Linq.Parallel.xml", "ref/netcore50/ko/System.Linq.Parallel.xml", "ref/netcore50/ru/System.Linq.Parallel.xml", "ref/netcore50/zh-hans/System.Linq.Parallel.xml", "ref/netcore50/zh-hant/System.Linq.Parallel.xml", "ref/netstandard1.1/System.Linq.Parallel.dll", "ref/netstandard1.1/System.Linq.Parallel.xml", "ref/netstandard1.1/de/System.Linq.Parallel.xml", "ref/netstandard1.1/es/System.Linq.Parallel.xml", "ref/netstandard1.1/fr/System.Linq.Parallel.xml", "ref/netstandard1.1/it/System.Linq.Parallel.xml", "ref/netstandard1.1/ja/System.Linq.Parallel.xml", "ref/netstandard1.1/ko/System.Linq.Parallel.xml", "ref/netstandard1.1/ru/System.Linq.Parallel.xml", "ref/netstandard1.1/zh-hans/System.Linq.Parallel.xml", "ref/netstandard1.1/zh-hant/System.Linq.Parallel.xml", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.Linq.Queryable/4.0.1": {"sha512": "Yn/WfYe9RoRfmSLvUt2JerP0BTGGykCZkQPgojaxgzF2N0oPo+/AhB8TXOpdCcNlrG3VRtsamtK2uzsp3cqRVw==", "type": "package", "path": "System.Linq.Queryable/4.0.1", "files": ["System.Linq.Queryable.4.0.1.nupkg.sha512", "System.Linq.Queryable.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/monoandroid10/_._", "lib/monotouch10/_._", "lib/net45/_._", "lib/netcore50/System.Linq.Queryable.dll", "lib/netstandard1.3/System.Linq.Queryable.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/monoandroid10/_._", "ref/monotouch10/_._", "ref/net45/_._", "ref/netcore50/System.Linq.Queryable.dll", "ref/netcore50/System.Linq.Queryable.xml", "ref/netcore50/de/System.Linq.Queryable.xml", "ref/netcore50/es/System.Linq.Queryable.xml", "ref/netcore50/fr/System.Linq.Queryable.xml", "ref/netcore50/it/System.Linq.Queryable.xml", "ref/netcore50/ja/System.Linq.Queryable.xml", "ref/netcore50/ko/System.Linq.Queryable.xml", "ref/netcore50/ru/System.Linq.Queryable.xml", "ref/netcore50/zh-hans/System.Linq.Queryable.xml", "ref/netcore50/zh-hant/System.Linq.Queryable.xml", "ref/netstandard1.0/System.Linq.Queryable.dll", "ref/netstandard1.0/System.Linq.Queryable.xml", "ref/netstandard1.0/de/System.Linq.Queryable.xml", "ref/netstandard1.0/es/System.Linq.Queryable.xml", "ref/netstandard1.0/fr/System.Linq.Queryable.xml", "ref/netstandard1.0/it/System.Linq.Queryable.xml", "ref/netstandard1.0/ja/System.Linq.Queryable.xml", "ref/netstandard1.0/ko/System.Linq.Queryable.xml", "ref/netstandard1.0/ru/System.Linq.Queryable.xml", "ref/netstandard1.0/zh-hans/System.Linq.Queryable.xml", "ref/netstandard1.0/zh-hant/System.Linq.Queryable.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.Net.Http/4.1.0": {"sha512": "ULq9g3SOPVuupt+Y3U+A37coXzdNisB1neFCSKzBwo182u0RDddKJF8I5+HfyXqK6OhJPgeoAwWXrbiUXuRDsg==", "type": "package", "path": "System.Net.Http/4.1.0", "files": ["System.Net.Http.4.1.0.nupkg.sha512", "System.Net.Http.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/Xamarinmac20/_._", "lib/monoandroid10/_._", "lib/monotouch10/_._", "lib/net45/_._", "lib/net46/System.Net.Http.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/Xamarinmac20/_._", "ref/monoandroid10/_._", "ref/monotouch10/_._", "ref/net45/_._", "ref/net46/System.Net.Http.dll", "ref/net46/System.Net.Http.xml", "ref/net46/de/System.Net.Http.xml", "ref/net46/es/System.Net.Http.xml", "ref/net46/fr/System.Net.Http.xml", "ref/net46/it/System.Net.Http.xml", "ref/net46/ja/System.Net.Http.xml", "ref/net46/ko/System.Net.Http.xml", "ref/net46/ru/System.Net.Http.xml", "ref/net46/zh-hans/System.Net.Http.xml", "ref/net46/zh-hant/System.Net.Http.xml", "ref/netcore50/System.Net.Http.dll", "ref/netcore50/System.Net.Http.xml", "ref/netcore50/de/System.Net.Http.xml", "ref/netcore50/es/System.Net.Http.xml", "ref/netcore50/fr/System.Net.Http.xml", "ref/netcore50/it/System.Net.Http.xml", "ref/netcore50/ja/System.Net.Http.xml", "ref/netcore50/ko/System.Net.Http.xml", "ref/netcore50/ru/System.Net.Http.xml", "ref/netcore50/zh-hans/System.Net.Http.xml", "ref/netcore50/zh-hant/System.Net.Http.xml", "ref/netstandard1.1/System.Net.Http.dll", "ref/netstandard1.1/System.Net.Http.xml", "ref/netstandard1.1/de/System.Net.Http.xml", "ref/netstandard1.1/es/System.Net.Http.xml", "ref/netstandard1.1/fr/System.Net.Http.xml", "ref/netstandard1.1/it/System.Net.Http.xml", "ref/netstandard1.1/ja/System.Net.Http.xml", "ref/netstandard1.1/ko/System.Net.Http.xml", "ref/netstandard1.1/ru/System.Net.Http.xml", "ref/netstandard1.1/zh-hans/System.Net.Http.xml", "ref/netstandard1.1/zh-hant/System.Net.Http.xml", "ref/netstandard1.3/System.Net.Http.dll", "ref/netstandard1.3/System.Net.Http.xml", "ref/netstandard1.3/de/System.Net.Http.xml", "ref/netstandard1.3/es/System.Net.Http.xml", "ref/netstandard1.3/fr/System.Net.Http.xml", "ref/netstandard1.3/it/System.Net.Http.xml", "ref/netstandard1.3/ja/System.Net.Http.xml", "ref/netstandard1.3/ko/System.Net.Http.xml", "ref/netstandard1.3/ru/System.Net.Http.xml", "ref/netstandard1.3/zh-hans/System.Net.Http.xml", "ref/netstandard1.3/zh-hant/System.Net.Http.xml", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.6/System.Net.Http.dll", "runtimes/win/lib/net46/System.Net.Http.dll", "runtimes/win/lib/netcore50/System.Net.Http.dll", "runtimes/win/lib/netstandard1.3/System.Net.Http.dll"]}, "System.Net.NameResolution/4.0.0": {"sha512": "JdqRdM1Qym3YehqdKIi5LHrpypP4JMfxKQSNCJ2z4WawkG0il+N3XfNeJOxll2XrTnG7WgYYPoeiu/KOwg0DQw==", "type": "package", "path": "System.Net.NameResolution/4.0.0", "files": ["System.Net.NameResolution.4.0.0.nupkg.sha512", "System.Net.NameResolution.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Net.NameResolution.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Net.NameResolution.dll", "ref/netstandard1.3/System.Net.NameResolution.dll", "ref/netstandard1.3/System.Net.NameResolution.xml", "ref/netstandard1.3/de/System.Net.NameResolution.xml", "ref/netstandard1.3/es/System.Net.NameResolution.xml", "ref/netstandard1.3/fr/System.Net.NameResolution.xml", "ref/netstandard1.3/it/System.Net.NameResolution.xml", "ref/netstandard1.3/ja/System.Net.NameResolution.xml", "ref/netstandard1.3/ko/System.Net.NameResolution.xml", "ref/netstandard1.3/ru/System.Net.NameResolution.xml", "ref/netstandard1.3/zh-hans/System.Net.NameResolution.xml", "ref/netstandard1.3/zh-hant/System.Net.NameResolution.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.3/System.Net.NameResolution.dll", "runtimes/win/lib/net46/System.Net.NameResolution.dll", "runtimes/win/lib/netcore50/System.Net.NameResolution.dll", "runtimes/win/lib/netstandard1.3/System.Net.NameResolution.dll"]}, "System.Net.Primitives/4.0.11": {"sha512": "hVvfl4405DRjA2408luZekbPhplJK03j2Y2lSfMlny7GHXlkByw1iLnc9mgKW0GdQn73vvMcWrWewAhylXA4Nw==", "type": "package", "path": "System.Net.Primitives/4.0.11", "files": ["System.Net.Primitives.4.0.11.nupkg.sha512", "System.Net.Primitives.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Net.Primitives.dll", "ref/netcore50/System.Net.Primitives.xml", "ref/netcore50/de/System.Net.Primitives.xml", "ref/netcore50/es/System.Net.Primitives.xml", "ref/netcore50/fr/System.Net.Primitives.xml", "ref/netcore50/it/System.Net.Primitives.xml", "ref/netcore50/ja/System.Net.Primitives.xml", "ref/netcore50/ko/System.Net.Primitives.xml", "ref/netcore50/ru/System.Net.Primitives.xml", "ref/netcore50/zh-hans/System.Net.Primitives.xml", "ref/netcore50/zh-hant/System.Net.Primitives.xml", "ref/netstandard1.0/System.Net.Primitives.dll", "ref/netstandard1.0/System.Net.Primitives.xml", "ref/netstandard1.0/de/System.Net.Primitives.xml", "ref/netstandard1.0/es/System.Net.Primitives.xml", "ref/netstandard1.0/fr/System.Net.Primitives.xml", "ref/netstandard1.0/it/System.Net.Primitives.xml", "ref/netstandard1.0/ja/System.Net.Primitives.xml", "ref/netstandard1.0/ko/System.Net.Primitives.xml", "ref/netstandard1.0/ru/System.Net.Primitives.xml", "ref/netstandard1.0/zh-hans/System.Net.Primitives.xml", "ref/netstandard1.0/zh-hant/System.Net.Primitives.xml", "ref/netstandard1.1/System.Net.Primitives.dll", "ref/netstandard1.1/System.Net.Primitives.xml", "ref/netstandard1.1/de/System.Net.Primitives.xml", "ref/netstandard1.1/es/System.Net.Primitives.xml", "ref/netstandard1.1/fr/System.Net.Primitives.xml", "ref/netstandard1.1/it/System.Net.Primitives.xml", "ref/netstandard1.1/ja/System.Net.Primitives.xml", "ref/netstandard1.1/ko/System.Net.Primitives.xml", "ref/netstandard1.1/ru/System.Net.Primitives.xml", "ref/netstandard1.1/zh-hans/System.Net.Primitives.xml", "ref/netstandard1.1/zh-hant/System.Net.Primitives.xml", "ref/netstandard1.3/System.Net.Primitives.dll", "ref/netstandard1.3/System.Net.Primitives.xml", "ref/netstandard1.3/de/System.Net.Primitives.xml", "ref/netstandard1.3/es/System.Net.Primitives.xml", "ref/netstandard1.3/fr/System.Net.Primitives.xml", "ref/netstandard1.3/it/System.Net.Primitives.xml", "ref/netstandard1.3/ja/System.Net.Primitives.xml", "ref/netstandard1.3/ko/System.Net.Primitives.xml", "ref/netstandard1.3/ru/System.Net.Primitives.xml", "ref/netstandard1.3/zh-hans/System.Net.Primitives.xml", "ref/netstandard1.3/zh-hant/System.Net.Primitives.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.Net.Requests/4.0.11": {"sha512": "vxGt7C0cZixN+VqoSW4Yakc1Y9WknmxauDqzxgpw/FnBdz4kQNN51l4wxdXX5VY1xjqy//+G+4CvJWp1+f+y6Q==", "type": "package", "path": "System.Net.Requests/4.0.11", "files": ["System.Net.Requests.4.0.11.nupkg.sha512", "System.Net.Requests.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net46/_._", "ref/netcore50/System.Net.Requests.dll", "ref/netcore50/System.Net.Requests.xml", "ref/netcore50/de/System.Net.Requests.xml", "ref/netcore50/es/System.Net.Requests.xml", "ref/netcore50/fr/System.Net.Requests.xml", "ref/netcore50/it/System.Net.Requests.xml", "ref/netcore50/ja/System.Net.Requests.xml", "ref/netcore50/ko/System.Net.Requests.xml", "ref/netcore50/ru/System.Net.Requests.xml", "ref/netcore50/zh-hans/System.Net.Requests.xml", "ref/netcore50/zh-hant/System.Net.Requests.xml", "ref/netstandard1.0/System.Net.Requests.dll", "ref/netstandard1.0/System.Net.Requests.xml", "ref/netstandard1.0/de/System.Net.Requests.xml", "ref/netstandard1.0/es/System.Net.Requests.xml", "ref/netstandard1.0/fr/System.Net.Requests.xml", "ref/netstandard1.0/it/System.Net.Requests.xml", "ref/netstandard1.0/ja/System.Net.Requests.xml", "ref/netstandard1.0/ko/System.Net.Requests.xml", "ref/netstandard1.0/ru/System.Net.Requests.xml", "ref/netstandard1.0/zh-hans/System.Net.Requests.xml", "ref/netstandard1.0/zh-hant/System.Net.Requests.xml", "ref/netstandard1.1/System.Net.Requests.dll", "ref/netstandard1.1/System.Net.Requests.xml", "ref/netstandard1.1/de/System.Net.Requests.xml", "ref/netstandard1.1/es/System.Net.Requests.xml", "ref/netstandard1.1/fr/System.Net.Requests.xml", "ref/netstandard1.1/it/System.Net.Requests.xml", "ref/netstandard1.1/ja/System.Net.Requests.xml", "ref/netstandard1.1/ko/System.Net.Requests.xml", "ref/netstandard1.1/ru/System.Net.Requests.xml", "ref/netstandard1.1/zh-hans/System.Net.Requests.xml", "ref/netstandard1.1/zh-hant/System.Net.Requests.xml", "ref/netstandard1.3/System.Net.Requests.dll", "ref/netstandard1.3/System.Net.Requests.xml", "ref/netstandard1.3/de/System.Net.Requests.xml", "ref/netstandard1.3/es/System.Net.Requests.xml", "ref/netstandard1.3/fr/System.Net.Requests.xml", "ref/netstandard1.3/it/System.Net.Requests.xml", "ref/netstandard1.3/ja/System.Net.Requests.xml", "ref/netstandard1.3/ko/System.Net.Requests.xml", "ref/netstandard1.3/ru/System.Net.Requests.xml", "ref/netstandard1.3/zh-hans/System.Net.Requests.xml", "ref/netstandard1.3/zh-hant/System.Net.Requests.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.3/System.Net.Requests.dll", "runtimes/win/lib/net46/_._", "runtimes/win/lib/netstandard1.3/System.Net.Requests.dll"]}, "System.Net.Security/4.0.0": {"sha512": "uM1JaYJciCc2w7efD6du0EpQ1n5ZQqE6/P43/aI4H5E59qvP+wt3l70KIUF/Ha7NaeXGoGNFPVO0MB80pVHk2g==", "type": "package", "path": "System.Net.Security/4.0.0", "files": ["System.Net.Security.4.0.0.nupkg.sha512", "System.Net.Security.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Net.Security.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Net.Security.dll", "ref/netstandard1.3/System.Net.Security.dll", "ref/netstandard1.3/System.Net.Security.xml", "ref/netstandard1.3/de/System.Net.Security.xml", "ref/netstandard1.3/es/System.Net.Security.xml", "ref/netstandard1.3/fr/System.Net.Security.xml", "ref/netstandard1.3/it/System.Net.Security.xml", "ref/netstandard1.3/ja/System.Net.Security.xml", "ref/netstandard1.3/ko/System.Net.Security.xml", "ref/netstandard1.3/ru/System.Net.Security.xml", "ref/netstandard1.3/zh-hans/System.Net.Security.xml", "ref/netstandard1.3/zh-hant/System.Net.Security.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.6/System.Net.Security.dll", "runtimes/win/lib/net46/System.Net.Security.dll", "runtimes/win/lib/netstandard1.3/System.Net.Security.dll", "runtimes/win7/lib/netcore50/_._"]}, "System.Net.Sockets/4.1.0": {"sha512": "xAz0N3dAV/aR/9g8r0Y5oEqU1JRsz29F5EGb/WVHmX3jVSLqi2/92M5hTad2aNWovruXrJpJtgZ9fccPMG9uSw==", "type": "package", "path": "System.Net.Sockets/4.1.0", "files": ["System.Net.Sockets.4.1.0.nupkg.sha512", "System.Net.Sockets.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Net.Sockets.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Net.Sockets.dll", "ref/netstandard1.3/System.Net.Sockets.dll", "ref/netstandard1.3/System.Net.Sockets.xml", "ref/netstandard1.3/de/System.Net.Sockets.xml", "ref/netstandard1.3/es/System.Net.Sockets.xml", "ref/netstandard1.3/fr/System.Net.Sockets.xml", "ref/netstandard1.3/it/System.Net.Sockets.xml", "ref/netstandard1.3/ja/System.Net.Sockets.xml", "ref/netstandard1.3/ko/System.Net.Sockets.xml", "ref/netstandard1.3/ru/System.Net.Sockets.xml", "ref/netstandard1.3/zh-hans/System.Net.Sockets.xml", "ref/netstandard1.3/zh-hant/System.Net.Sockets.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.Net.WebHeaderCollection/4.0.1": {"sha512": "XX2TIAN+wBSAIV51BU2FvvXMdstUa8b0FBSZmDWjZdwUMmggQSifpTOZ5fNH20z9ZCg2fkV1L5SsZnpO2RQDRQ==", "type": "package", "path": "System.Net.WebHeaderCollection/4.0.1", "files": ["System.Net.WebHeaderCollection.4.0.1.nupkg.sha512", "System.Net.WebHeaderCollection.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/_._", "lib/netstandard1.3/System.Net.WebHeaderCollection.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/_._", "ref/netstandard1.3/System.Net.WebHeaderCollection.dll", "ref/netstandard1.3/System.Net.WebHeaderCollection.xml", "ref/netstandard1.3/de/System.Net.WebHeaderCollection.xml", "ref/netstandard1.3/es/System.Net.WebHeaderCollection.xml", "ref/netstandard1.3/fr/System.Net.WebHeaderCollection.xml", "ref/netstandard1.3/it/System.Net.WebHeaderCollection.xml", "ref/netstandard1.3/ja/System.Net.WebHeaderCollection.xml", "ref/netstandard1.3/ko/System.Net.WebHeaderCollection.xml", "ref/netstandard1.3/ru/System.Net.WebHeaderCollection.xml", "ref/netstandard1.3/zh-hans/System.Net.WebHeaderCollection.xml", "ref/netstandard1.3/zh-hant/System.Net.WebHeaderCollection.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.Net.WebSockets/4.0.0": {"sha512": "2KJo8hir6Edi9jnMDAMhiJoI691xRBmKcbNpwjrvpIMOCTYOtBpSsSEGBxBDV7PKbasJNaFp1+PZz1D7xS41Hg==", "type": "package", "path": "System.Net.WebSockets/4.0.0", "files": ["System.Net.WebSockets.4.0.0.nupkg.sha512", "System.Net.WebSockets.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Net.WebSockets.dll", "lib/netstandard1.3/System.Net.WebSockets.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Net.WebSockets.dll", "ref/netstandard1.3/System.Net.WebSockets.dll", "ref/netstandard1.3/System.Net.WebSockets.xml", "ref/netstandard1.3/de/System.Net.WebSockets.xml", "ref/netstandard1.3/es/System.Net.WebSockets.xml", "ref/netstandard1.3/fr/System.Net.WebSockets.xml", "ref/netstandard1.3/it/System.Net.WebSockets.xml", "ref/netstandard1.3/ja/System.Net.WebSockets.xml", "ref/netstandard1.3/ko/System.Net.WebSockets.xml", "ref/netstandard1.3/ru/System.Net.WebSockets.xml", "ref/netstandard1.3/zh-hans/System.Net.WebSockets.xml", "ref/netstandard1.3/zh-hant/System.Net.WebSockets.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.Numerics.Vectors/4.1.1": {"sha512": "Ex1NSKycC2wi5XBMWUGWPc3lumh6OQWFFmmpZFZz0oLht5lQ+wWPHVZumOrMJuckfUiVMd4p67BrkBos8lcF+Q==", "type": "package", "path": "System.Numerics.Vectors/4.1.1", "files": ["System.Numerics.Vectors.4.1.1.nupkg.sha512", "System.Numerics.Vectors.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Numerics.Vectors.dll", "lib/net46/System.Numerics.Vectors.xml", "lib/netstandard1.0/System.Numerics.Vectors.dll", "lib/netstandard1.0/System.Numerics.Vectors.xml", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.dll", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Numerics.Vectors.dll", "ref/net46/System.Numerics.Vectors.xml", "ref/netstandard1.0/System.Numerics.Vectors.dll", "ref/netstandard1.0/System.Numerics.Vectors.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.ObjectModel/4.0.12": {"sha512": "tAgJM1xt3ytyMoW4qn4wIqgJYm7L7TShRZG4+Q4Qsi2PCcj96pXN7nRywS9KkB3p/xDUjc2HSwP9SROyPYDYKQ==", "type": "package", "path": "System.ObjectModel/4.0.12", "files": ["System.ObjectModel.4.0.12.nupkg.sha512", "System.ObjectModel.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.ObjectModel.dll", "lib/netstandard1.3/System.ObjectModel.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.ObjectModel.dll", "ref/netcore50/System.ObjectModel.xml", "ref/netcore50/de/System.ObjectModel.xml", "ref/netcore50/es/System.ObjectModel.xml", "ref/netcore50/fr/System.ObjectModel.xml", "ref/netcore50/it/System.ObjectModel.xml", "ref/netcore50/ja/System.ObjectModel.xml", "ref/netcore50/ko/System.ObjectModel.xml", "ref/netcore50/ru/System.ObjectModel.xml", "ref/netcore50/zh-hans/System.ObjectModel.xml", "ref/netcore50/zh-hant/System.ObjectModel.xml", "ref/netstandard1.0/System.ObjectModel.dll", "ref/netstandard1.0/System.ObjectModel.xml", "ref/netstandard1.0/de/System.ObjectModel.xml", "ref/netstandard1.0/es/System.ObjectModel.xml", "ref/netstandard1.0/fr/System.ObjectModel.xml", "ref/netstandard1.0/it/System.ObjectModel.xml", "ref/netstandard1.0/ja/System.ObjectModel.xml", "ref/netstandard1.0/ko/System.ObjectModel.xml", "ref/netstandard1.0/ru/System.ObjectModel.xml", "ref/netstandard1.0/zh-hans/System.ObjectModel.xml", "ref/netstandard1.0/zh-hant/System.ObjectModel.xml", "ref/netstandard1.3/System.ObjectModel.dll", "ref/netstandard1.3/System.ObjectModel.xml", "ref/netstandard1.3/de/System.ObjectModel.xml", "ref/netstandard1.3/es/System.ObjectModel.xml", "ref/netstandard1.3/fr/System.ObjectModel.xml", "ref/netstandard1.3/it/System.ObjectModel.xml", "ref/netstandard1.3/ja/System.ObjectModel.xml", "ref/netstandard1.3/ko/System.ObjectModel.xml", "ref/netstandard1.3/ru/System.ObjectModel.xml", "ref/netstandard1.3/zh-hans/System.ObjectModel.xml", "ref/netstandard1.3/zh-hant/System.ObjectModel.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.Reflection/4.1.0": {"sha512": "JCKANJ0TI7kzoQzuwB/OoJANy1Lg338B6+JVacPl4TpUwi3cReg3nMLplMq2uqYfHFQpKIlHAUVAJlImZz/4ng==", "type": "package", "path": "System.Reflection/4.1.0", "files": ["System.Reflection.4.1.0.nupkg.sha512", "System.Reflection.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Reflection.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Reflection.dll", "ref/netcore50/System.Reflection.dll", "ref/netcore50/System.Reflection.xml", "ref/netcore50/de/System.Reflection.xml", "ref/netcore50/es/System.Reflection.xml", "ref/netcore50/fr/System.Reflection.xml", "ref/netcore50/it/System.Reflection.xml", "ref/netcore50/ja/System.Reflection.xml", "ref/netcore50/ko/System.Reflection.xml", "ref/netcore50/ru/System.Reflection.xml", "ref/netcore50/zh-hans/System.Reflection.xml", "ref/netcore50/zh-hant/System.Reflection.xml", "ref/netstandard1.0/System.Reflection.dll", "ref/netstandard1.0/System.Reflection.xml", "ref/netstandard1.0/de/System.Reflection.xml", "ref/netstandard1.0/es/System.Reflection.xml", "ref/netstandard1.0/fr/System.Reflection.xml", "ref/netstandard1.0/it/System.Reflection.xml", "ref/netstandard1.0/ja/System.Reflection.xml", "ref/netstandard1.0/ko/System.Reflection.xml", "ref/netstandard1.0/ru/System.Reflection.xml", "ref/netstandard1.0/zh-hans/System.Reflection.xml", "ref/netstandard1.0/zh-hant/System.Reflection.xml", "ref/netstandard1.3/System.Reflection.dll", "ref/netstandard1.3/System.Reflection.xml", "ref/netstandard1.3/de/System.Reflection.xml", "ref/netstandard1.3/es/System.Reflection.xml", "ref/netstandard1.3/fr/System.Reflection.xml", "ref/netstandard1.3/it/System.Reflection.xml", "ref/netstandard1.3/ja/System.Reflection.xml", "ref/netstandard1.3/ko/System.Reflection.xml", "ref/netstandard1.3/ru/System.Reflection.xml", "ref/netstandard1.3/zh-hans/System.Reflection.xml", "ref/netstandard1.3/zh-hant/System.Reflection.xml", "ref/netstandard1.5/System.Reflection.dll", "ref/netstandard1.5/System.Reflection.xml", "ref/netstandard1.5/de/System.Reflection.xml", "ref/netstandard1.5/es/System.Reflection.xml", "ref/netstandard1.5/fr/System.Reflection.xml", "ref/netstandard1.5/it/System.Reflection.xml", "ref/netstandard1.5/ja/System.Reflection.xml", "ref/netstandard1.5/ko/System.Reflection.xml", "ref/netstandard1.5/ru/System.Reflection.xml", "ref/netstandard1.5/zh-hans/System.Reflection.xml", "ref/netstandard1.5/zh-hant/System.Reflection.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.Reflection.DispatchProxy/4.0.1": {"sha512": "GPPgWoSxQEU3aCKSOvsAc1dhTTi4iq92PUVEVfnGPGwqCf6synaAJGYLKMs5E3CuRfel8ufACWUijXqDpOlGrA==", "type": "package", "path": "System.Reflection.DispatchProxy/4.0.1", "files": ["System.Reflection.DispatchProxy.4.0.1.nupkg.sha512", "System.Reflection.DispatchProxy.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/netstandard1.3/System.Reflection.DispatchProxy.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/netstandard1.3/System.Reflection.DispatchProxy.dll", "ref/netstandard1.3/System.Reflection.DispatchProxy.xml", "ref/netstandard1.3/de/System.Reflection.DispatchProxy.xml", "ref/netstandard1.3/es/System.Reflection.DispatchProxy.xml", "ref/netstandard1.3/fr/System.Reflection.DispatchProxy.xml", "ref/netstandard1.3/it/System.Reflection.DispatchProxy.xml", "ref/netstandard1.3/ja/System.Reflection.DispatchProxy.xml", "ref/netstandard1.3/ko/System.Reflection.DispatchProxy.xml", "ref/netstandard1.3/ru/System.Reflection.DispatchProxy.xml", "ref/netstandard1.3/zh-hans/System.Reflection.DispatchProxy.xml", "ref/netstandard1.3/zh-hant/System.Reflection.DispatchProxy.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Reflection.DispatchProxy.dll"]}, "System.Reflection.Emit/4.0.1": {"sha512": "P2wqAj72fFjpP6wb9nSfDqNBMab+2ovzSDzUZK7MVIm54tBJEPr9jWfSjjoTpPwj1LeKcmX3vr0ttyjSSFM47g==", "type": "package", "path": "System.Reflection.Emit/4.0.1", "files": ["System.Reflection.Emit.4.0.1.nupkg.sha512", "System.Reflection.Emit.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.dll", "lib/netstandard1.3/System.Reflection.Emit.dll", "lib/xamarinmac20/_._", "ref/MonoAndroid10/_._", "ref/net45/_._", "ref/netstandard1.1/System.Reflection.Emit.dll", "ref/netstandard1.1/System.Reflection.Emit.xml", "ref/netstandard1.1/de/System.Reflection.Emit.xml", "ref/netstandard1.1/es/System.Reflection.Emit.xml", "ref/netstandard1.1/fr/System.Reflection.Emit.xml", "ref/netstandard1.1/it/System.Reflection.Emit.xml", "ref/netstandard1.1/ja/System.Reflection.Emit.xml", "ref/netstandard1.1/ko/System.Reflection.Emit.xml", "ref/netstandard1.1/ru/System.Reflection.Emit.xml", "ref/netstandard1.1/zh-hans/System.Reflection.Emit.xml", "ref/netstandard1.1/zh-hant/System.Reflection.Emit.xml", "ref/xamarinmac20/_._"]}, "System.Reflection.Emit.ILGeneration/4.0.1": {"sha512": "Ov6dU8Bu15Bc7zuqttgHF12J5lwSWyTf1S+FJouUXVMSqImLZzYaQ+vRr1rQ0OZ0HqsrwWl4dsKHELckQkVpgA==", "type": "package", "path": "System.Reflection.Emit.ILGeneration/4.0.1", "files": ["System.Reflection.Emit.ILGeneration.4.0.1.nupkg.sha512", "System.Reflection.Emit.ILGeneration.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.ILGeneration.dll", "lib/netstandard1.3/System.Reflection.Emit.ILGeneration.dll", "lib/portable-net45+wp8/_._", "lib/wp80/_._", "ref/net45/_._", "ref/netstandard1.0/System.Reflection.Emit.ILGeneration.dll", "ref/netstandard1.0/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/de/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/es/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/fr/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/it/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/ja/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/ko/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/ru/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Emit.ILGeneration.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Emit.ILGeneration.xml", "ref/portable-net45+wp8/_._", "ref/wp80/_._", "runtimes/aot/lib/netcore50/_._"]}, "System.Reflection.Emit.Lightweight/4.0.1": {"sha512": "sSzHHXueZ5Uh0OLpUQprhr+ZYJrLPA2Cmr4gn0wj9+FftNKXx8RIMKvO9qnjk2ebPYUjZ+F2ulGdPOsvj+MEjA==", "type": "package", "path": "System.Reflection.Emit.Lightweight/4.0.1", "files": ["System.Reflection.Emit.Lightweight.4.0.1.nupkg.sha512", "System.Reflection.Emit.Lightweight.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/net45/_._", "lib/netcore50/System.Reflection.Emit.Lightweight.dll", "lib/netstandard1.3/System.Reflection.Emit.Lightweight.dll", "lib/portable-net45+wp8/_._", "lib/wp80/_._", "ref/net45/_._", "ref/netstandard1.0/System.Reflection.Emit.Lightweight.dll", "ref/netstandard1.0/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/de/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/es/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/fr/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/it/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ja/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ko/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/ru/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Emit.Lightweight.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Emit.Lightweight.xml", "ref/portable-net45+wp8/_._", "ref/wp80/_._", "runtimes/aot/lib/netcore50/_._"]}, "System.Reflection.Extensions/4.0.1": {"sha512": "GYrtRsZcMuHF3sbmRHfMYpvxZoIN2bQGrYGerUiWLEkqdEUQZhH3TRSaC/oI4wO0II1RKBPlpIa1TOMxIcOOzQ==", "type": "package", "path": "System.Reflection.Extensions/4.0.1", "files": ["System.Reflection.Extensions.4.0.1.nupkg.sha512", "System.Reflection.Extensions.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Reflection.Extensions.dll", "ref/netcore50/System.Reflection.Extensions.xml", "ref/netcore50/de/System.Reflection.Extensions.xml", "ref/netcore50/es/System.Reflection.Extensions.xml", "ref/netcore50/fr/System.Reflection.Extensions.xml", "ref/netcore50/it/System.Reflection.Extensions.xml", "ref/netcore50/ja/System.Reflection.Extensions.xml", "ref/netcore50/ko/System.Reflection.Extensions.xml", "ref/netcore50/ru/System.Reflection.Extensions.xml", "ref/netcore50/zh-hans/System.Reflection.Extensions.xml", "ref/netcore50/zh-hant/System.Reflection.Extensions.xml", "ref/netstandard1.0/System.Reflection.Extensions.dll", "ref/netstandard1.0/System.Reflection.Extensions.xml", "ref/netstandard1.0/de/System.Reflection.Extensions.xml", "ref/netstandard1.0/es/System.Reflection.Extensions.xml", "ref/netstandard1.0/fr/System.Reflection.Extensions.xml", "ref/netstandard1.0/it/System.Reflection.Extensions.xml", "ref/netstandard1.0/ja/System.Reflection.Extensions.xml", "ref/netstandard1.0/ko/System.Reflection.Extensions.xml", "ref/netstandard1.0/ru/System.Reflection.Extensions.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Extensions.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Extensions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.Reflection.Metadata/1.3.0": {"sha512": "jMSCxA4LSyKBGRDm/WtfkO03FkcgRzHxwvQRib1bm2GZ8ifKM1MX1al6breGCEQK280mdl9uQS7JNPXRYk90jw==", "type": "package", "path": "System.Reflection.Metadata/1.3.0", "files": ["System.Reflection.Metadata.1.3.0.nupkg.sha512", "System.Reflection.Metadata.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.1/System.Reflection.Metadata.dll", "lib/netstandard1.1/System.Reflection.Metadata.xml", "lib/portable-net45+win8/System.Reflection.Metadata.dll", "lib/portable-net45+win8/System.Reflection.Metadata.xml"]}, "System.Reflection.Primitives/4.0.1": {"sha512": "4inTox4wTBaDhB7V3mPvp9XlCbeGYWVEM9/fXALd52vNEAVisc1BoVWQPuUuD0Ga//dNbA/WeMy9u9mzLxGTHQ==", "type": "package", "path": "System.Reflection.Primitives/4.0.1", "files": ["System.Reflection.Primitives.4.0.1.nupkg.sha512", "System.Reflection.Primitives.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Reflection.Primitives.dll", "ref/netcore50/System.Reflection.Primitives.xml", "ref/netcore50/de/System.Reflection.Primitives.xml", "ref/netcore50/es/System.Reflection.Primitives.xml", "ref/netcore50/fr/System.Reflection.Primitives.xml", "ref/netcore50/it/System.Reflection.Primitives.xml", "ref/netcore50/ja/System.Reflection.Primitives.xml", "ref/netcore50/ko/System.Reflection.Primitives.xml", "ref/netcore50/ru/System.Reflection.Primitives.xml", "ref/netcore50/zh-hans/System.Reflection.Primitives.xml", "ref/netcore50/zh-hant/System.Reflection.Primitives.xml", "ref/netstandard1.0/System.Reflection.Primitives.dll", "ref/netstandard1.0/System.Reflection.Primitives.xml", "ref/netstandard1.0/de/System.Reflection.Primitives.xml", "ref/netstandard1.0/es/System.Reflection.Primitives.xml", "ref/netstandard1.0/fr/System.Reflection.Primitives.xml", "ref/netstandard1.0/it/System.Reflection.Primitives.xml", "ref/netstandard1.0/ja/System.Reflection.Primitives.xml", "ref/netstandard1.0/ko/System.Reflection.Primitives.xml", "ref/netstandard1.0/ru/System.Reflection.Primitives.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Primitives.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Primitives.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.Reflection.TypeExtensions/4.1.0": {"sha512": "tsQ/ptQ3H5FYfON8lL4MxRk/8kFyE0A+tGPXmVP967cT/gzLHYxIejIYSxp4JmIeFHVP78g/F2FE1mUUTbDtrg==", "type": "package", "path": "System.Reflection.TypeExtensions/4.1.0", "files": ["System.Reflection.TypeExtensions.4.1.0.nupkg.sha512", "System.Reflection.TypeExtensions.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Reflection.TypeExtensions.dll", "lib/net462/System.Reflection.TypeExtensions.dll", "lib/netcore50/System.Reflection.TypeExtensions.dll", "lib/netstandard1.5/System.Reflection.TypeExtensions.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Reflection.TypeExtensions.dll", "ref/net462/System.Reflection.TypeExtensions.dll", "ref/netstandard1.3/System.Reflection.TypeExtensions.dll", "ref/netstandard1.3/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/de/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/es/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/fr/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/it/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/ja/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/ko/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/ru/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/zh-hans/System.Reflection.TypeExtensions.xml", "ref/netstandard1.3/zh-hant/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/System.Reflection.TypeExtensions.dll", "ref/netstandard1.5/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/de/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/es/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/fr/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/it/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/ja/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/ko/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/ru/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/zh-hans/System.Reflection.TypeExtensions.xml", "ref/netstandard1.5/zh-hant/System.Reflection.TypeExtensions.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Reflection.TypeExtensions.dll"]}, "System.Resources.Reader/4.0.0": {"sha512": "VX1iHAoHxgrLZv+nq/9drCZI6Q4SSCzSVyUm1e0U60sqWdj6XhY7wvKmy3RvsSal9h+/vqSWwxxJsm0J4vn/jA==", "type": "package", "path": "System.Resources.Reader/4.0.0", "files": ["System.Resources.Reader.4.0.0.nupkg.sha512", "System.Resources.Reader.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/System.Resources.Reader.dll"]}, "System.Resources.ResourceManager/4.0.1": {"sha512": "TxwVeUNoTgUOdQ09gfTjvW411MF+w9MBYL7AtNVc+HtBCFlutPLhUCdZjNkjbhj3bNQWMdHboF0KIWEOjJssbA==", "type": "package", "path": "System.Resources.ResourceManager/4.0.1", "files": ["System.Resources.ResourceManager.4.0.1.nupkg.sha512", "System.Resources.ResourceManager.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Resources.ResourceManager.dll", "ref/netcore50/System.Resources.ResourceManager.xml", "ref/netcore50/de/System.Resources.ResourceManager.xml", "ref/netcore50/es/System.Resources.ResourceManager.xml", "ref/netcore50/fr/System.Resources.ResourceManager.xml", "ref/netcore50/it/System.Resources.ResourceManager.xml", "ref/netcore50/ja/System.Resources.ResourceManager.xml", "ref/netcore50/ko/System.Resources.ResourceManager.xml", "ref/netcore50/ru/System.Resources.ResourceManager.xml", "ref/netcore50/zh-hans/System.Resources.ResourceManager.xml", "ref/netcore50/zh-hant/System.Resources.ResourceManager.xml", "ref/netstandard1.0/System.Resources.ResourceManager.dll", "ref/netstandard1.0/System.Resources.ResourceManager.xml", "ref/netstandard1.0/de/System.Resources.ResourceManager.xml", "ref/netstandard1.0/es/System.Resources.ResourceManager.xml", "ref/netstandard1.0/fr/System.Resources.ResourceManager.xml", "ref/netstandard1.0/it/System.Resources.ResourceManager.xml", "ref/netstandard1.0/ja/System.Resources.ResourceManager.xml", "ref/netstandard1.0/ko/System.Resources.ResourceManager.xml", "ref/netstandard1.0/ru/System.Resources.ResourceManager.xml", "ref/netstandard1.0/zh-hans/System.Resources.ResourceManager.xml", "ref/netstandard1.0/zh-hant/System.Resources.ResourceManager.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.Runtime/4.1.0": {"sha512": "v6c/4Yaa9uWsq+JMhnOFewrYkgdNHNG2eMKuNqRn8P733rNXeRCGvV5FkkjBXn2dbVkPXOsO0xjsEeM1q2zC0g==", "type": "package", "path": "System.Runtime/4.1.0", "files": ["System.Runtime.4.1.0.nupkg.sha512", "System.Runtime.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.dll", "lib/portable-net45+win8+wp80+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.dll", "ref/netcore50/System.Runtime.dll", "ref/netcore50/System.Runtime.xml", "ref/netcore50/de/System.Runtime.xml", "ref/netcore50/es/System.Runtime.xml", "ref/netcore50/fr/System.Runtime.xml", "ref/netcore50/it/System.Runtime.xml", "ref/netcore50/ja/System.Runtime.xml", "ref/netcore50/ko/System.Runtime.xml", "ref/netcore50/ru/System.Runtime.xml", "ref/netcore50/zh-hans/System.Runtime.xml", "ref/netcore50/zh-hant/System.Runtime.xml", "ref/netstandard1.0/System.Runtime.dll", "ref/netstandard1.0/System.Runtime.xml", "ref/netstandard1.0/de/System.Runtime.xml", "ref/netstandard1.0/es/System.Runtime.xml", "ref/netstandard1.0/fr/System.Runtime.xml", "ref/netstandard1.0/it/System.Runtime.xml", "ref/netstandard1.0/ja/System.Runtime.xml", "ref/netstandard1.0/ko/System.Runtime.xml", "ref/netstandard1.0/ru/System.Runtime.xml", "ref/netstandard1.0/zh-hans/System.Runtime.xml", "ref/netstandard1.0/zh-hant/System.Runtime.xml", "ref/netstandard1.2/System.Runtime.dll", "ref/netstandard1.2/System.Runtime.xml", "ref/netstandard1.2/de/System.Runtime.xml", "ref/netstandard1.2/es/System.Runtime.xml", "ref/netstandard1.2/fr/System.Runtime.xml", "ref/netstandard1.2/it/System.Runtime.xml", "ref/netstandard1.2/ja/System.Runtime.xml", "ref/netstandard1.2/ko/System.Runtime.xml", "ref/netstandard1.2/ru/System.Runtime.xml", "ref/netstandard1.2/zh-hans/System.Runtime.xml", "ref/netstandard1.2/zh-hant/System.Runtime.xml", "ref/netstandard1.3/System.Runtime.dll", "ref/netstandard1.3/System.Runtime.xml", "ref/netstandard1.3/de/System.Runtime.xml", "ref/netstandard1.3/es/System.Runtime.xml", "ref/netstandard1.3/fr/System.Runtime.xml", "ref/netstandard1.3/it/System.Runtime.xml", "ref/netstandard1.3/ja/System.Runtime.xml", "ref/netstandard1.3/ko/System.Runtime.xml", "ref/netstandard1.3/ru/System.Runtime.xml", "ref/netstandard1.3/zh-hans/System.Runtime.xml", "ref/netstandard1.3/zh-hant/System.Runtime.xml", "ref/netstandard1.5/System.Runtime.dll", "ref/netstandard1.5/System.Runtime.xml", "ref/netstandard1.5/de/System.Runtime.xml", "ref/netstandard1.5/es/System.Runtime.xml", "ref/netstandard1.5/fr/System.Runtime.xml", "ref/netstandard1.5/it/System.Runtime.xml", "ref/netstandard1.5/ja/System.Runtime.xml", "ref/netstandard1.5/ko/System.Runtime.xml", "ref/netstandard1.5/ru/System.Runtime.xml", "ref/netstandard1.5/zh-hans/System.Runtime.xml", "ref/netstandard1.5/zh-hant/System.Runtime.xml", "ref/portable-net45+win8+wp80+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.Runtime.Extensions/4.1.0": {"sha512": "CUOHjTT/vgP0qGW22U4/hDlOqXmcPq5YicBaXdUR2UiUoLwBT+olO6we4DVbq57jeX5uXH2uerVZhf0qGj+sVQ==", "type": "package", "path": "System.Runtime.Extensions/4.1.0", "files": ["System.Runtime.Extensions.4.1.0.nupkg.sha512", "System.Runtime.Extensions.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.Extensions.dll", "ref/netcore50/System.Runtime.Extensions.dll", "ref/netcore50/System.Runtime.Extensions.xml", "ref/netcore50/de/System.Runtime.Extensions.xml", "ref/netcore50/es/System.Runtime.Extensions.xml", "ref/netcore50/fr/System.Runtime.Extensions.xml", "ref/netcore50/it/System.Runtime.Extensions.xml", "ref/netcore50/ja/System.Runtime.Extensions.xml", "ref/netcore50/ko/System.Runtime.Extensions.xml", "ref/netcore50/ru/System.Runtime.Extensions.xml", "ref/netcore50/zh-hans/System.Runtime.Extensions.xml", "ref/netcore50/zh-hant/System.Runtime.Extensions.xml", "ref/netstandard1.0/System.Runtime.Extensions.dll", "ref/netstandard1.0/System.Runtime.Extensions.xml", "ref/netstandard1.0/de/System.Runtime.Extensions.xml", "ref/netstandard1.0/es/System.Runtime.Extensions.xml", "ref/netstandard1.0/fr/System.Runtime.Extensions.xml", "ref/netstandard1.0/it/System.Runtime.Extensions.xml", "ref/netstandard1.0/ja/System.Runtime.Extensions.xml", "ref/netstandard1.0/ko/System.Runtime.Extensions.xml", "ref/netstandard1.0/ru/System.Runtime.Extensions.xml", "ref/netstandard1.0/zh-hans/System.Runtime.Extensions.xml", "ref/netstandard1.0/zh-hant/System.Runtime.Extensions.xml", "ref/netstandard1.3/System.Runtime.Extensions.dll", "ref/netstandard1.3/System.Runtime.Extensions.xml", "ref/netstandard1.3/de/System.Runtime.Extensions.xml", "ref/netstandard1.3/es/System.Runtime.Extensions.xml", "ref/netstandard1.3/fr/System.Runtime.Extensions.xml", "ref/netstandard1.3/it/System.Runtime.Extensions.xml", "ref/netstandard1.3/ja/System.Runtime.Extensions.xml", "ref/netstandard1.3/ko/System.Runtime.Extensions.xml", "ref/netstandard1.3/ru/System.Runtime.Extensions.xml", "ref/netstandard1.3/zh-hans/System.Runtime.Extensions.xml", "ref/netstandard1.3/zh-hant/System.Runtime.Extensions.xml", "ref/netstandard1.5/System.Runtime.Extensions.dll", "ref/netstandard1.5/System.Runtime.Extensions.xml", "ref/netstandard1.5/de/System.Runtime.Extensions.xml", "ref/netstandard1.5/es/System.Runtime.Extensions.xml", "ref/netstandard1.5/fr/System.Runtime.Extensions.xml", "ref/netstandard1.5/it/System.Runtime.Extensions.xml", "ref/netstandard1.5/ja/System.Runtime.Extensions.xml", "ref/netstandard1.5/ko/System.Runtime.Extensions.xml", "ref/netstandard1.5/ru/System.Runtime.Extensions.xml", "ref/netstandard1.5/zh-hans/System.Runtime.Extensions.xml", "ref/netstandard1.5/zh-hant/System.Runtime.Extensions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.Runtime.Handles/4.0.1": {"sha512": "nCJvEKguXEvk2ymk1gqj625vVnlK3/xdGzx0vOKicQkoquaTBJTP13AIYkocSUwHCLNBwUbXTqTWGDxBTWpt7g==", "type": "package", "path": "System.Runtime.Handles/4.0.1", "files": ["System.Runtime.Handles.4.0.1.nupkg.sha512", "System.Runtime.Handles.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/_._", "ref/netstandard1.3/System.Runtime.Handles.dll", "ref/netstandard1.3/System.Runtime.Handles.xml", "ref/netstandard1.3/de/System.Runtime.Handles.xml", "ref/netstandard1.3/es/System.Runtime.Handles.xml", "ref/netstandard1.3/fr/System.Runtime.Handles.xml", "ref/netstandard1.3/it/System.Runtime.Handles.xml", "ref/netstandard1.3/ja/System.Runtime.Handles.xml", "ref/netstandard1.3/ko/System.Runtime.Handles.xml", "ref/netstandard1.3/ru/System.Runtime.Handles.xml", "ref/netstandard1.3/zh-hans/System.Runtime.Handles.xml", "ref/netstandard1.3/zh-hant/System.Runtime.Handles.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.Runtime.InteropServices/4.1.0": {"sha512": "16eu3kjHS633yYdkjwShDHZLRNMKVi/s0bY8ODiqJ2RfMhDMAwxZaUaWVnZ2P71kr/or+X9o/xFWtNqz8ivieQ==", "type": "package", "path": "System.Runtime.InteropServices/4.1.0", "files": ["System.Runtime.InteropServices.4.1.0.nupkg.sha512", "System.Runtime.InteropServices.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.InteropServices.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.InteropServices.dll", "ref/netcore50/System.Runtime.InteropServices.dll", "ref/netcore50/System.Runtime.InteropServices.xml", "ref/netcore50/de/System.Runtime.InteropServices.xml", "ref/netcore50/es/System.Runtime.InteropServices.xml", "ref/netcore50/fr/System.Runtime.InteropServices.xml", "ref/netcore50/it/System.Runtime.InteropServices.xml", "ref/netcore50/ja/System.Runtime.InteropServices.xml", "ref/netcore50/ko/System.Runtime.InteropServices.xml", "ref/netcore50/ru/System.Runtime.InteropServices.xml", "ref/netcore50/zh-hans/System.Runtime.InteropServices.xml", "ref/netcore50/zh-hant/System.Runtime.InteropServices.xml", "ref/netstandard1.1/System.Runtime.InteropServices.dll", "ref/netstandard1.1/System.Runtime.InteropServices.xml", "ref/netstandard1.1/de/System.Runtime.InteropServices.xml", "ref/netstandard1.1/es/System.Runtime.InteropServices.xml", "ref/netstandard1.1/fr/System.Runtime.InteropServices.xml", "ref/netstandard1.1/it/System.Runtime.InteropServices.xml", "ref/netstandard1.1/ja/System.Runtime.InteropServices.xml", "ref/netstandard1.1/ko/System.Runtime.InteropServices.xml", "ref/netstandard1.1/ru/System.Runtime.InteropServices.xml", "ref/netstandard1.1/zh-hans/System.Runtime.InteropServices.xml", "ref/netstandard1.1/zh-hant/System.Runtime.InteropServices.xml", "ref/netstandard1.2/System.Runtime.InteropServices.dll", "ref/netstandard1.2/System.Runtime.InteropServices.xml", "ref/netstandard1.2/de/System.Runtime.InteropServices.xml", "ref/netstandard1.2/es/System.Runtime.InteropServices.xml", "ref/netstandard1.2/fr/System.Runtime.InteropServices.xml", "ref/netstandard1.2/it/System.Runtime.InteropServices.xml", "ref/netstandard1.2/ja/System.Runtime.InteropServices.xml", "ref/netstandard1.2/ko/System.Runtime.InteropServices.xml", "ref/netstandard1.2/ru/System.Runtime.InteropServices.xml", "ref/netstandard1.2/zh-hans/System.Runtime.InteropServices.xml", "ref/netstandard1.2/zh-hant/System.Runtime.InteropServices.xml", "ref/netstandard1.3/System.Runtime.InteropServices.dll", "ref/netstandard1.3/System.Runtime.InteropServices.xml", "ref/netstandard1.3/de/System.Runtime.InteropServices.xml", "ref/netstandard1.3/es/System.Runtime.InteropServices.xml", "ref/netstandard1.3/fr/System.Runtime.InteropServices.xml", "ref/netstandard1.3/it/System.Runtime.InteropServices.xml", "ref/netstandard1.3/ja/System.Runtime.InteropServices.xml", "ref/netstandard1.3/ko/System.Runtime.InteropServices.xml", "ref/netstandard1.3/ru/System.Runtime.InteropServices.xml", "ref/netstandard1.3/zh-hans/System.Runtime.InteropServices.xml", "ref/netstandard1.3/zh-hant/System.Runtime.InteropServices.xml", "ref/netstandard1.5/System.Runtime.InteropServices.dll", "ref/netstandard1.5/System.Runtime.InteropServices.xml", "ref/netstandard1.5/de/System.Runtime.InteropServices.xml", "ref/netstandard1.5/es/System.Runtime.InteropServices.xml", "ref/netstandard1.5/fr/System.Runtime.InteropServices.xml", "ref/netstandard1.5/it/System.Runtime.InteropServices.xml", "ref/netstandard1.5/ja/System.Runtime.InteropServices.xml", "ref/netstandard1.5/ko/System.Runtime.InteropServices.xml", "ref/netstandard1.5/ru/System.Runtime.InteropServices.xml", "ref/netstandard1.5/zh-hans/System.Runtime.InteropServices.xml", "ref/netstandard1.5/zh-hant/System.Runtime.InteropServices.xml", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.Runtime.InteropServices.RuntimeInformation/4.0.0": {"sha512": "hWPhJxc453RCa8Z29O91EmfGeZIHX1ZH2A8L6lYQVSaKzku2DfArSfMEb1/MYYzPQRJZeu0c9dmYeJKxW5Fgng==", "type": "package", "path": "System.Runtime.InteropServices.RuntimeInformation/4.0.0", "files": ["System.Runtime.InteropServices.RuntimeInformation.4.0.0.nupkg.sha512", "System.Runtime.InteropServices.RuntimeInformation.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/System.Runtime.InteropServices.RuntimeInformation.dll", "lib/win8/System.Runtime.InteropServices.RuntimeInformation.dll", "lib/wpa81/System.Runtime.InteropServices.RuntimeInformation.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Runtime.InteropServices.RuntimeInformation.dll", "runtimes/unix/lib/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll", "runtimes/win/lib/net45/System.Runtime.InteropServices.RuntimeInformation.dll", "runtimes/win/lib/netcore50/System.Runtime.InteropServices.RuntimeInformation.dll", "runtimes/win/lib/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll"]}, "System.Runtime.Loader/4.0.0": {"sha512": "4UN78GOVU/mbDFcXkEWtetJT/sJ0yic2gGk1HSlSpWI0TDf421xnrZTDZnwNBapk1GQeYN7U1lTj/aQB1by6ow==", "type": "package", "path": "System.Runtime.Loader/4.0.0", "files": ["System.Runtime.Loader.4.0.0.nupkg.sha512", "System.Runtime.Loader.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/net462/_._", "lib/netstandard1.5/System.Runtime.Loader.dll", "ref/netstandard1.5/System.Runtime.Loader.dll", "ref/netstandard1.5/System.Runtime.Loader.xml", "ref/netstandard1.5/de/System.Runtime.Loader.xml", "ref/netstandard1.5/es/System.Runtime.Loader.xml", "ref/netstandard1.5/fr/System.Runtime.Loader.xml", "ref/netstandard1.5/it/System.Runtime.Loader.xml", "ref/netstandard1.5/ja/System.Runtime.Loader.xml", "ref/netstandard1.5/ko/System.Runtime.Loader.xml", "ref/netstandard1.5/ru/System.Runtime.Loader.xml", "ref/netstandard1.5/zh-hans/System.Runtime.Loader.xml", "ref/netstandard1.5/zh-hant/System.Runtime.Loader.xml"]}, "System.Runtime.Numerics/4.0.1": {"sha512": "+XbKFuzdmLP3d1o9pdHu2nxjNr2OEPqGzKeegPLCUMM71a0t50A/rOcIRmGs9wR7a8KuHX6hYs/7/TymIGLNqg==", "type": "package", "path": "System.Runtime.Numerics/4.0.1", "files": ["System.Runtime.Numerics.4.0.1.nupkg.sha512", "System.Runtime.Numerics.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Runtime.Numerics.dll", "lib/netstandard1.3/System.Runtime.Numerics.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Runtime.Numerics.dll", "ref/netcore50/System.Runtime.Numerics.xml", "ref/netcore50/de/System.Runtime.Numerics.xml", "ref/netcore50/es/System.Runtime.Numerics.xml", "ref/netcore50/fr/System.Runtime.Numerics.xml", "ref/netcore50/it/System.Runtime.Numerics.xml", "ref/netcore50/ja/System.Runtime.Numerics.xml", "ref/netcore50/ko/System.Runtime.Numerics.xml", "ref/netcore50/ru/System.Runtime.Numerics.xml", "ref/netcore50/zh-hans/System.Runtime.Numerics.xml", "ref/netcore50/zh-hant/System.Runtime.Numerics.xml", "ref/netstandard1.1/System.Runtime.Numerics.dll", "ref/netstandard1.1/System.Runtime.Numerics.xml", "ref/netstandard1.1/de/System.Runtime.Numerics.xml", "ref/netstandard1.1/es/System.Runtime.Numerics.xml", "ref/netstandard1.1/fr/System.Runtime.Numerics.xml", "ref/netstandard1.1/it/System.Runtime.Numerics.xml", "ref/netstandard1.1/ja/System.Runtime.Numerics.xml", "ref/netstandard1.1/ko/System.Runtime.Numerics.xml", "ref/netstandard1.1/ru/System.Runtime.Numerics.xml", "ref/netstandard1.1/zh-hans/System.Runtime.Numerics.xml", "ref/netstandard1.1/zh-hant/System.Runtime.Numerics.xml", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.Runtime.Serialization.Primitives/4.1.1": {"sha512": "HZ6Du5QrTG8MNJbf4e4qMO3JRAkIboGT5Fk804uZtg3Gq516S7hAqTm2UZKUHa7/6HUGdVy3AqMQKbns06G/cg==", "type": "package", "path": "System.Runtime.Serialization.Primitives/4.1.1", "files": ["System.Runtime.Serialization.Primitives.4.1.1.nupkg.sha512", "System.Runtime.Serialization.Primitives.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net46/System.Runtime.Serialization.Primitives.dll", "lib/netcore50/System.Runtime.Serialization.Primitives.dll", "lib/netstandard1.3/System.Runtime.Serialization.Primitives.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net46/System.Runtime.Serialization.Primitives.dll", "ref/netcore50/System.Runtime.Serialization.Primitives.dll", "ref/netcore50/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/de/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/es/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/fr/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/it/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/ja/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/ko/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/ru/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/zh-hans/System.Runtime.Serialization.Primitives.xml", "ref/netcore50/zh-hant/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/System.Runtime.Serialization.Primitives.dll", "ref/netstandard1.0/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/de/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/es/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/fr/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/it/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/ja/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/ko/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/ru/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/zh-hans/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.0/zh-hant/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/System.Runtime.Serialization.Primitives.dll", "ref/netstandard1.3/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/de/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/es/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/fr/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/it/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/ja/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/ko/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/ru/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/zh-hans/System.Runtime.Serialization.Primitives.xml", "ref/netstandard1.3/zh-hant/System.Runtime.Serialization.Primitives.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Runtime.Serialization.Primitives.dll"]}, "System.Security.Claims/4.0.1": {"sha512": "4Jlp0OgJLS/Voj1kyFP6MJlIYp3crgfH8kNQk2p7+4JYfc1aAmh9PZyAMMbDhuoolGNtux9HqSOazsioRiDvCw==", "type": "package", "path": "System.Security.Claims/4.0.1", "files": ["System.Security.Claims.4.0.1.nupkg.sha512", "System.Security.Claims.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Claims.dll", "lib/netstandard1.3/System.Security.Claims.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Claims.dll", "ref/netstandard1.3/System.Security.Claims.dll", "ref/netstandard1.3/System.Security.Claims.xml", "ref/netstandard1.3/de/System.Security.Claims.xml", "ref/netstandard1.3/es/System.Security.Claims.xml", "ref/netstandard1.3/fr/System.Security.Claims.xml", "ref/netstandard1.3/it/System.Security.Claims.xml", "ref/netstandard1.3/ja/System.Security.Claims.xml", "ref/netstandard1.3/ko/System.Security.Claims.xml", "ref/netstandard1.3/ru/System.Security.Claims.xml", "ref/netstandard1.3/zh-hans/System.Security.Claims.xml", "ref/netstandard1.3/zh-hant/System.Security.Claims.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.Security.Cryptography.Algorithms/4.2.0": {"sha512": "8JQFxbLVdrtIOKMDN38Fn0GWnqYZw/oMlwOUG/qz1jqChvyZlnUmu+0s7wLx7JYua/nAXoESpHA3iw11QFWhXg==", "type": "package", "path": "System.Security.Cryptography.Algorithms/4.2.0", "files": ["System.Security.Cryptography.Algorithms.4.2.0.nupkg.sha512", "System.Security.Cryptography.Algorithms.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.Algorithms.dll", "lib/net461/System.Security.Cryptography.Algorithms.dll", "lib/net463/System.Security.Cryptography.Algorithms.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.Algorithms.dll", "ref/net461/System.Security.Cryptography.Algorithms.dll", "ref/net463/System.Security.Cryptography.Algorithms.dll", "ref/netstandard1.3/System.Security.Cryptography.Algorithms.dll", "ref/netstandard1.4/System.Security.Cryptography.Algorithms.dll", "ref/netstandard1.6/System.Security.Cryptography.Algorithms.dll", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.6/System.Security.Cryptography.Algorithms.dll", "runtimes/win/lib/net46/System.Security.Cryptography.Algorithms.dll", "runtimes/win/lib/net461/System.Security.Cryptography.Algorithms.dll", "runtimes/win/lib/net463/System.Security.Cryptography.Algorithms.dll", "runtimes/win/lib/netcore50/System.Security.Cryptography.Algorithms.dll", "runtimes/win/lib/netstandard1.6/System.Security.Cryptography.Algorithms.dll"]}, "System.Security.Cryptography.Cng/4.2.0": {"sha512": "cUJ2h+ZvONDe28Szw3st5dOHdjndhJzQ2WObDEXAWRPEQBtVItVoxbXM/OEsTthl3cNn2dk2k0I3y45igCQcLw==", "type": "package", "path": "System.Security.Cryptography.Cng/4.2.0", "files": ["System.Security.Cryptography.Cng.4.2.0.nupkg.sha512", "System.Security.Cryptography.Cng.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/net46/System.Security.Cryptography.Cng.dll", "lib/net461/System.Security.Cryptography.Cng.dll", "lib/net463/System.Security.Cryptography.Cng.dll", "ref/net46/System.Security.Cryptography.Cng.dll", "ref/net461/System.Security.Cryptography.Cng.dll", "ref/net463/System.Security.Cryptography.Cng.dll", "ref/netstandard1.3/System.Security.Cryptography.Cng.dll", "ref/netstandard1.4/System.Security.Cryptography.Cng.dll", "ref/netstandard1.6/System.Security.Cryptography.Cng.dll", "runtimes/unix/lib/netstandard1.6/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net46/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net461/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/net463/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netstandard1.4/System.Security.Cryptography.Cng.dll", "runtimes/win/lib/netstandard1.6/System.Security.Cryptography.Cng.dll"]}, "System.Security.Cryptography.Csp/4.0.0": {"sha512": "/i1Usuo4PgAqgbPNC0NjbO3jPW//BoBlTpcWFD1EHVbidH21y4c1ap5bbEMSGAXjAShhMH4abi/K8fILrnu4BQ==", "type": "package", "path": "System.Security.Cryptography.Csp/4.0.0", "files": ["System.Security.Cryptography.Csp.4.0.0.nupkg.sha512", "System.Security.Cryptography.Csp.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.Csp.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.Csp.dll", "ref/netstandard1.3/System.Security.Cryptography.Csp.dll", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.3/System.Security.Cryptography.Csp.dll", "runtimes/win/lib/net46/System.Security.Cryptography.Csp.dll", "runtimes/win/lib/netcore50/_._", "runtimes/win/lib/netstandard1.3/System.Security.Cryptography.Csp.dll"]}, "System.Security.Cryptography.Encoding/4.0.0": {"sha512": "FbKgE5MbxSQMPcSVRgwM6bXN3GtyAh04NkV8E5zKCBE26X0vYW0UtTa2FIgkH33WVqBVxRgxljlVYumWtU+HcQ==", "type": "package", "path": "System.Security.Cryptography.Encoding/4.0.0", "files": ["System.Security.Cryptography.Encoding.4.0.0.nupkg.sha512", "System.Security.Cryptography.Encoding.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.Encoding.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.Encoding.dll", "ref/netstandard1.3/System.Security.Cryptography.Encoding.dll", "ref/netstandard1.3/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/de/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/es/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/fr/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/it/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/ja/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/ko/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/ru/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/zh-hans/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/zh-hant/System.Security.Cryptography.Encoding.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.3/System.Security.Cryptography.Encoding.dll", "runtimes/win/lib/net46/System.Security.Cryptography.Encoding.dll", "runtimes/win/lib/netstandard1.3/System.Security.Cryptography.Encoding.dll"]}, "System.Security.Cryptography.OpenSsl/4.0.0": {"sha512": "HUG/zNUJwEiLkoURDixzkzZdB5yGA5pQhDP93ArOpDPQMteURIGERRNzzoJlmTreLBWr5lkFSjjMSk8ySEpQMw==", "type": "package", "path": "System.Security.Cryptography.OpenSsl/4.0.0", "files": ["System.Security.Cryptography.OpenSsl.4.0.0.nupkg.sha512", "System.Security.Cryptography.OpenSsl.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.6/System.Security.Cryptography.OpenSsl.dll", "ref/netstandard1.6/System.Security.Cryptography.OpenSsl.dll", "runtimes/unix/lib/netstandard1.6/System.Security.Cryptography.OpenSsl.dll"]}, "System.Security.Cryptography.Primitives/4.0.0": {"sha512": "Wkd7QryWYjkQclX0bngpntW5HSlMzeJU24UaLJQ7YTfI8ydAVAaU2J+HXLLABOVJlKTVvAeL0Aj39VeTe7L+oA==", "type": "package", "path": "System.Security.Cryptography.Primitives/4.0.0", "files": ["System.Security.Cryptography.Primitives.4.0.0.nupkg.sha512", "System.Security.Cryptography.Primitives.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.Primitives.dll", "lib/netstandard1.3/System.Security.Cryptography.Primitives.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.Primitives.dll", "ref/netstandard1.3/System.Security.Cryptography.Primitives.dll", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.Security.Cryptography.X509Certificates/4.1.0": {"sha512": "4HEfsQIKAhA1+ApNn729Gi09zh+lYWwyIuViihoMDWp1vQnEkL2ct7mAbhBlLYm+x/L4Rr/pyGge1lIY635e0w==", "type": "package", "path": "System.Security.Cryptography.X509Certificates/4.1.0", "files": ["System.Security.Cryptography.X509Certificates.4.1.0.nupkg.sha512", "System.Security.Cryptography.X509Certificates.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.X509Certificates.dll", "lib/net461/System.Security.Cryptography.X509Certificates.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.X509Certificates.dll", "ref/net461/System.Security.Cryptography.X509Certificates.dll", "ref/netstandard1.3/System.Security.Cryptography.X509Certificates.dll", "ref/netstandard1.3/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/de/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/es/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/fr/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/it/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/ja/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/ko/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/ru/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/zh-hans/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/zh-hant/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/System.Security.Cryptography.X509Certificates.dll", "ref/netstandard1.4/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/de/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/es/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/fr/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/it/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/ja/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/ko/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/ru/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/zh-hans/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/zh-hant/System.Security.Cryptography.X509Certificates.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.6/System.Security.Cryptography.X509Certificates.dll", "runtimes/win/lib/net46/System.Security.Cryptography.X509Certificates.dll", "runtimes/win/lib/net461/System.Security.Cryptography.X509Certificates.dll", "runtimes/win/lib/netcore50/System.Security.Cryptography.X509Certificates.dll", "runtimes/win/lib/netstandard1.6/System.Security.Cryptography.X509Certificates.dll"]}, "System.Security.Principal/4.0.1": {"sha512": "On+SKhXY5rzxh/S8wlH1Rm0ogBlu7zyHNxeNBiXauNrhHRXAe9EuX8Yl5IOzLPGU5Z4kLWHMvORDOCG8iu9hww==", "type": "package", "path": "System.Security.Principal/4.0.1", "files": ["System.Security.Principal.4.0.1.nupkg.sha512", "System.Security.Principal.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Security.Principal.dll", "lib/netstandard1.0/System.Security.Principal.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Security.Principal.dll", "ref/netcore50/System.Security.Principal.xml", "ref/netcore50/de/System.Security.Principal.xml", "ref/netcore50/es/System.Security.Principal.xml", "ref/netcore50/fr/System.Security.Principal.xml", "ref/netcore50/it/System.Security.Principal.xml", "ref/netcore50/ja/System.Security.Principal.xml", "ref/netcore50/ko/System.Security.Principal.xml", "ref/netcore50/ru/System.Security.Principal.xml", "ref/netcore50/zh-hans/System.Security.Principal.xml", "ref/netcore50/zh-hant/System.Security.Principal.xml", "ref/netstandard1.0/System.Security.Principal.dll", "ref/netstandard1.0/System.Security.Principal.xml", "ref/netstandard1.0/de/System.Security.Principal.xml", "ref/netstandard1.0/es/System.Security.Principal.xml", "ref/netstandard1.0/fr/System.Security.Principal.xml", "ref/netstandard1.0/it/System.Security.Principal.xml", "ref/netstandard1.0/ja/System.Security.Principal.xml", "ref/netstandard1.0/ko/System.Security.Principal.xml", "ref/netstandard1.0/ru/System.Security.Principal.xml", "ref/netstandard1.0/zh-hans/System.Security.Principal.xml", "ref/netstandard1.0/zh-hant/System.Security.Principal.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.Security.Principal.Windows/4.0.0": {"sha512": "iFx15AF3RMEPZn3COh8+Bb2Thv2zsmLd93RchS1b8Mj5SNYeGqbYNCSn5AES1+gq56p4ujGZPrl0xN7ngkXOHg==", "type": "package", "path": "System.Security.Principal.Windows/4.0.0", "files": ["System.Security.Principal.Windows.4.0.0.nupkg.sha512", "System.Security.Principal.Windows.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/net46/System.Security.Principal.Windows.dll", "ref/net46/System.Security.Principal.Windows.dll", "ref/netstandard1.3/System.Security.Principal.Windows.dll", "ref/netstandard1.3/System.Security.Principal.Windows.xml", "ref/netstandard1.3/de/System.Security.Principal.Windows.xml", "ref/netstandard1.3/es/System.Security.Principal.Windows.xml", "ref/netstandard1.3/fr/System.Security.Principal.Windows.xml", "ref/netstandard1.3/it/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ja/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ko/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ru/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hans/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hant/System.Security.Principal.Windows.xml", "runtimes/unix/lib/netstandard1.3/System.Security.Principal.Windows.dll", "runtimes/win/lib/net46/System.Security.Principal.Windows.dll", "runtimes/win/lib/netstandard1.3/System.Security.Principal.Windows.dll"]}, "System.Text.Encoding/4.0.11": {"sha512": "U3gGeMlDZXxCEiY4DwVLSacg+DFWCvoiX+JThA/rvw37Sqrku7sEFeVBBBMBnfB6FeZHsyDx85HlKL19x0HtZA==", "type": "package", "path": "System.Text.Encoding/4.0.11", "files": ["System.Text.Encoding.4.0.11.nupkg.sha512", "System.Text.Encoding.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Text.Encoding.dll", "ref/netcore50/System.Text.Encoding.xml", "ref/netcore50/de/System.Text.Encoding.xml", "ref/netcore50/es/System.Text.Encoding.xml", "ref/netcore50/fr/System.Text.Encoding.xml", "ref/netcore50/it/System.Text.Encoding.xml", "ref/netcore50/ja/System.Text.Encoding.xml", "ref/netcore50/ko/System.Text.Encoding.xml", "ref/netcore50/ru/System.Text.Encoding.xml", "ref/netcore50/zh-hans/System.Text.Encoding.xml", "ref/netcore50/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.0/System.Text.Encoding.dll", "ref/netstandard1.0/System.Text.Encoding.xml", "ref/netstandard1.0/de/System.Text.Encoding.xml", "ref/netstandard1.0/es/System.Text.Encoding.xml", "ref/netstandard1.0/fr/System.Text.Encoding.xml", "ref/netstandard1.0/it/System.Text.Encoding.xml", "ref/netstandard1.0/ja/System.Text.Encoding.xml", "ref/netstandard1.0/ko/System.Text.Encoding.xml", "ref/netstandard1.0/ru/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.3/System.Text.Encoding.dll", "ref/netstandard1.3/System.Text.Encoding.xml", "ref/netstandard1.3/de/System.Text.Encoding.xml", "ref/netstandard1.3/es/System.Text.Encoding.xml", "ref/netstandard1.3/fr/System.Text.Encoding.xml", "ref/netstandard1.3/it/System.Text.Encoding.xml", "ref/netstandard1.3/ja/System.Text.Encoding.xml", "ref/netstandard1.3/ko/System.Text.Encoding.xml", "ref/netstandard1.3/ru/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hant/System.Text.Encoding.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.Text.Encoding.CodePages/4.0.1": {"sha512": "h4z6rrA/hxWf4655D18IIZ0eaLRa3tQC/j+e26W+VinIHY0l07iEXaAvO0YSYq3MvCjMYy8Zs5AdC1sxNQOB7Q==", "type": "package", "path": "System.Text.Encoding.CodePages/4.0.1", "files": ["System.Text.Encoding.CodePages.4.0.1.nupkg.sha512", "System.Text.Encoding.CodePages.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Text.Encoding.CodePages.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/netstandard1.3/System.Text.Encoding.CodePages.dll", "ref/netstandard1.3/System.Text.Encoding.CodePages.xml", "ref/netstandard1.3/de/System.Text.Encoding.CodePages.xml", "ref/netstandard1.3/es/System.Text.Encoding.CodePages.xml", "ref/netstandard1.3/fr/System.Text.Encoding.CodePages.xml", "ref/netstandard1.3/it/System.Text.Encoding.CodePages.xml", "ref/netstandard1.3/ja/System.Text.Encoding.CodePages.xml", "ref/netstandard1.3/ko/System.Text.Encoding.CodePages.xml", "ref/netstandard1.3/ru/System.Text.Encoding.CodePages.xml", "ref/netstandard1.3/zh-hans/System.Text.Encoding.CodePages.xml", "ref/netstandard1.3/zh-hant/System.Text.Encoding.CodePages.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.3/System.Text.Encoding.CodePages.dll", "runtimes/win/lib/netstandard1.3/System.Text.Encoding.CodePages.dll"]}, "System.Text.Encoding.Extensions/4.0.11": {"sha512": "jtbiTDtvfLYgXn8PTfWI+SiBs51rrmO4AAckx4KR6vFK9Wzf6tI8kcRdsYQNwriUeQ1+CtQbM1W4cMbLXnj/OQ==", "type": "package", "path": "System.Text.Encoding.Extensions/4.0.11", "files": ["System.Text.Encoding.Extensions.4.0.11.nupkg.sha512", "System.Text.Encoding.Extensions.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Text.Encoding.Extensions.dll", "ref/netcore50/System.Text.Encoding.Extensions.xml", "ref/netcore50/de/System.Text.Encoding.Extensions.xml", "ref/netcore50/es/System.Text.Encoding.Extensions.xml", "ref/netcore50/fr/System.Text.Encoding.Extensions.xml", "ref/netcore50/it/System.Text.Encoding.Extensions.xml", "ref/netcore50/ja/System.Text.Encoding.Extensions.xml", "ref/netcore50/ko/System.Text.Encoding.Extensions.xml", "ref/netcore50/ru/System.Text.Encoding.Extensions.xml", "ref/netcore50/zh-hans/System.Text.Encoding.Extensions.xml", "ref/netcore50/zh-hant/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/System.Text.Encoding.Extensions.dll", "ref/netstandard1.0/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/de/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/es/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/fr/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/it/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/ja/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/ko/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/ru/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/zh-hans/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/zh-hant/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/System.Text.Encoding.Extensions.dll", "ref/netstandard1.3/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/de/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/es/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/fr/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/it/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/ja/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/ko/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/ru/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/zh-hans/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/zh-hant/System.Text.Encoding.Extensions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.Text.Encodings.Web/4.0.0": {"sha512": "TWZnuiJgPDAEEUfobD7njXvSVR2Toz+jvKWds6yL4oSztmKQfnWzucczjzA+6Dv1bktBdY71sZW1YN0X6m9chQ==", "type": "package", "path": "System.Text.Encodings.Web/4.0.0", "files": ["System.Text.Encodings.Web.4.0.0.nupkg.sha512", "System.Text.Encodings.Web.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/System.Text.Encodings.Web.dll", "lib/netstandard1.0/System.Text.Encodings.Web.xml"]}, "System.Text.RegularExpressions/4.1.0": {"sha512": "i88YCXpRTjCnoSQZtdlHkAOx4KNNik4hMy83n0+Ftlb7jvV6ZiZWMpnEZHhjBp6hQVh8gWd/iKNPzlPF7iyA2g==", "type": "package", "path": "System.Text.RegularExpressions/4.1.0", "files": ["System.Text.RegularExpressions.4.1.0.nupkg.sha512", "System.Text.RegularExpressions.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net463/System.Text.RegularExpressions.dll", "lib/netcore50/System.Text.RegularExpressions.dll", "lib/netstandard1.6/System.Text.RegularExpressions.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net463/System.Text.RegularExpressions.dll", "ref/netcore50/System.Text.RegularExpressions.dll", "ref/netcore50/System.Text.RegularExpressions.xml", "ref/netcore50/de/System.Text.RegularExpressions.xml", "ref/netcore50/es/System.Text.RegularExpressions.xml", "ref/netcore50/fr/System.Text.RegularExpressions.xml", "ref/netcore50/it/System.Text.RegularExpressions.xml", "ref/netcore50/ja/System.Text.RegularExpressions.xml", "ref/netcore50/ko/System.Text.RegularExpressions.xml", "ref/netcore50/ru/System.Text.RegularExpressions.xml", "ref/netcore50/zh-hans/System.Text.RegularExpressions.xml", "ref/netcore50/zh-hant/System.Text.RegularExpressions.xml", "ref/netstandard1.0/System.Text.RegularExpressions.dll", "ref/netstandard1.0/System.Text.RegularExpressions.xml", "ref/netstandard1.0/de/System.Text.RegularExpressions.xml", "ref/netstandard1.0/es/System.Text.RegularExpressions.xml", "ref/netstandard1.0/fr/System.Text.RegularExpressions.xml", "ref/netstandard1.0/it/System.Text.RegularExpressions.xml", "ref/netstandard1.0/ja/System.Text.RegularExpressions.xml", "ref/netstandard1.0/ko/System.Text.RegularExpressions.xml", "ref/netstandard1.0/ru/System.Text.RegularExpressions.xml", "ref/netstandard1.0/zh-hans/System.Text.RegularExpressions.xml", "ref/netstandard1.0/zh-hant/System.Text.RegularExpressions.xml", "ref/netstandard1.3/System.Text.RegularExpressions.dll", "ref/netstandard1.3/System.Text.RegularExpressions.xml", "ref/netstandard1.3/de/System.Text.RegularExpressions.xml", "ref/netstandard1.3/es/System.Text.RegularExpressions.xml", "ref/netstandard1.3/fr/System.Text.RegularExpressions.xml", "ref/netstandard1.3/it/System.Text.RegularExpressions.xml", "ref/netstandard1.3/ja/System.Text.RegularExpressions.xml", "ref/netstandard1.3/ko/System.Text.RegularExpressions.xml", "ref/netstandard1.3/ru/System.Text.RegularExpressions.xml", "ref/netstandard1.3/zh-hans/System.Text.RegularExpressions.xml", "ref/netstandard1.3/zh-hant/System.Text.RegularExpressions.xml", "ref/netstandard1.6/System.Text.RegularExpressions.dll", "ref/netstandard1.6/System.Text.RegularExpressions.xml", "ref/netstandard1.6/de/System.Text.RegularExpressions.xml", "ref/netstandard1.6/es/System.Text.RegularExpressions.xml", "ref/netstandard1.6/fr/System.Text.RegularExpressions.xml", "ref/netstandard1.6/it/System.Text.RegularExpressions.xml", "ref/netstandard1.6/ja/System.Text.RegularExpressions.xml", "ref/netstandard1.6/ko/System.Text.RegularExpressions.xml", "ref/netstandard1.6/ru/System.Text.RegularExpressions.xml", "ref/netstandard1.6/zh-hans/System.Text.RegularExpressions.xml", "ref/netstandard1.6/zh-hant/System.Text.RegularExpressions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.Threading/4.0.11": {"sha512": "N+3xqIcg3VDKyjwwCGaZ9HawG9aC6cSDI+s7ROma310GQo8vilFZa86hqKppwTHleR/G0sfOzhvgnUxWCR/DrQ==", "type": "package", "path": "System.Threading/4.0.11", "files": ["System.Threading.4.0.11.nupkg.sha512", "System.Threading.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Threading.dll", "lib/netstandard1.3/System.Threading.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Threading.dll", "ref/netcore50/System.Threading.xml", "ref/netcore50/de/System.Threading.xml", "ref/netcore50/es/System.Threading.xml", "ref/netcore50/fr/System.Threading.xml", "ref/netcore50/it/System.Threading.xml", "ref/netcore50/ja/System.Threading.xml", "ref/netcore50/ko/System.Threading.xml", "ref/netcore50/ru/System.Threading.xml", "ref/netcore50/zh-hans/System.Threading.xml", "ref/netcore50/zh-hant/System.Threading.xml", "ref/netstandard1.0/System.Threading.dll", "ref/netstandard1.0/System.Threading.xml", "ref/netstandard1.0/de/System.Threading.xml", "ref/netstandard1.0/es/System.Threading.xml", "ref/netstandard1.0/fr/System.Threading.xml", "ref/netstandard1.0/it/System.Threading.xml", "ref/netstandard1.0/ja/System.Threading.xml", "ref/netstandard1.0/ko/System.Threading.xml", "ref/netstandard1.0/ru/System.Threading.xml", "ref/netstandard1.0/zh-hans/System.Threading.xml", "ref/netstandard1.0/zh-hant/System.Threading.xml", "ref/netstandard1.3/System.Threading.dll", "ref/netstandard1.3/System.Threading.xml", "ref/netstandard1.3/de/System.Threading.xml", "ref/netstandard1.3/es/System.Threading.xml", "ref/netstandard1.3/fr/System.Threading.xml", "ref/netstandard1.3/it/System.Threading.xml", "ref/netstandard1.3/ja/System.Threading.xml", "ref/netstandard1.3/ko/System.Threading.xml", "ref/netstandard1.3/ru/System.Threading.xml", "ref/netstandard1.3/zh-hans/System.Threading.xml", "ref/netstandard1.3/zh-hant/System.Threading.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Threading.dll"]}, "System.Threading.Overlapped/4.0.1": {"sha512": "f7aLuLkBoCQM2kng7zqLFBXz9Gk48gDK8lk1ih9rH/1arJJzZK9gJwNvPDhL6Ps/l6rwOr8jw+4FCHL0KKWiEg==", "type": "package", "path": "System.Threading.Overlapped/4.0.1", "files": ["System.Threading.Overlapped.4.0.1.nupkg.sha512", "System.Threading.Overlapped.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/net46/System.Threading.Overlapped.dll", "ref/net46/System.Threading.Overlapped.dll", "ref/netstandard1.3/System.Threading.Overlapped.dll", "ref/netstandard1.3/System.Threading.Overlapped.xml", "ref/netstandard1.3/de/System.Threading.Overlapped.xml", "ref/netstandard1.3/es/System.Threading.Overlapped.xml", "ref/netstandard1.3/fr/System.Threading.Overlapped.xml", "ref/netstandard1.3/it/System.Threading.Overlapped.xml", "ref/netstandard1.3/ja/System.Threading.Overlapped.xml", "ref/netstandard1.3/ko/System.Threading.Overlapped.xml", "ref/netstandard1.3/ru/System.Threading.Overlapped.xml", "ref/netstandard1.3/zh-hans/System.Threading.Overlapped.xml", "ref/netstandard1.3/zh-hant/System.Threading.Overlapped.xml", "runtimes/unix/lib/netstandard1.3/System.Threading.Overlapped.dll", "runtimes/win/lib/net46/System.Threading.Overlapped.dll", "runtimes/win/lib/netcore50/System.Threading.Overlapped.dll", "runtimes/win/lib/netstandard1.3/System.Threading.Overlapped.dll"]}, "System.Threading.Tasks/4.0.11": {"sha512": "k1S4Gc6IGwtHGT8188RSeGaX86Qw/wnrgNLshJvsdNUOPP9etMmo8S07c+UlOAx4K/xLuN9ivA1bD0LVurtIxQ==", "type": "package", "path": "System.Threading.Tasks/4.0.11", "files": ["System.Threading.Tasks.4.0.11.nupkg.sha512", "System.Threading.Tasks.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Threading.Tasks.dll", "ref/netcore50/System.Threading.Tasks.xml", "ref/netcore50/de/System.Threading.Tasks.xml", "ref/netcore50/es/System.Threading.Tasks.xml", "ref/netcore50/fr/System.Threading.Tasks.xml", "ref/netcore50/it/System.Threading.Tasks.xml", "ref/netcore50/ja/System.Threading.Tasks.xml", "ref/netcore50/ko/System.Threading.Tasks.xml", "ref/netcore50/ru/System.Threading.Tasks.xml", "ref/netcore50/zh-hans/System.Threading.Tasks.xml", "ref/netcore50/zh-hant/System.Threading.Tasks.xml", "ref/netstandard1.0/System.Threading.Tasks.dll", "ref/netstandard1.0/System.Threading.Tasks.xml", "ref/netstandard1.0/de/System.Threading.Tasks.xml", "ref/netstandard1.0/es/System.Threading.Tasks.xml", "ref/netstandard1.0/fr/System.Threading.Tasks.xml", "ref/netstandard1.0/it/System.Threading.Tasks.xml", "ref/netstandard1.0/ja/System.Threading.Tasks.xml", "ref/netstandard1.0/ko/System.Threading.Tasks.xml", "ref/netstandard1.0/ru/System.Threading.Tasks.xml", "ref/netstandard1.0/zh-hans/System.Threading.Tasks.xml", "ref/netstandard1.0/zh-hant/System.Threading.Tasks.xml", "ref/netstandard1.3/System.Threading.Tasks.dll", "ref/netstandard1.3/System.Threading.Tasks.xml", "ref/netstandard1.3/de/System.Threading.Tasks.xml", "ref/netstandard1.3/es/System.Threading.Tasks.xml", "ref/netstandard1.3/fr/System.Threading.Tasks.xml", "ref/netstandard1.3/it/System.Threading.Tasks.xml", "ref/netstandard1.3/ja/System.Threading.Tasks.xml", "ref/netstandard1.3/ko/System.Threading.Tasks.xml", "ref/netstandard1.3/ru/System.Threading.Tasks.xml", "ref/netstandard1.3/zh-hans/System.Threading.Tasks.xml", "ref/netstandard1.3/zh-hant/System.Threading.Tasks.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.Threading.Tasks.Dataflow/4.6.0": {"sha512": "2hRjGu2r2jxRZ55wmcHO/WbdX+YAOz9x6FE8xqkHZgPaoFMKQZRe9dk8xTZIas8fRjxRmzawnTEWIrhlM+Un7w==", "type": "package", "path": "System.Threading.Tasks.Dataflow/4.6.0", "files": ["System.Threading.Tasks.Dataflow.4.6.0.nupkg.sha512", "System.Threading.Tasks.Dataflow.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/System.Threading.Tasks.Dataflow.XML", "lib/netstandard1.0/System.Threading.Tasks.Dataflow.dll", "lib/netstandard1.1/System.Threading.Tasks.Dataflow.XML", "lib/netstandard1.1/System.Threading.Tasks.Dataflow.dll"]}, "System.Threading.Tasks.Extensions/4.0.0": {"sha512": "pH4FZDsZQ/WmgJtN4LWYmRdJAEeVkyriSwrv2Teoe5FOU0Yxlb6II6GL8dBPOfRmutHGATduj3ooMt7dJ2+i+w==", "type": "package", "path": "System.Threading.Tasks.Extensions/4.0.0", "files": ["System.Threading.Tasks.Extensions.4.0.0.nupkg.sha512", "System.Threading.Tasks.Extensions.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard1.0/System.Threading.Tasks.Extensions.xml", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.xml"]}, "System.Threading.Tasks.Parallel/4.0.1": {"sha512": "7Pc9t25bcynT9FpMvkUw4ZjYwUiGup/5cJFW72/5MgCG+np2cfVUMdh29u8d7onxX7d8PS3J+wL73zQRqkdrSA==", "type": "package", "path": "System.Threading.Tasks.Parallel/4.0.1", "files": ["System.Threading.Tasks.Parallel.4.0.1.nupkg.sha512", "System.Threading.Tasks.Parallel.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Threading.Tasks.Parallel.dll", "lib/netstandard1.3/System.Threading.Tasks.Parallel.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Threading.Tasks.Parallel.dll", "ref/netcore50/System.Threading.Tasks.Parallel.xml", "ref/netcore50/de/System.Threading.Tasks.Parallel.xml", "ref/netcore50/es/System.Threading.Tasks.Parallel.xml", "ref/netcore50/fr/System.Threading.Tasks.Parallel.xml", "ref/netcore50/it/System.Threading.Tasks.Parallel.xml", "ref/netcore50/ja/System.Threading.Tasks.Parallel.xml", "ref/netcore50/ko/System.Threading.Tasks.Parallel.xml", "ref/netcore50/ru/System.Threading.Tasks.Parallel.xml", "ref/netcore50/zh-hans/System.Threading.Tasks.Parallel.xml", "ref/netcore50/zh-hant/System.Threading.Tasks.Parallel.xml", "ref/netstandard1.1/System.Threading.Tasks.Parallel.dll", "ref/netstandard1.1/System.Threading.Tasks.Parallel.xml", "ref/netstandard1.1/de/System.Threading.Tasks.Parallel.xml", "ref/netstandard1.1/es/System.Threading.Tasks.Parallel.xml", "ref/netstandard1.1/fr/System.Threading.Tasks.Parallel.xml", "ref/netstandard1.1/it/System.Threading.Tasks.Parallel.xml", "ref/netstandard1.1/ja/System.Threading.Tasks.Parallel.xml", "ref/netstandard1.1/ko/System.Threading.Tasks.Parallel.xml", "ref/netstandard1.1/ru/System.Threading.Tasks.Parallel.xml", "ref/netstandard1.1/zh-hans/System.Threading.Tasks.Parallel.xml", "ref/netstandard1.1/zh-hant/System.Threading.Tasks.Parallel.xml", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.Threading.Thread/4.0.0": {"sha512": "gIdJqDXlOr5W9zeqFErLw3dsOsiShSCYtF9SEHitACycmvNvY8odf9kiKvp6V7aibc8C4HzzNBkWXjyfn7plbQ==", "type": "package", "path": "System.Threading.Thread/4.0.0", "files": ["System.Threading.Thread.4.0.0.nupkg.sha512", "System.Threading.Thread.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Threading.Thread.dll", "lib/netcore50/_._", "lib/netstandard1.3/System.Threading.Thread.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Threading.Thread.dll", "ref/netstandard1.3/System.Threading.Thread.dll", "ref/netstandard1.3/System.Threading.Thread.xml", "ref/netstandard1.3/de/System.Threading.Thread.xml", "ref/netstandard1.3/es/System.Threading.Thread.xml", "ref/netstandard1.3/fr/System.Threading.Thread.xml", "ref/netstandard1.3/it/System.Threading.Thread.xml", "ref/netstandard1.3/ja/System.Threading.Thread.xml", "ref/netstandard1.3/ko/System.Threading.Thread.xml", "ref/netstandard1.3/ru/System.Threading.Thread.xml", "ref/netstandard1.3/zh-hans/System.Threading.Thread.xml", "ref/netstandard1.3/zh-hant/System.Threading.Thread.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.Threading.ThreadPool/4.0.10": {"sha512": "IMXgB5Vf/5Qw1kpoVgJMOvUO1l32aC+qC3OaIZjWJOjvcxuxNWOK2ZTWWYXfij22NHxT2j1yWX5vlAeQWld9vA==", "type": "package", "path": "System.Threading.ThreadPool/4.0.10", "files": ["System.Threading.ThreadPool.4.0.10.nupkg.sha512", "System.Threading.ThreadPool.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Threading.ThreadPool.dll", "lib/netcore50/_._", "lib/netstandard1.3/System.Threading.ThreadPool.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Threading.ThreadPool.dll", "ref/netstandard1.3/System.Threading.ThreadPool.dll", "ref/netstandard1.3/System.Threading.ThreadPool.xml", "ref/netstandard1.3/de/System.Threading.ThreadPool.xml", "ref/netstandard1.3/es/System.Threading.ThreadPool.xml", "ref/netstandard1.3/fr/System.Threading.ThreadPool.xml", "ref/netstandard1.3/it/System.Threading.ThreadPool.xml", "ref/netstandard1.3/ja/System.Threading.ThreadPool.xml", "ref/netstandard1.3/ko/System.Threading.ThreadPool.xml", "ref/netstandard1.3/ru/System.Threading.ThreadPool.xml", "ref/netstandard1.3/zh-hans/System.Threading.ThreadPool.xml", "ref/netstandard1.3/zh-hant/System.Threading.ThreadPool.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.Threading.Timer/4.0.1": {"sha512": "saGfUV8uqVW6LeURiqxcGhZ24PzuRNaUBtbhVeuUAvky1naH395A/1nY0P2bWvrw/BreRtIB/EzTDkGBpqCwEw==", "type": "package", "path": "System.Threading.Timer/4.0.1", "files": ["System.Threading.Timer.4.0.1.nupkg.sha512", "System.Threading.Timer.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net451/_._", "lib/portable-net451+win81+wpa81/_._", "lib/win81/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net451/_._", "ref/netcore50/System.Threading.Timer.dll", "ref/netcore50/System.Threading.Timer.xml", "ref/netcore50/de/System.Threading.Timer.xml", "ref/netcore50/es/System.Threading.Timer.xml", "ref/netcore50/fr/System.Threading.Timer.xml", "ref/netcore50/it/System.Threading.Timer.xml", "ref/netcore50/ja/System.Threading.Timer.xml", "ref/netcore50/ko/System.Threading.Timer.xml", "ref/netcore50/ru/System.Threading.Timer.xml", "ref/netcore50/zh-hans/System.Threading.Timer.xml", "ref/netcore50/zh-hant/System.Threading.Timer.xml", "ref/netstandard1.2/System.Threading.Timer.dll", "ref/netstandard1.2/System.Threading.Timer.xml", "ref/netstandard1.2/de/System.Threading.Timer.xml", "ref/netstandard1.2/es/System.Threading.Timer.xml", "ref/netstandard1.2/fr/System.Threading.Timer.xml", "ref/netstandard1.2/it/System.Threading.Timer.xml", "ref/netstandard1.2/ja/System.Threading.Timer.xml", "ref/netstandard1.2/ko/System.Threading.Timer.xml", "ref/netstandard1.2/ru/System.Threading.Timer.xml", "ref/netstandard1.2/zh-hans/System.Threading.Timer.xml", "ref/netstandard1.2/zh-hant/System.Threading.Timer.xml", "ref/portable-net451+win81+wpa81/_._", "ref/win81/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.Xml.ReaderWriter/4.0.11": {"sha512": "ZIiLPsf67YZ9zgr31vzrFaYQqxRPX9cVHjtPSnmx4eN6lbS/yEyYNr2vs1doGDEscF0tjCZFsk9yUg1sC9e8tg==", "type": "package", "path": "System.Xml.ReaderWriter/4.0.11", "files": ["System.Xml.ReaderWriter.4.0.11.nupkg.sha512", "System.Xml.ReaderWriter.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Xml.ReaderWriter.dll", "lib/netstandard1.3/System.Xml.ReaderWriter.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Xml.ReaderWriter.dll", "ref/netcore50/System.Xml.ReaderWriter.xml", "ref/netcore50/de/System.Xml.ReaderWriter.xml", "ref/netcore50/es/System.Xml.ReaderWriter.xml", "ref/netcore50/fr/System.Xml.ReaderWriter.xml", "ref/netcore50/it/System.Xml.ReaderWriter.xml", "ref/netcore50/ja/System.Xml.ReaderWriter.xml", "ref/netcore50/ko/System.Xml.ReaderWriter.xml", "ref/netcore50/ru/System.Xml.ReaderWriter.xml", "ref/netcore50/zh-hans/System.Xml.ReaderWriter.xml", "ref/netcore50/zh-hant/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/System.Xml.ReaderWriter.dll", "ref/netstandard1.0/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/de/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/es/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/fr/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/it/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/ja/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/ko/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/ru/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/zh-hans/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/zh-hant/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/System.Xml.ReaderWriter.dll", "ref/netstandard1.3/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/de/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/es/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/fr/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/it/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/ja/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/ko/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/ru/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/zh-hans/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/zh-hant/System.Xml.ReaderWriter.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.Xml.XDocument/4.0.11": {"sha512": "Mk2mKmPi0nWaoiYeotq1dgeNK1fqWh61+EK+w4Wu8SWuTYLzpUnschb59bJtGywaPq7SmTuPf44wrXRwbIrukg==", "type": "package", "path": "System.Xml.XDocument/4.0.11", "files": ["System.Xml.XDocument.4.0.11.nupkg.sha512", "System.Xml.XDocument.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Xml.XDocument.dll", "lib/netstandard1.3/System.Xml.XDocument.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Xml.XDocument.dll", "ref/netcore50/System.Xml.XDocument.xml", "ref/netcore50/de/System.Xml.XDocument.xml", "ref/netcore50/es/System.Xml.XDocument.xml", "ref/netcore50/fr/System.Xml.XDocument.xml", "ref/netcore50/it/System.Xml.XDocument.xml", "ref/netcore50/ja/System.Xml.XDocument.xml", "ref/netcore50/ko/System.Xml.XDocument.xml", "ref/netcore50/ru/System.Xml.XDocument.xml", "ref/netcore50/zh-hans/System.Xml.XDocument.xml", "ref/netcore50/zh-hant/System.Xml.XDocument.xml", "ref/netstandard1.0/System.Xml.XDocument.dll", "ref/netstandard1.0/System.Xml.XDocument.xml", "ref/netstandard1.0/de/System.Xml.XDocument.xml", "ref/netstandard1.0/es/System.Xml.XDocument.xml", "ref/netstandard1.0/fr/System.Xml.XDocument.xml", "ref/netstandard1.0/it/System.Xml.XDocument.xml", "ref/netstandard1.0/ja/System.Xml.XDocument.xml", "ref/netstandard1.0/ko/System.Xml.XDocument.xml", "ref/netstandard1.0/ru/System.Xml.XDocument.xml", "ref/netstandard1.0/zh-hans/System.Xml.XDocument.xml", "ref/netstandard1.0/zh-hant/System.Xml.XDocument.xml", "ref/netstandard1.3/System.Xml.XDocument.dll", "ref/netstandard1.3/System.Xml.XDocument.xml", "ref/netstandard1.3/de/System.Xml.XDocument.xml", "ref/netstandard1.3/es/System.Xml.XDocument.xml", "ref/netstandard1.3/fr/System.Xml.XDocument.xml", "ref/netstandard1.3/it/System.Xml.XDocument.xml", "ref/netstandard1.3/ja/System.Xml.XDocument.xml", "ref/netstandard1.3/ko/System.Xml.XDocument.xml", "ref/netstandard1.3/ru/System.Xml.XDocument.xml", "ref/netstandard1.3/zh-hans/System.Xml.XDocument.xml", "ref/netstandard1.3/zh-hant/System.Xml.XDocument.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.Xml.XmlDocument/4.0.1": {"sha512": "2eZu6IP+etFVBBFUFzw2w6J21DqIN5eL9Y8r8JfJWUmV28Z5P0SNU01oCisVHQgHsDhHPnmq2s1hJrJCFZWloQ==", "type": "package", "path": "System.Xml.XmlDocument/4.0.1", "files": ["System.Xml.XmlDocument.4.0.1.nupkg.sha512", "System.Xml.XmlDocument.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Xml.XmlDocument.dll", "lib/netstandard1.3/System.Xml.XmlDocument.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Xml.XmlDocument.dll", "ref/netstandard1.3/System.Xml.XmlDocument.dll", "ref/netstandard1.3/System.Xml.XmlDocument.xml", "ref/netstandard1.3/de/System.Xml.XmlDocument.xml", "ref/netstandard1.3/es/System.Xml.XmlDocument.xml", "ref/netstandard1.3/fr/System.Xml.XmlDocument.xml", "ref/netstandard1.3/it/System.Xml.XmlDocument.xml", "ref/netstandard1.3/ja/System.Xml.XmlDocument.xml", "ref/netstandard1.3/ko/System.Xml.XmlDocument.xml", "ref/netstandard1.3/ru/System.Xml.XmlDocument.xml", "ref/netstandard1.3/zh-hans/System.Xml.XmlDocument.xml", "ref/netstandard1.3/zh-hant/System.Xml.XmlDocument.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.Xml.XPath/4.0.1": {"sha512": "UWd1H+1IJ9Wlq5nognZ/XJdyj8qPE4XufBUkAW59ijsCPjZkZe0MUzKKJFBr+ZWBe5Wq1u1d5f2CYgE93uH7DA==", "type": "package", "path": "System.Xml.XPath/4.0.1", "files": ["System.Xml.XPath.4.0.1.nupkg.sha512", "System.Xml.XPath.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Xml.XPath.dll", "lib/netstandard1.3/System.Xml.XPath.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Xml.XPath.dll", "ref/netstandard1.3/System.Xml.XPath.dll", "ref/netstandard1.3/System.Xml.XPath.xml", "ref/netstandard1.3/de/System.Xml.XPath.xml", "ref/netstandard1.3/es/System.Xml.XPath.xml", "ref/netstandard1.3/fr/System.Xml.XPath.xml", "ref/netstandard1.3/it/System.Xml.XPath.xml", "ref/netstandard1.3/ja/System.Xml.XPath.xml", "ref/netstandard1.3/ko/System.Xml.XPath.xml", "ref/netstandard1.3/ru/System.Xml.XPath.xml", "ref/netstandard1.3/zh-hans/System.Xml.XPath.xml", "ref/netstandard1.3/zh-hant/System.Xml.XPath.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "System.Xml.XPath.XDocument/4.0.1": {"sha512": "FLhdYJx4331oGovQypQ8JIw2kEmNzCsjVOVYY/16kZTUoquZG85oVn7yUhBE2OZt1yGPSXAL0HTEfzjlbNpM7Q==", "type": "package", "path": "System.Xml.XPath.XDocument/4.0.1", "files": ["System.Xml.XPath.XDocument.4.0.1.nupkg.sha512", "System.Xml.XPath.XDocument.nuspec", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Xml.XPath.XDocument.dll", "lib/netstandard1.3/System.Xml.XPath.XDocument.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Xml.XPath.XDocument.dll", "ref/netstandard1.3/System.Xml.XPath.XDocument.dll", "ref/netstandard1.3/System.Xml.XPath.XDocument.xml", "ref/netstandard1.3/de/System.Xml.XPath.XDocument.xml", "ref/netstandard1.3/es/System.Xml.XPath.XDocument.xml", "ref/netstandard1.3/fr/System.Xml.XPath.XDocument.xml", "ref/netstandard1.3/it/System.Xml.XPath.XDocument.xml", "ref/netstandard1.3/ja/System.Xml.XPath.XDocument.xml", "ref/netstandard1.3/ko/System.Xml.XPath.XDocument.xml", "ref/netstandard1.3/ru/System.Xml.XPath.XDocument.xml", "ref/netstandard1.3/zh-hans/System.Xml.XPath.XDocument.xml", "ref/netstandard1.3/zh-hant/System.Xml.XPath.XDocument.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}}, "projectFileDependencyGroups": {"": ["Microsoft.AspNetCore.Mvc >= 1.0.0", "Microsoft.AspNetCore.Server.IISIntegration >= 1.0.0", "Microsoft.AspNetCore.Server.Kestrel >= 1.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables >= 1.0.0", "Microsoft.Extensions.Configuration.FileExtensions >= 1.0.0", "Microsoft.Extensions.Configuration.Json >= 1.0.0", "Microsoft.Extensions.Logging >= 1.0.0", "Microsoft.Extensions.Logging.Console >= 1.0.0", "Microsoft.Extensions.Logging.Debug >= 1.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions >= 1.0.0", "Microsoft.NETCore.App >= 1.0.0"], ".NETCoreApp,Version=v1.0": []}, "tools": {".NETCoreApp,Version=v1.0": {"Microsoft.AspNetCore.Server.IISIntegration.Tools/1.0.0-preview2-final": {"type": "package", "dependencies": {"Microsoft.DotNet.ProjectModel": "1.0.0-rc3-003121", "Microsoft.Extensions.CommandLineUtils": "1.0.0", "Microsoft.NETCore.App": "1.0.0", "System.Diagnostics.Process": "4.1.0"}, "compile": {"lib/netcoreapp1.0/dotnet-publish-iis.dll": {}}, "runtime": {"lib/netcoreapp1.0/dotnet-publish-iis.dll": {}}}}}, "projectFileToolGroups": {".NETCoreApp,Version=v1.0": ["Microsoft.AspNetCore.Server.IISIntegration.Tools >= 1.0.0-preview2-final"]}}