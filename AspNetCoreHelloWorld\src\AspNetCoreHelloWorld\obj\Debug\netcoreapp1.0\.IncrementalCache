{"inputs": ["C:\\ASP.NET\\AspNetCoreHelloWorld\\src\\AspNetCoreHelloWorld\\project.json", "C:\\ASP.NET\\AspNetCoreHelloWorld\\src\\AspNetCoreHelloWorld\\project.lock.json", "C:\\ASP.NET\\AspNetCoreHelloWorld\\src\\AspNetCoreHelloWorld\\Program.cs", "C:\\ASP.NET\\AspNetCoreHelloWorld\\src\\AspNetCoreHelloWorld\\Startup.cs"], "outputs": ["C:\\ASP.NET\\AspNetCoreHelloWorld\\src\\AspNetCoreHelloWorld\\bin\\Debug\\netcoreapp1.0\\AspNetCoreHelloWorld.dll", "C:\\ASP.NET\\AspNetCoreHelloWorld\\src\\AspNetCoreHelloWorld\\bin\\Debug\\netcoreapp1.0\\AspNetCoreHelloWorld.pdb", "C:\\ASP.NET\\AspNetCoreHelloWorld\\src\\AspNetCoreHelloWorld\\bin\\Debug\\netcoreapp1.0\\AspNetCoreHelloWorld.deps.json", "C:\\ASP.NET\\AspNetCoreHelloWorld\\src\\AspNetCoreHelloWorld\\bin\\Debug\\netcoreapp1.0\\AspNetCoreHelloWorld.runtimeconfig.json"], "buildArguments": {"version-suffix": null}}