﻿-d:DEBUG
-d:TRACE
-d:NETCOREAPP1_0
-nowarn:CS1701
-nowarn:CS1702
-nowarn:CS1705
-debug:full
-nostdlib
-nologo
"C:\ASP.NET\AspNetCoreHelloWorld\src\AspNetCoreHelloWorld\obj\Debug\netcoreapp1.0\dotnet-compile.assemblyinfo.cs"
-out:"C:\ASP.NET\AspNetCoreHelloWorld\src\AspNetCoreHelloWorld\bin\Debug\netcoreapp1.0\AspNetCoreHelloWorld.dll"
-r:"C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Diagnostics\1.0.0\lib\netstandard1.3\Microsoft.AspNetCore.Diagnostics.dll"
-r:"C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Diagnostics.Abstractions\1.0.0\lib\netstandard1.0\Microsoft.AspNetCore.Diagnostics.Abstractions.dll"
-r:"C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Hosting\1.0.0\lib\netstandard1.3\Microsoft.AspNetCore.Hosting.dll"
-r:"C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Hosting.Abstractions\1.0.0\lib\netstandard1.3\Microsoft.AspNetCore.Hosting.Abstractions.dll"
-r:"C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Hosting.Server.Abstractions\1.0.0\lib\netstandard1.3\Microsoft.AspNetCore.Hosting.Server.Abstractions.dll"
-r:"C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Http\1.0.0\lib\netstandard1.3\Microsoft.AspNetCore.Http.dll"
-r:"C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Http.Abstractions\1.0.0\lib\netstandard1.3\Microsoft.AspNetCore.Http.Abstractions.dll"
-r:"C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Http.Extensions\1.0.0\lib\netstandard1.3\Microsoft.AspNetCore.Http.Extensions.dll"
-r:"C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Http.Features\1.0.0\lib\netstandard1.3\Microsoft.AspNetCore.Http.Features.dll"
-r:"C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.HttpOverrides\1.0.0\lib\netstandard1.3\Microsoft.AspNetCore.HttpOverrides.dll"
-r:"C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Server.IISIntegration\1.0.0\lib\netstandard1.3\Microsoft.AspNetCore.Server.IISIntegration.dll"
-r:"C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Server.Kestrel\1.0.0\lib\netstandard1.3\Microsoft.AspNetCore.Server.Kestrel.dll"
-r:"C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.WebUtilities\1.0.0\lib\netstandard1.3\Microsoft.AspNetCore.WebUtilities.dll"
-r:"C:\Users\<USER>\.nuget\packages\Microsoft.CSharp\4.0.1\ref\netstandard1.0\Microsoft.CSharp.dll"
-r:"C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.Configuration\1.0.0\lib\netstandard1.1\Microsoft.Extensions.Configuration.dll"
-r:"C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.Configuration.Abstractions\1.0.0\lib\netstandard1.0\Microsoft.Extensions.Configuration.Abstractions.dll"
-r:"C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.Configuration.EnvironmentVariables\1.0.0\lib\netstandard1.3\Microsoft.Extensions.Configuration.EnvironmentVariables.dll"
-r:"C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.DependencyInjection\1.0.0\lib\netstandard1.1\Microsoft.Extensions.DependencyInjection.dll"
-r:"C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.DependencyInjection.Abstractions\1.0.0\lib\netstandard1.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll"
-r:"C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.FileProviders.Abstractions\1.0.0\lib\netstandard1.0\Microsoft.Extensions.FileProviders.Abstractions.dll"
-r:"C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.FileProviders.Physical\1.0.0\lib\netstandard1.3\Microsoft.Extensions.FileProviders.Physical.dll"
-r:"C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.FileSystemGlobbing\1.0.0\lib\netstandard1.3\Microsoft.Extensions.FileSystemGlobbing.dll"
-r:"C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.Logging\1.0.0\lib\netstandard1.1\Microsoft.Extensions.Logging.dll"
-r:"C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.Logging.Abstractions\1.0.0\lib\netstandard1.1\Microsoft.Extensions.Logging.Abstractions.dll"
-r:"C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.Logging.Console\1.0.0\lib\netstandard1.3\Microsoft.Extensions.Logging.Console.dll"
-r:"C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.ObjectPool\1.0.0\lib\netstandard1.3\Microsoft.Extensions.ObjectPool.dll"
-r:"C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.Options\1.0.0\lib\netstandard1.0\Microsoft.Extensions.Options.dll"
-r:"C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.PlatformAbstractions\1.0.0\lib\netstandard1.3\Microsoft.Extensions.PlatformAbstractions.dll"
-r:"C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.Primitives\1.0.0\lib\netstandard1.0\Microsoft.Extensions.Primitives.dll"
-r:"C:\Users\<USER>\.nuget\packages\Microsoft.Net.Http.Headers\1.0.0\lib\netstandard1.1\Microsoft.Net.Http.Headers.dll"
-r:"C:\Users\<USER>\.nuget\packages\Microsoft.VisualBasic\10.0.1\ref\netstandard1.1\Microsoft.VisualBasic.dll"
-r:"C:\Users\<USER>\.nuget\packages\Microsoft.Win32.Primitives\4.0.1\ref\netstandard1.3\Microsoft.Win32.Primitives.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.AppContext\4.1.0\ref\netstandard1.6\System.AppContext.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Buffers\4.0.0\lib\netstandard1.1\System.Buffers.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Collections\4.0.11\ref\netstandard1.3\System.Collections.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Collections.Concurrent\4.0.12\ref\netstandard1.3\System.Collections.Concurrent.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Collections.Immutable\1.2.0\lib\netstandard1.0\System.Collections.Immutable.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.ComponentModel\4.0.1\ref\netstandard1.0\System.ComponentModel.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.ComponentModel.Annotations\4.1.0\ref\netstandard1.4\System.ComponentModel.Annotations.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Console\4.0.0\ref\netstandard1.3\System.Console.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Diagnostics.Contracts\4.0.1\ref\netstandard1.0\System.Diagnostics.Contracts.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Diagnostics.Debug\4.0.11\ref\netstandard1.3\System.Diagnostics.Debug.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Diagnostics.DiagnosticSource\4.0.0\lib\netstandard1.3\System.Diagnostics.DiagnosticSource.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Diagnostics.Process\4.1.0\ref\netstandard1.4\System.Diagnostics.Process.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Diagnostics.StackTrace\4.0.1\ref\netstandard1.3\System.Diagnostics.StackTrace.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Diagnostics.Tools\4.0.1\ref\netstandard1.0\System.Diagnostics.Tools.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Diagnostics.Tracing\4.1.0\ref\netstandard1.5\System.Diagnostics.Tracing.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Dynamic.Runtime\4.0.11\ref\netstandard1.3\System.Dynamic.Runtime.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Globalization\4.0.11\ref\netstandard1.3\System.Globalization.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Globalization.Calendars\4.0.1\ref\netstandard1.3\System.Globalization.Calendars.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Globalization.Extensions\4.0.1\ref\netstandard1.3\System.Globalization.Extensions.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.IO\4.1.0\ref\netstandard1.5\System.IO.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.IO.Compression\4.1.0\ref\netstandard1.3\System.IO.Compression.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.IO.Compression.ZipFile\4.0.1\ref\netstandard1.3\System.IO.Compression.ZipFile.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.IO.FileSystem\4.0.1\ref\netstandard1.3\System.IO.FileSystem.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.IO.FileSystem.Primitives\4.0.1\ref\netstandard1.3\System.IO.FileSystem.Primitives.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.IO.FileSystem.Watcher\4.0.0\ref\netstandard1.3\System.IO.FileSystem.Watcher.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.IO.MemoryMappedFiles\4.0.0\ref\netstandard1.3\System.IO.MemoryMappedFiles.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.IO.UnmanagedMemoryStream\4.0.1\ref\netstandard1.3\System.IO.UnmanagedMemoryStream.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Linq\4.1.0\ref\netstandard1.6\System.Linq.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Linq.Expressions\4.1.0\ref\netstandard1.6\System.Linq.Expressions.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Linq.Parallel\4.0.1\ref\netstandard1.1\System.Linq.Parallel.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Linq.Queryable\4.0.1\ref\netstandard1.0\System.Linq.Queryable.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Net.Http\4.1.0\ref\netstandard1.3\System.Net.Http.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Net.NameResolution\4.0.0\ref\netstandard1.3\System.Net.NameResolution.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Net.Primitives\4.0.11\ref\netstandard1.3\System.Net.Primitives.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Net.Requests\4.0.11\ref\netstandard1.3\System.Net.Requests.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Net.Security\4.0.0\ref\netstandard1.3\System.Net.Security.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Net.Sockets\4.1.0\ref\netstandard1.3\System.Net.Sockets.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Net.WebHeaderCollection\4.0.1\ref\netstandard1.3\System.Net.WebHeaderCollection.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Net.WebSockets\4.0.0\ref\netstandard1.3\System.Net.WebSockets.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Numerics.Vectors\4.1.1\ref\netstandard1.0\System.Numerics.Vectors.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.ObjectModel\4.0.12\ref\netstandard1.3\System.ObjectModel.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Reflection\4.1.0\ref\netstandard1.5\System.Reflection.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Reflection.DispatchProxy\4.0.1\ref\netstandard1.3\System.Reflection.DispatchProxy.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Reflection.Extensions\4.0.1\ref\netstandard1.0\System.Reflection.Extensions.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Reflection.Metadata\1.3.0\lib\netstandard1.1\System.Reflection.Metadata.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Reflection.Primitives\4.0.1\ref\netstandard1.0\System.Reflection.Primitives.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Reflection.TypeExtensions\4.1.0\ref\netstandard1.5\System.Reflection.TypeExtensions.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Resources.Reader\4.0.0\lib\netstandard1.0\System.Resources.Reader.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Resources.ResourceManager\4.0.1\ref\netstandard1.0\System.Resources.ResourceManager.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Runtime\4.1.0\ref\netstandard1.5\System.Runtime.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Runtime.Extensions\4.1.0\ref\netstandard1.5\System.Runtime.Extensions.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Runtime.Handles\4.0.1\ref\netstandard1.3\System.Runtime.Handles.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Runtime.InteropServices\4.1.0\ref\netstandard1.5\System.Runtime.InteropServices.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Runtime.InteropServices.RuntimeInformation\4.0.0\ref\netstandard1.1\System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Runtime.Numerics\4.0.1\ref\netstandard1.1\System.Runtime.Numerics.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Security.Claims\4.0.1\ref\netstandard1.3\System.Security.Claims.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Security.Cryptography.Algorithms\4.2.0\ref\netstandard1.6\System.Security.Cryptography.Algorithms.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Security.Cryptography.Encoding\4.0.0\ref\netstandard1.3\System.Security.Cryptography.Encoding.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Security.Cryptography.Primitives\4.0.0\ref\netstandard1.3\System.Security.Cryptography.Primitives.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Security.Cryptography.X509Certificates\4.1.0\ref\netstandard1.4\System.Security.Cryptography.X509Certificates.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Security.Principal\4.0.1\ref\netstandard1.0\System.Security.Principal.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Security.Principal.Windows\4.0.0\ref\netstandard1.3\System.Security.Principal.Windows.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Text.Encoding\4.0.11\ref\netstandard1.3\System.Text.Encoding.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Text.Encoding.Extensions\4.0.11\ref\netstandard1.3\System.Text.Encoding.Extensions.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Text.Encodings.Web\4.0.0\lib\netstandard1.0\System.Text.Encodings.Web.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Text.RegularExpressions\4.1.0\ref\netstandard1.6\System.Text.RegularExpressions.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Threading\4.0.11\ref\netstandard1.3\System.Threading.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Threading.Tasks\4.0.11\ref\netstandard1.3\System.Threading.Tasks.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Threading.Tasks.Dataflow\4.6.0\lib\netstandard1.1\System.Threading.Tasks.Dataflow.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Threading.Tasks.Extensions\4.0.0\lib\netstandard1.0\System.Threading.Tasks.Extensions.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Threading.Tasks.Parallel\4.0.1\ref\netstandard1.1\System.Threading.Tasks.Parallel.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Threading.Thread\4.0.0\ref\netstandard1.3\System.Threading.Thread.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Threading.ThreadPool\4.0.10\ref\netstandard1.3\System.Threading.ThreadPool.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Threading.Timer\4.0.1\ref\netstandard1.2\System.Threading.Timer.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Xml.ReaderWriter\4.0.11\ref\netstandard1.3\System.Xml.ReaderWriter.dll"
-r:"C:\Users\<USER>\.nuget\packages\System.Xml.XDocument\4.0.11\ref\netstandard1.3\System.Xml.XDocument.dll"
-resource:"C:\ASP.NET\AspNetCoreHelloWorld\src\AspNetCoreHelloWorld\obj\Debug\netcoreapp1.0\AspNetCoreHelloWorlddotnet-compile.deps.json",AspNetCoreHelloWorld.deps.json
"C:\ASP.NET\AspNetCoreHelloWorld\src\AspNetCoreHelloWorld\Program.cs"
"C:\ASP.NET\AspNetCoreHelloWorld\src\AspNetCoreHelloWorld\Startup.cs"
