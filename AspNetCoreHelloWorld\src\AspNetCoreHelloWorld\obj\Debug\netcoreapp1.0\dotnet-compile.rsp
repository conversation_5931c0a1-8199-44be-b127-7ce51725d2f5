--temp-output:C:\ASP.NET\AspNetCoreHelloWorld\src\AspNetCoreHelloWorld\obj\Debug\netcoreapp1.0\
--out:C:\ASP.NET\AspNetCoreHelloWorld\src\AspNetCoreHelloWorld\bin\Debug\netcoreapp1.0\AspNetCoreHelloWorld.dll
--define:DEBUG
--define:TRACE
--define:NETCOREAPP1_0
--suppress-warning:CS1701
--suppress-warning:CS1702
--suppress-warning:CS1705
--optimize:False
--emit-entry-point:True
--output-name:Asp<PERSON><PERSON>oreHelloWorld
--file-version:*******
--version:*******
--informational-version:1.0.0
--target-framework:.NETCoreApp,Version=v1.0
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Diagnostics\1.0.0\lib\netstandard1.3\Microsoft.AspNetCore.Diagnostics.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Diagnostics.Abstractions\1.0.0\lib\netstandard1.0\Microsoft.AspNetCore.Diagnostics.Abstractions.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Hosting\1.0.0\lib\netstandard1.3\Microsoft.AspNetCore.Hosting.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Hosting.Abstractions\1.0.0\lib\netstandard1.3\Microsoft.AspNetCore.Hosting.Abstractions.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Hosting.Server.Abstractions\1.0.0\lib\netstandard1.3\Microsoft.AspNetCore.Hosting.Server.Abstractions.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Http\1.0.0\lib\netstandard1.3\Microsoft.AspNetCore.Http.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Http.Abstractions\1.0.0\lib\netstandard1.3\Microsoft.AspNetCore.Http.Abstractions.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Http.Extensions\1.0.0\lib\netstandard1.3\Microsoft.AspNetCore.Http.Extensions.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Http.Features\1.0.0\lib\netstandard1.3\Microsoft.AspNetCore.Http.Features.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.HttpOverrides\1.0.0\lib\netstandard1.3\Microsoft.AspNetCore.HttpOverrides.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Server.IISIntegration\1.0.0\lib\netstandard1.3\Microsoft.AspNetCore.Server.IISIntegration.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Server.Kestrel\1.0.0\lib\netstandard1.3\Microsoft.AspNetCore.Server.Kestrel.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.WebUtilities\1.0.0\lib\netstandard1.3\Microsoft.AspNetCore.WebUtilities.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.CSharp\4.0.1\ref\netstandard1.0\Microsoft.CSharp.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.Configuration\1.0.0\lib\netstandard1.1\Microsoft.Extensions.Configuration.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.Configuration.Abstractions\1.0.0\lib\netstandard1.0\Microsoft.Extensions.Configuration.Abstractions.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.Configuration.EnvironmentVariables\1.0.0\lib\netstandard1.3\Microsoft.Extensions.Configuration.EnvironmentVariables.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.DependencyInjection\1.0.0\lib\netstandard1.1\Microsoft.Extensions.DependencyInjection.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.DependencyInjection.Abstractions\1.0.0\lib\netstandard1.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.FileProviders.Abstractions\1.0.0\lib\netstandard1.0\Microsoft.Extensions.FileProviders.Abstractions.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.FileProviders.Physical\1.0.0\lib\netstandard1.3\Microsoft.Extensions.FileProviders.Physical.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.FileSystemGlobbing\1.0.0\lib\netstandard1.3\Microsoft.Extensions.FileSystemGlobbing.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.Logging\1.0.0\lib\netstandard1.1\Microsoft.Extensions.Logging.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.Logging.Abstractions\1.0.0\lib\netstandard1.1\Microsoft.Extensions.Logging.Abstractions.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.Logging.Console\1.0.0\lib\netstandard1.3\Microsoft.Extensions.Logging.Console.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.ObjectPool\1.0.0\lib\netstandard1.3\Microsoft.Extensions.ObjectPool.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.Options\1.0.0\lib\netstandard1.0\Microsoft.Extensions.Options.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.PlatformAbstractions\1.0.0\lib\netstandard1.3\Microsoft.Extensions.PlatformAbstractions.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.Primitives\1.0.0\lib\netstandard1.0\Microsoft.Extensions.Primitives.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.Net.Http.Headers\1.0.0\lib\netstandard1.1\Microsoft.Net.Http.Headers.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.VisualBasic\10.0.1\ref\netstandard1.1\Microsoft.VisualBasic.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.Win32.Primitives\4.0.1\ref\netstandard1.3\Microsoft.Win32.Primitives.dll
--reference:C:\Users\<USER>\.nuget\packages\System.AppContext\4.1.0\ref\netstandard1.6\System.AppContext.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Buffers\4.0.0\lib\netstandard1.1\System.Buffers.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Collections\4.0.11\ref\netstandard1.3\System.Collections.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Collections.Concurrent\4.0.12\ref\netstandard1.3\System.Collections.Concurrent.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Collections.Immutable\1.2.0\lib\netstandard1.0\System.Collections.Immutable.dll
--reference:C:\Users\<USER>\.nuget\packages\System.ComponentModel\4.0.1\ref\netstandard1.0\System.ComponentModel.dll
--reference:C:\Users\<USER>\.nuget\packages\System.ComponentModel.Annotations\4.1.0\ref\netstandard1.4\System.ComponentModel.Annotations.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Console\4.0.0\ref\netstandard1.3\System.Console.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Diagnostics.Contracts\4.0.1\ref\netstandard1.0\System.Diagnostics.Contracts.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Diagnostics.Debug\4.0.11\ref\netstandard1.3\System.Diagnostics.Debug.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Diagnostics.DiagnosticSource\4.0.0\lib\netstandard1.3\System.Diagnostics.DiagnosticSource.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Diagnostics.Process\4.1.0\ref\netstandard1.4\System.Diagnostics.Process.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Diagnostics.StackTrace\4.0.1\ref\netstandard1.3\System.Diagnostics.StackTrace.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Diagnostics.Tools\4.0.1\ref\netstandard1.0\System.Diagnostics.Tools.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Diagnostics.Tracing\4.1.0\ref\netstandard1.5\System.Diagnostics.Tracing.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Dynamic.Runtime\4.0.11\ref\netstandard1.3\System.Dynamic.Runtime.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Globalization\4.0.11\ref\netstandard1.3\System.Globalization.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Globalization.Calendars\4.0.1\ref\netstandard1.3\System.Globalization.Calendars.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Globalization.Extensions\4.0.1\ref\netstandard1.3\System.Globalization.Extensions.dll
--reference:C:\Users\<USER>\.nuget\packages\System.IO\4.1.0\ref\netstandard1.5\System.IO.dll
--reference:C:\Users\<USER>\.nuget\packages\System.IO.Compression\4.1.0\ref\netstandard1.3\System.IO.Compression.dll
--reference:C:\Users\<USER>\.nuget\packages\System.IO.Compression.ZipFile\4.0.1\ref\netstandard1.3\System.IO.Compression.ZipFile.dll
--reference:C:\Users\<USER>\.nuget\packages\System.IO.FileSystem\4.0.1\ref\netstandard1.3\System.IO.FileSystem.dll
--reference:C:\Users\<USER>\.nuget\packages\System.IO.FileSystem.Primitives\4.0.1\ref\netstandard1.3\System.IO.FileSystem.Primitives.dll
--reference:C:\Users\<USER>\.nuget\packages\System.IO.FileSystem.Watcher\4.0.0\ref\netstandard1.3\System.IO.FileSystem.Watcher.dll
--reference:C:\Users\<USER>\.nuget\packages\System.IO.MemoryMappedFiles\4.0.0\ref\netstandard1.3\System.IO.MemoryMappedFiles.dll
--reference:C:\Users\<USER>\.nuget\packages\System.IO.UnmanagedMemoryStream\4.0.1\ref\netstandard1.3\System.IO.UnmanagedMemoryStream.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Linq\4.1.0\ref\netstandard1.6\System.Linq.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Linq.Expressions\4.1.0\ref\netstandard1.6\System.Linq.Expressions.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Linq.Parallel\4.0.1\ref\netstandard1.1\System.Linq.Parallel.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Linq.Queryable\4.0.1\ref\netstandard1.0\System.Linq.Queryable.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Net.Http\4.1.0\ref\netstandard1.3\System.Net.Http.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Net.NameResolution\4.0.0\ref\netstandard1.3\System.Net.NameResolution.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Net.Primitives\4.0.11\ref\netstandard1.3\System.Net.Primitives.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Net.Requests\4.0.11\ref\netstandard1.3\System.Net.Requests.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Net.Security\4.0.0\ref\netstandard1.3\System.Net.Security.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Net.Sockets\4.1.0\ref\netstandard1.3\System.Net.Sockets.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Net.WebHeaderCollection\4.0.1\ref\netstandard1.3\System.Net.WebHeaderCollection.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Net.WebSockets\4.0.0\ref\netstandard1.3\System.Net.WebSockets.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Numerics.Vectors\4.1.1\ref\netstandard1.0\System.Numerics.Vectors.dll
--reference:C:\Users\<USER>\.nuget\packages\System.ObjectModel\4.0.12\ref\netstandard1.3\System.ObjectModel.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Reflection\4.1.0\ref\netstandard1.5\System.Reflection.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Reflection.DispatchProxy\4.0.1\ref\netstandard1.3\System.Reflection.DispatchProxy.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Reflection.Extensions\4.0.1\ref\netstandard1.0\System.Reflection.Extensions.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Reflection.Metadata\1.3.0\lib\netstandard1.1\System.Reflection.Metadata.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Reflection.Primitives\4.0.1\ref\netstandard1.0\System.Reflection.Primitives.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Reflection.TypeExtensions\4.1.0\ref\netstandard1.5\System.Reflection.TypeExtensions.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Resources.Reader\4.0.0\lib\netstandard1.0\System.Resources.Reader.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Resources.ResourceManager\4.0.1\ref\netstandard1.0\System.Resources.ResourceManager.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Runtime\4.1.0\ref\netstandard1.5\System.Runtime.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Runtime.Extensions\4.1.0\ref\netstandard1.5\System.Runtime.Extensions.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Runtime.Handles\4.0.1\ref\netstandard1.3\System.Runtime.Handles.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Runtime.InteropServices\4.1.0\ref\netstandard1.5\System.Runtime.InteropServices.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Runtime.InteropServices.RuntimeInformation\4.0.0\ref\netstandard1.1\System.Runtime.InteropServices.RuntimeInformation.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Runtime.Numerics\4.0.1\ref\netstandard1.1\System.Runtime.Numerics.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Security.Claims\4.0.1\ref\netstandard1.3\System.Security.Claims.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Security.Cryptography.Algorithms\4.2.0\ref\netstandard1.6\System.Security.Cryptography.Algorithms.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Security.Cryptography.Encoding\4.0.0\ref\netstandard1.3\System.Security.Cryptography.Encoding.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Security.Cryptography.Primitives\4.0.0\ref\netstandard1.3\System.Security.Cryptography.Primitives.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Security.Cryptography.X509Certificates\4.1.0\ref\netstandard1.4\System.Security.Cryptography.X509Certificates.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Security.Principal\4.0.1\ref\netstandard1.0\System.Security.Principal.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Security.Principal.Windows\4.0.0\ref\netstandard1.3\System.Security.Principal.Windows.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Text.Encoding\4.0.11\ref\netstandard1.3\System.Text.Encoding.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Text.Encoding.Extensions\4.0.11\ref\netstandard1.3\System.Text.Encoding.Extensions.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Text.Encodings.Web\4.0.0\lib\netstandard1.0\System.Text.Encodings.Web.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Text.RegularExpressions\4.1.0\ref\netstandard1.6\System.Text.RegularExpressions.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Threading\4.0.11\ref\netstandard1.3\System.Threading.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Threading.Tasks\4.0.11\ref\netstandard1.3\System.Threading.Tasks.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Threading.Tasks.Dataflow\4.6.0\lib\netstandard1.1\System.Threading.Tasks.Dataflow.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Threading.Tasks.Extensions\4.0.0\lib\netstandard1.0\System.Threading.Tasks.Extensions.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Threading.Tasks.Parallel\4.0.1\ref\netstandard1.1\System.Threading.Tasks.Parallel.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Threading.Thread\4.0.0\ref\netstandard1.3\System.Threading.Thread.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Threading.ThreadPool\4.0.10\ref\netstandard1.3\System.Threading.ThreadPool.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Threading.Timer\4.0.1\ref\netstandard1.2\System.Threading.Timer.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Xml.ReaderWriter\4.0.11\ref\netstandard1.3\System.Xml.ReaderWriter.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Xml.XDocument\4.0.11\ref\netstandard1.3\System.Xml.XDocument.dll
--resource:"C:\ASP.NET\AspNetCoreHelloWorld\src\AspNetCoreHelloWorld\obj\Debug\netcoreapp1.0\AspNetCoreHelloWorlddotnet-compile.deps.json",AspNetCoreHelloWorld.deps.json
C:\ASP.NET\AspNetCoreHelloWorld\src\AspNetCoreHelloWorld\Program.cs
C:\ASP.NET\AspNetCoreHelloWorld\src\AspNetCoreHelloWorld\Startup.cs
