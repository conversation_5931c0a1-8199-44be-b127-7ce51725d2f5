{"inputs": ["C:\\ASP.NET\\AspNetCoreWebApplication\\src\\AspNetCoreWebApplication\\project.json", "C:\\ASP.NET\\AspNetCoreWebApplication\\src\\AspNetCoreWebApplication\\project.lock.json", "C:\\ASP.NET\\AspNetCoreWebApplication\\src\\AspNetCoreWebApplication\\Program.cs", "C:\\ASP.NET\\AspNetCoreWebApplication\\src\\AspNetCoreWebApplication\\Startup.cs", "C:\\ASP.NET\\AspNetCoreWebApplication\\src\\AspNetCoreWebApplication\\Controllers\\HomeController.cs"], "outputs": ["C:\\ASP.NET\\AspNetCoreWebApplication\\src\\AspNetCoreWebApplication\\bin\\Debug\\netcoreapp1.0\\AspNetCoreWebApplication.dll", "C:\\ASP.NET\\AspNetCoreWebApplication\\src\\AspNetCoreWebApplication\\bin\\Debug\\netcoreapp1.0\\AspNetCoreWebApplication.pdb", "C:\\ASP.NET\\AspNetCoreWebApplication\\src\\AspNetCoreWebApplication\\bin\\Debug\\netcoreapp1.0\\AspNetCoreWebApplication.deps.json", "C:\\ASP.NET\\AspNetCoreWebApplication\\src\\AspNetCoreWebApplication\\bin\\Debug\\netcoreapp1.0\\AspNetCoreWebApplication.runtimeconfig.json"], "buildArguments": {"version-suffix": null}}