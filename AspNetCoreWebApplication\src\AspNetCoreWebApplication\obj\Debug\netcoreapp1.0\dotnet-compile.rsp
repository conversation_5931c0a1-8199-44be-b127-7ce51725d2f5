--temp-output:C:\ASP.NET\AspNetCoreWebApplication\src\AspNetCoreWebApplication\obj\Debug\netcoreapp1.0\
--out:C:\ASP.NET\AspNetCoreWebApplication\src\AspNetCoreWebApplication\bin\Debug\netcoreapp1.0\AspNetCoreWebApplication.dll
--define:DEBUG
--define:TRACE
--define:NETCOREAPP1_0
--suppress-warning:CS1701
--suppress-warning:CS1702
--suppress-warning:CS1705
--optimize:False
--emit-entry-point:True
--output-name:AspNetCoreWebApplication
--file-version:1.0.0.0
--version:1.0.0.0
--informational-version:1.0.0
--target-framework:.NETCoreApp,Version=v1.0
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Antiforgery\1.0.0\lib\netstandard1.3\Microsoft.AspNetCore.Antiforgery.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Authorization\1.0.0\lib\netstandard1.3\Microsoft.AspNetCore.Authorization.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Cors\1.0.0\lib\netstandard1.3\Microsoft.AspNetCore.Cors.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Cryptography.Internal\1.0.0\lib\netstandard1.3\Microsoft.AspNetCore.Cryptography.Internal.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.DataProtection\1.0.0\lib\netstandard1.3\Microsoft.AspNetCore.DataProtection.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.DataProtection.Abstractions\1.0.0\lib\netstandard1.3\Microsoft.AspNetCore.DataProtection.Abstractions.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Diagnostics\1.0.0\lib\netstandard1.3\Microsoft.AspNetCore.Diagnostics.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Diagnostics.Abstractions\1.0.0\lib\netstandard1.0\Microsoft.AspNetCore.Diagnostics.Abstractions.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Hosting\1.0.0\lib\netstandard1.3\Microsoft.AspNetCore.Hosting.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Hosting.Abstractions\1.0.0\lib\netstandard1.3\Microsoft.AspNetCore.Hosting.Abstractions.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Hosting.Server.Abstractions\1.0.0\lib\netstandard1.3\Microsoft.AspNetCore.Hosting.Server.Abstractions.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Html.Abstractions\1.0.0\lib\netstandard1.0\Microsoft.AspNetCore.Html.Abstractions.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Http\1.0.0\lib\netstandard1.3\Microsoft.AspNetCore.Http.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Http.Abstractions\1.0.0\lib\netstandard1.3\Microsoft.AspNetCore.Http.Abstractions.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Http.Extensions\1.0.0\lib\netstandard1.3\Microsoft.AspNetCore.Http.Extensions.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Http.Features\1.0.0\lib\netstandard1.3\Microsoft.AspNetCore.Http.Features.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.HttpOverrides\1.0.0\lib\netstandard1.3\Microsoft.AspNetCore.HttpOverrides.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.JsonPatch\1.0.0\lib\netstandard1.1\Microsoft.AspNetCore.JsonPatch.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Localization\1.0.0\lib\netstandard1.3\Microsoft.AspNetCore.Localization.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Mvc\1.0.0\lib\netstandard1.6\Microsoft.AspNetCore.Mvc.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Mvc.Abstractions\1.0.0\lib\netstandard1.3\Microsoft.AspNetCore.Mvc.Abstractions.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Mvc.ApiExplorer\1.0.0\lib\netstandard1.6\Microsoft.AspNetCore.Mvc.ApiExplorer.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Mvc.Core\1.0.0\lib\netstandard1.6\Microsoft.AspNetCore.Mvc.Core.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Mvc.Cors\1.0.0\lib\netstandard1.6\Microsoft.AspNetCore.Mvc.Cors.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Mvc.DataAnnotations\1.0.0\lib\netstandard1.6\Microsoft.AspNetCore.Mvc.DataAnnotations.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Mvc.Formatters.Json\1.0.0\lib\netstandard1.6\Microsoft.AspNetCore.Mvc.Formatters.Json.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Mvc.Localization\1.0.0\lib\netstandard1.6\Microsoft.AspNetCore.Mvc.Localization.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Mvc.Razor\1.0.0\lib\netstandard1.6\Microsoft.AspNetCore.Mvc.Razor.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Mvc.Razor.Host\1.0.0\lib\netstandard1.6\Microsoft.AspNetCore.Mvc.Razor.Host.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Mvc.TagHelpers\1.0.0\lib\netstandard1.6\Microsoft.AspNetCore.Mvc.TagHelpers.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Mvc.ViewFeatures\1.0.0\lib\netstandard1.6\Microsoft.AspNetCore.Mvc.ViewFeatures.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Razor\1.0.0\lib\netstandard1.3\Microsoft.AspNetCore.Razor.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Razor.Runtime\1.0.0\lib\netstandard1.5\Microsoft.AspNetCore.Razor.Runtime.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Razor.Tools\1.0.0-preview2-final\lib\netcoreapp1.0\dotnet-razor-tooling.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Routing\1.0.0\lib\netstandard1.3\Microsoft.AspNetCore.Routing.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Routing.Abstractions\1.0.0\lib\netstandard1.3\Microsoft.AspNetCore.Routing.Abstractions.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Server.IISIntegration\1.0.0\lib\netstandard1.3\Microsoft.AspNetCore.Server.IISIntegration.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.Server.Kestrel\1.0.0\lib\netstandard1.3\Microsoft.AspNetCore.Server.Kestrel.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.StaticFiles\1.0.0\lib\netstandard1.3\Microsoft.AspNetCore.StaticFiles.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.AspNetCore.WebUtilities\1.0.0\lib\netstandard1.3\Microsoft.AspNetCore.WebUtilities.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.CodeAnalysis.Common\1.3.0\lib\netstandard1.3\Microsoft.CodeAnalysis.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.CodeAnalysis.CSharp\1.3.0\lib\netstandard1.3\Microsoft.CodeAnalysis.CSharp.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.CSharp\4.0.1\ref\netstandard1.0\Microsoft.CSharp.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.DotNet.Cli.Utils\1.0.0-preview2-003121\lib\netstandard1.6\Microsoft.DotNet.Cli.Utils.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.DotNet.InternalAbstractions\1.0.0\lib\netstandard1.3\Microsoft.DotNet.InternalAbstractions.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.DotNet.ProjectModel\1.0.0-rc3-003121\lib\netstandard1.6\Microsoft.DotNet.ProjectModel.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.DotNet.ProjectModel.Loader\1.0.0-preview2-003121\lib\netstandard1.6\Microsoft.DotNet.ProjectModel.Loader.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.Caching.Abstractions\1.0.0\lib\netstandard1.0\Microsoft.Extensions.Caching.Abstractions.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.Caching.Memory\1.0.0\lib\netstandard1.3\Microsoft.Extensions.Caching.Memory.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.CommandLineUtils\1.0.0\lib\netstandard1.3\Microsoft.Extensions.CommandLineUtils.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.Configuration\1.0.0\lib\netstandard1.1\Microsoft.Extensions.Configuration.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.Configuration.Abstractions\1.0.0\lib\netstandard1.0\Microsoft.Extensions.Configuration.Abstractions.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.Configuration.Binder\1.0.0\lib\netstandard1.1\Microsoft.Extensions.Configuration.Binder.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.Configuration.EnvironmentVariables\1.0.0\lib\netstandard1.3\Microsoft.Extensions.Configuration.EnvironmentVariables.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.Configuration.FileExtensions\1.0.0\lib\netstandard1.3\Microsoft.Extensions.Configuration.FileExtensions.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.Configuration.Json\1.0.0\lib\netstandard1.3\Microsoft.Extensions.Configuration.Json.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.DependencyInjection\1.0.0\lib\netstandard1.1\Microsoft.Extensions.DependencyInjection.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.DependencyInjection.Abstractions\1.0.0\lib\netstandard1.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.DependencyModel\1.0.0\lib\netstandard1.6\Microsoft.Extensions.DependencyModel.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.FileProviders.Abstractions\1.0.0\lib\netstandard1.0\Microsoft.Extensions.FileProviders.Abstractions.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.FileProviders.Composite\1.0.0\lib\netstandard1.0\Microsoft.Extensions.FileProviders.Composite.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.FileProviders.Physical\1.0.0\lib\netstandard1.3\Microsoft.Extensions.FileProviders.Physical.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.FileSystemGlobbing\1.0.0\lib\netstandard1.3\Microsoft.Extensions.FileSystemGlobbing.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.Globalization.CultureInfoCache\1.0.0\lib\netstandard1.1\Microsoft.Extensions.Globalization.CultureInfoCache.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.Localization\1.0.0\lib\netstandard1.3\Microsoft.Extensions.Localization.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.Localization.Abstractions\1.0.0\lib\netstandard1.0\Microsoft.Extensions.Localization.Abstractions.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.Logging\1.0.0\lib\netstandard1.1\Microsoft.Extensions.Logging.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.Logging.Abstractions\1.0.0\lib\netstandard1.1\Microsoft.Extensions.Logging.Abstractions.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.Logging.Console\1.0.0\lib\netstandard1.3\Microsoft.Extensions.Logging.Console.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.Logging.Debug\1.0.0\lib\netstandard1.3\Microsoft.Extensions.Logging.Debug.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.ObjectPool\1.0.0\lib\netstandard1.3\Microsoft.Extensions.ObjectPool.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.Options\1.0.0\lib\netstandard1.0\Microsoft.Extensions.Options.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.Options.ConfigurationExtensions\1.0.0\lib\netstandard1.1\Microsoft.Extensions.Options.ConfigurationExtensions.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.PlatformAbstractions\1.0.0\lib\netstandard1.3\Microsoft.Extensions.PlatformAbstractions.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.Primitives\1.0.0\lib\netstandard1.0\Microsoft.Extensions.Primitives.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.Extensions.WebEncoders\1.0.0\lib\netstandard1.0\Microsoft.Extensions.WebEncoders.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.Net.Http.Headers\1.0.0\lib\netstandard1.1\Microsoft.Net.Http.Headers.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.VisualBasic\10.0.1\ref\netstandard1.1\Microsoft.VisualBasic.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.VisualStudio.Web.BrowserLink.Loader\14.0.0\lib\netstandard1.5\Microsoft.VisualStudio.Web.BrowserLink.Loader.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.Win32.Primitives\4.0.1\ref\netstandard1.3\Microsoft.Win32.Primitives.dll
--reference:C:\Users\<USER>\.nuget\packages\Microsoft.Win32.Registry\4.0.0\ref\netstandard1.3\Microsoft.Win32.Registry.dll
--reference:C:\Users\<USER>\.nuget\packages\Newtonsoft.Json\9.0.1\lib\netstandard1.0\Newtonsoft.Json.dll
--reference:C:\Users\<USER>\.nuget\packages\NuGet.Common\3.5.0-beta2-1484\lib\netstandard1.3\NuGet.Common.dll
--reference:C:\Users\<USER>\.nuget\packages\NuGet.Configuration\3.5.0-beta2-1484\lib\netstandard1.3\NuGet.Configuration.dll
--reference:C:\Users\<USER>\.nuget\packages\NuGet.DependencyResolver.Core\3.5.0-beta2-1484\lib\netstandard1.3\NuGet.DependencyResolver.Core.dll
--reference:C:\Users\<USER>\.nuget\packages\NuGet.Frameworks\3.5.0-beta2-1484\lib\netstandard1.3\NuGet.Frameworks.dll
--reference:C:\Users\<USER>\.nuget\packages\NuGet.LibraryModel\3.5.0-beta2-1484\lib\netstandard1.3\NuGet.LibraryModel.dll
--reference:C:\Users\<USER>\.nuget\packages\NuGet.Packaging\3.5.0-beta2-1484\lib\netstandard1.3\NuGet.Packaging.dll
--reference:C:\Users\<USER>\.nuget\packages\NuGet.Packaging.Core\3.5.0-beta2-1484\lib\netstandard1.3\NuGet.Packaging.Core.dll
--reference:C:\Users\<USER>\.nuget\packages\NuGet.Packaging.Core.Types\3.5.0-beta2-1484\lib\netstandard1.3\NuGet.Packaging.Core.Types.dll
--reference:C:\Users\<USER>\.nuget\packages\NuGet.ProjectModel\3.5.0-beta2-1484\lib\netstandard1.3\NuGet.ProjectModel.dll
--reference:C:\Users\<USER>\.nuget\packages\NuGet.Protocol.Core.Types\3.5.0-beta2-1484\lib\netstandard1.3\NuGet.Protocol.Core.Types.dll
--reference:C:\Users\<USER>\.nuget\packages\NuGet.Protocol.Core.v3\3.5.0-beta2-1484\lib\netstandard1.3\NuGet.Protocol.Core.v3.dll
--reference:C:\Users\<USER>\.nuget\packages\NuGet.Repositories\3.5.0-beta2-1484\lib\netstandard1.3\NuGet.Repositories.dll
--reference:C:\Users\<USER>\.nuget\packages\NuGet.RuntimeModel\3.5.0-beta2-1484\lib\netstandard1.3\NuGet.RuntimeModel.dll
--reference:C:\Users\<USER>\.nuget\packages\NuGet.Versioning\3.5.0-beta2-1484\lib\netstandard1.0\NuGet.Versioning.dll
--reference:C:\Users\<USER>\.nuget\packages\System.AppContext\4.1.0\ref\netstandard1.6\System.AppContext.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Buffers\4.0.0\lib\netstandard1.1\System.Buffers.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Collections\4.0.11\ref\netstandard1.3\System.Collections.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Collections.Concurrent\4.0.12\ref\netstandard1.3\System.Collections.Concurrent.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Collections.Immutable\1.2.0\lib\netstandard1.0\System.Collections.Immutable.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Collections.NonGeneric\4.0.1\ref\netstandard1.3\System.Collections.NonGeneric.dll
--reference:C:\Users\<USER>\.nuget\packages\System.ComponentModel\4.0.1\ref\netstandard1.0\System.ComponentModel.dll
--reference:C:\Users\<USER>\.nuget\packages\System.ComponentModel.Annotations\4.1.0\ref\netstandard1.4\System.ComponentModel.Annotations.dll
--reference:C:\Users\<USER>\.nuget\packages\System.ComponentModel.Primitives\4.1.0\ref\netstandard1.0\System.ComponentModel.Primitives.dll
--reference:C:\Users\<USER>\.nuget\packages\System.ComponentModel.TypeConverter\4.1.0\ref\netstandard1.5\System.ComponentModel.TypeConverter.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Console\4.0.0\ref\netstandard1.3\System.Console.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Diagnostics.Contracts\4.0.1\ref\netstandard1.0\System.Diagnostics.Contracts.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Diagnostics.Debug\4.0.11\ref\netstandard1.3\System.Diagnostics.Debug.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Diagnostics.DiagnosticSource\4.0.0\lib\netstandard1.3\System.Diagnostics.DiagnosticSource.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Diagnostics.Process\4.1.0\ref\netstandard1.4\System.Diagnostics.Process.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Diagnostics.StackTrace\4.0.1\ref\netstandard1.3\System.Diagnostics.StackTrace.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Diagnostics.Tools\4.0.1\ref\netstandard1.0\System.Diagnostics.Tools.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Diagnostics.Tracing\4.1.0\ref\netstandard1.5\System.Diagnostics.Tracing.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Dynamic.Runtime\4.0.11\ref\netstandard1.3\System.Dynamic.Runtime.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Globalization\4.0.11\ref\netstandard1.3\System.Globalization.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Globalization.Calendars\4.0.1\ref\netstandard1.3\System.Globalization.Calendars.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Globalization.Extensions\4.0.1\ref\netstandard1.3\System.Globalization.Extensions.dll
--reference:C:\Users\<USER>\.nuget\packages\System.IO\4.1.0\ref\netstandard1.5\System.IO.dll
--reference:C:\Users\<USER>\.nuget\packages\System.IO.Compression\4.1.0\ref\netstandard1.3\System.IO.Compression.dll
--reference:C:\Users\<USER>\.nuget\packages\System.IO.Compression.ZipFile\4.0.1\ref\netstandard1.3\System.IO.Compression.ZipFile.dll
--reference:C:\Users\<USER>\.nuget\packages\System.IO.FileSystem\4.0.1\ref\netstandard1.3\System.IO.FileSystem.dll
--reference:C:\Users\<USER>\.nuget\packages\System.IO.FileSystem.Primitives\4.0.1\ref\netstandard1.3\System.IO.FileSystem.Primitives.dll
--reference:C:\Users\<USER>\.nuget\packages\System.IO.FileSystem.Watcher\4.0.0\ref\netstandard1.3\System.IO.FileSystem.Watcher.dll
--reference:C:\Users\<USER>\.nuget\packages\System.IO.MemoryMappedFiles\4.0.0\ref\netstandard1.3\System.IO.MemoryMappedFiles.dll
--reference:C:\Users\<USER>\.nuget\packages\System.IO.UnmanagedMemoryStream\4.0.1\ref\netstandard1.3\System.IO.UnmanagedMemoryStream.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Linq\4.1.0\ref\netstandard1.6\System.Linq.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Linq.Expressions\4.1.0\ref\netstandard1.6\System.Linq.Expressions.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Linq.Parallel\4.0.1\ref\netstandard1.1\System.Linq.Parallel.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Linq.Queryable\4.0.1\ref\netstandard1.0\System.Linq.Queryable.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Net.Http\4.1.0\ref\netstandard1.3\System.Net.Http.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Net.NameResolution\4.0.0\ref\netstandard1.3\System.Net.NameResolution.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Net.Primitives\4.0.11\ref\netstandard1.3\System.Net.Primitives.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Net.Requests\4.0.11\ref\netstandard1.3\System.Net.Requests.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Net.Security\4.0.0\ref\netstandard1.3\System.Net.Security.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Net.Sockets\4.1.0\ref\netstandard1.3\System.Net.Sockets.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Net.WebHeaderCollection\4.0.1\ref\netstandard1.3\System.Net.WebHeaderCollection.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Net.WebSockets\4.0.0\ref\netstandard1.3\System.Net.WebSockets.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Numerics.Vectors\4.1.1\ref\netstandard1.0\System.Numerics.Vectors.dll
--reference:C:\Users\<USER>\.nuget\packages\System.ObjectModel\4.0.12\ref\netstandard1.3\System.ObjectModel.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Reflection\4.1.0\ref\netstandard1.5\System.Reflection.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Reflection.DispatchProxy\4.0.1\ref\netstandard1.3\System.Reflection.DispatchProxy.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Reflection.Extensions\4.0.1\ref\netstandard1.0\System.Reflection.Extensions.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Reflection.Metadata\1.3.0\lib\netstandard1.1\System.Reflection.Metadata.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Reflection.Primitives\4.0.1\ref\netstandard1.0\System.Reflection.Primitives.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Reflection.TypeExtensions\4.1.0\ref\netstandard1.5\System.Reflection.TypeExtensions.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Resources.Reader\4.0.0\lib\netstandard1.0\System.Resources.Reader.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Resources.ResourceManager\4.0.1\ref\netstandard1.0\System.Resources.ResourceManager.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Runtime\4.1.0\ref\netstandard1.5\System.Runtime.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Runtime.Extensions\4.1.0\ref\netstandard1.5\System.Runtime.Extensions.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Runtime.Handles\4.0.1\ref\netstandard1.3\System.Runtime.Handles.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Runtime.InteropServices\4.1.0\ref\netstandard1.5\System.Runtime.InteropServices.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Runtime.InteropServices.RuntimeInformation\4.0.0\ref\netstandard1.1\System.Runtime.InteropServices.RuntimeInformation.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Runtime.Loader\4.0.0\ref\netstandard1.5\System.Runtime.Loader.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Runtime.Numerics\4.0.1\ref\netstandard1.1\System.Runtime.Numerics.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Runtime.Serialization.Primitives\4.1.1\ref\netstandard1.3\System.Runtime.Serialization.Primitives.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Security.Claims\4.0.1\ref\netstandard1.3\System.Security.Claims.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Security.Cryptography.Algorithms\4.2.0\ref\netstandard1.6\System.Security.Cryptography.Algorithms.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Security.Cryptography.Encoding\4.0.0\ref\netstandard1.3\System.Security.Cryptography.Encoding.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Security.Cryptography.Primitives\4.0.0\ref\netstandard1.3\System.Security.Cryptography.Primitives.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Security.Cryptography.X509Certificates\4.1.0\ref\netstandard1.4\System.Security.Cryptography.X509Certificates.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Security.Principal\4.0.1\ref\netstandard1.0\System.Security.Principal.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Security.Principal.Windows\4.0.0\ref\netstandard1.3\System.Security.Principal.Windows.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Text.Encoding\4.0.11\ref\netstandard1.3\System.Text.Encoding.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Text.Encoding.Extensions\4.0.11\ref\netstandard1.3\System.Text.Encoding.Extensions.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Text.Encodings.Web\4.0.0\lib\netstandard1.0\System.Text.Encodings.Web.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Text.RegularExpressions\4.1.0\ref\netstandard1.6\System.Text.RegularExpressions.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Threading\4.0.11\ref\netstandard1.3\System.Threading.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Threading.Tasks\4.0.11\ref\netstandard1.3\System.Threading.Tasks.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Threading.Tasks.Dataflow\4.6.0\lib\netstandard1.1\System.Threading.Tasks.Dataflow.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Threading.Tasks.Extensions\4.0.0\lib\netstandard1.0\System.Threading.Tasks.Extensions.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Threading.Tasks.Parallel\4.0.1\ref\netstandard1.1\System.Threading.Tasks.Parallel.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Threading.Thread\4.0.0\ref\netstandard1.3\System.Threading.Thread.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Threading.ThreadPool\4.0.10\ref\netstandard1.3\System.Threading.ThreadPool.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Threading.Timer\4.0.1\ref\netstandard1.2\System.Threading.Timer.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Xml.ReaderWriter\4.0.11\ref\netstandard1.3\System.Xml.ReaderWriter.dll
--reference:C:\Users\<USER>\.nuget\packages\System.Xml.XDocument\4.0.11\ref\netstandard1.3\System.Xml.XDocument.dll
--resource:"C:\ASP.NET\AspNetCoreWebApplication\src\AspNetCoreWebApplication\obj\Debug\netcoreapp1.0\AspNetCoreWebApplicationdotnet-compile.deps.json",AspNetCoreWebApplication.deps.json
C:\ASP.NET\AspNetCoreWebApplication\src\AspNetCoreWebApplication\Program.cs
C:\ASP.NET\AspNetCoreWebApplication\src\AspNetCoreWebApplication\Startup.cs
C:\ASP.NET\AspNetCoreWebApplication\src\AspNetCoreWebApplication\Controllers\HomeController.cs
